import { NextApiRequest, NextApiResponse } from 'next';
import { KYCService } from '../../../../lib/services/kycService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get user from session/auth
    const userId = parseInt(req.headers['x-user-id'] as string);
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check KYC completion status
    const isCompleted = await KYCService.checkKYCCompletion(userId);
    const kycData = await KYCService.getKYCByUserId(userId);

    const response = {
      isCompleted,
      hasKYC: !!kycData,
      status: kycData?.kyc_status || null,
      completedAt: kycData?.kyc_completed_at || null,
      certificateRequested: kycData?.certificate_requested || false,
      certificateGenerated: !!kycData?.certificate_generated_at,
      certificateSent: !!kycData?.certificate_sent_at
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('Error checking KYC status:', error);
    return res.status(500).json({ error: 'Failed to check KYC status' });
  }
}
