import bcrypt from 'bcryptjs'

// Password validation rules
export interface PasswordValidation {
  isValid: boolean
  errors: string[]
  strength: 'weak' | 'medium' | 'strong'
  score: number
}

// Validate password strength and requirements
export const validatePassword = (password: string): PasswordValidation => {
  const errors: string[] = []
  let score = 0

  // Length check
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  } else if (password.length >= 12) {
    score += 2
  } else {
    score += 1
  }

  // Uppercase letter check
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  } else {
    score += 1
  }

  // Lowercase letter check
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  } else {
    score += 1
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  } else {
    score += 1
  }

  // Special character check
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character')
  } else {
    score += 1
  }

  // Common password patterns check
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /admin/i,
    /letmein/i,
    /welcome/i
  ]

  if (commonPatterns.some(pattern => pattern.test(password))) {
    errors.push('Password contains common patterns and is not secure')
    score -= 2
  }

  // Sequential characters check
  if (/(.)\1{2,}/.test(password)) {
    errors.push('Password should not contain repeated characters')
    score -= 1
  }

  // Determine strength
  let strength: 'weak' | 'medium' | 'strong'
  if (score >= 5 && errors.length === 0) {
    strength = 'strong'
  } else if (score >= 3 && errors.length <= 1) {
    strength = 'medium'
  } else {
    strength = 'weak'
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength,
    score: Math.max(0, score)
  }
}

// Hash password with bcrypt
export const hashPassword = async (password: string): Promise<string> => {
  try {
    const saltRounds = 12 // High security level
    const hashedPassword = await bcrypt.hash(password, saltRounds)
    return hashedPassword
  } catch (error) {
    console.error('Error hashing password:', error)
    throw new Error('Failed to hash password')
  }
}

// Verify password against hash
export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  try {
    const isValid = await bcrypt.compare(password, hash)
    return isValid
  } catch (error) {
    console.error('Error verifying password:', error)
    return false
  }
}

// Generate secure random password
export const generateSecurePassword = (length: number = 16): string => {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const numbers = '0123456789'
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?'
  
  const allChars = uppercase + lowercase + numbers + symbols
  
  let password = ''
  
  // Ensure at least one character from each category
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += symbols[Math.floor(Math.random() * symbols.length)]
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

// Generate password reset token
export const generateResetToken = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let token = ''
  for (let i = 0; i < 32; i++) {
    token += chars[Math.floor(Math.random() * chars.length)]
  }
  return token
}

// Check if password has been compromised (basic check)
export const checkPasswordCompromised = async (password: string): Promise<boolean> => {
  // In a real implementation, you might check against Have I Been Pwned API
  // For now, just check against common passwords
  const commonPasswords = [
    '123456', 'password', '123456789', '12345678', '12345',
    '1234567', '1234567890', 'qwerty', 'abc123', 'million2',
    '000000', '1234', 'iloveyou', 'aaron431', 'password1',
    'qqww1122', '123', 'omgpop', '123321', '654321'
  ]
  
  return commonPasswords.includes(password.toLowerCase())
}

// Password strength meter component data
export const getPasswordStrengthMeter = (password: string) => {
  const validation = validatePassword(password)
  
  const strengthColors = {
    weak: 'bg-red-500',
    medium: 'bg-yellow-500',
    strong: 'bg-green-500'
  }
  
  const strengthTexts = {
    weak: 'Weak',
    medium: 'Medium',
    strong: 'Strong'
  }
  
  const progressPercentage = Math.min((validation.score / 6) * 100, 100)
  
  return {
    strength: validation.strength,
    color: strengthColors[validation.strength],
    text: strengthTexts[validation.strength],
    percentage: progressPercentage,
    errors: validation.errors,
    isValid: validation.isValid
  }
}

// Password history check (to prevent reuse)
export const checkPasswordHistory = async (
  userId: number, 
  newPassword: string, 
  historyLimit: number = 5
): Promise<boolean> => {
  try {
    // In a real implementation, you would:
    // 1. Fetch the last N password hashes from password_history table
    // 2. Compare the new password against each hash
    // 3. Return true if password was used before
    
    // For now, just return false (password not in history)
    console.log(`Checking password history for user ${userId} (limit: ${historyLimit})`)
    return false
  } catch (error) {
    console.error('Error checking password history:', error)
    return false
  }
}

// Store password in history
export const storePasswordHistory = async (
  userId: number, 
  passwordHash: string
): Promise<void> => {
  try {
    // In a real implementation, you would:
    // 1. Insert the new password hash into password_history table
    // 2. Clean up old entries beyond the history limit
    
    console.log(`Storing password history for user ${userId}`)
  } catch (error) {
    console.error('Error storing password history:', error)
  }
}

// Password expiry check
export const isPasswordExpired = (lastChanged: Date, expiryDays: number = 90): boolean => {
  const now = new Date()
  const expiryDate = new Date(lastChanged)
  expiryDate.setDate(expiryDate.getDate() + expiryDays)
  
  return now > expiryDate
}

// Calculate days until password expires
export const getDaysUntilExpiry = (lastChanged: Date, expiryDays: number = 90): number => {
  const now = new Date()
  const expiryDate = new Date(lastChanged)
  expiryDate.setDate(expiryDate.getDate() + expiryDays)
  
  const diffTime = expiryDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return Math.max(0, diffDays)
}
