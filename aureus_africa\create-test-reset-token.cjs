const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTestResetLink() {
  console.log('🔗 Creating test reset token...');
  
  const testEmail = '<EMAIL>';
  const resetToken = require('crypto').randomBytes(32).toString('hex');
  const resetExpires = new Date(Date.now() + 24 * 60 * 60 * 1000);
  
  const { error } = await supabase
    .from('users')
    .update({
      reset_token: resetToken,
      reset_token_expires: resetExpires.toISOString()
    })
    .eq('email', testEmail);
  
  if (error) {
    console.error('❌ Error:', error);
  } else {
    console.log('✅ Test reset link created:');
    console.log('🔗 http://localhost:8006/reset-password?token=' + resetToken);
    console.log('');
    console.log('📧 This link is for user: ' + testEmail);
    console.log('⏰ Link expires in 24 hours');
  }
}

createTestResetLink().catch(console.error);
