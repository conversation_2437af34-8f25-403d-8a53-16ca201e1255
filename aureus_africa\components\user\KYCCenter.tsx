import React, { useState, useEffect } from 'react';
import { KYCStatusDashboard } from './KYCStatusDashboard';
import { KYCDocumentUpload } from './KYCDocumentUpload';
import { supabase } from '../../lib/supabase';

interface KYCCenterProps {
  userId: number;
  className?: string;
}

interface KYCAuditLog {
  id: string;
  action: string;
  field_changed?: string;
  performed_at: string;
  performed_by_username?: string;
  ip_address?: string;
}

export const KYCCenter: React.FC<KYCCenterProps> = ({ userId, className = '' }) => {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [auditLogs, setAuditLogs] = useState<KYCAuditLog[]>([]);
  const [showAuditLogs, setShowAuditLogs] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    loadAuditLogs();
  }, [userId, refreshTrigger]);

  const loadAuditLogs = async () => {
    try {
      const { data, error } = await supabase
        .from('kyc_audit_log')
        .select('*')
        .eq('user_id', userId)
        .order('performed_at', { ascending: false })
        .limit(10);

      if (!error && data) {
        setAuditLogs(data);
      }
    } catch (error) {
      console.error('Error loading audit logs:', error);
    }
  };

  const handleKYCComplete = (data: any) => {
    console.log('KYC completed:', data);
    setShowUploadModal(false);
    setRefreshTrigger(prev => prev + 1);
    
    // Show success message
    alert('KYC information submitted successfully! Your verification will be reviewed within 24-48 hours.');
  };

  const handleStartKYC = () => {
    setShowUploadModal(true);
  };

  const handleUpdateKYC = () => {
    setShowUploadModal(true);
  };

  const formatAuditAction = (action: string) => {
    switch (action) {
      case 'created': return '📝 KYC Created';
      case 'updated': return '✏️ KYC Updated';
      case 'viewed': return '👁️ KYC Viewed';
      case 'certificate_requested': return '📜 Certificate Requested';
      case 'approved': return '✅ KYC Approved';
      case 'rejected': return '❌ KYC Rejected';
      default: return `🔄 ${action}`;
    }
  };

  return (
    <div className={className}>
      {/* Main KYC Status Dashboard */}
      <KYCStatusDashboard
        userId={userId}
        onStartKYC={handleStartKYC}
        onUpdateKYC={handleUpdateKYC}
      />

      {/* KYC Information Panel */}
      <div style={{
        backgroundColor: 'rgba(31, 41, 55, 0.9)',
        borderRadius: '12px',
        padding: '24px',
        border: '1px solid #374151',
        marginBottom: '24px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
            📚 KYC Information
          </h3>
          
          <button
            onClick={() => setShowAuditLogs(!showAuditLogs)}
            style={{
              padding: '8px 16px',
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              border: '1px solid #3b82f6',
              borderRadius: '8px',
              color: '#60a5fa',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            {showAuditLogs ? 'Hide' : 'Show'} Activity Log
          </button>
        </div>

        {/* KYC Requirements */}
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '20px',
          marginBottom: '20px'
        }}>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
            📋 KYC Requirements
          </h4>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '16px'
          }}>
            <div style={{
              padding: '16px',
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              border: '1px solid rgba(16, 185, 129, 0.3)',
              borderRadius: '8px'
            }}>
              <div style={{ color: '#10b981', fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                🆔 Identity Verification
              </div>
              <ul style={{ color: '#d1d5db', fontSize: '14px', margin: 0, paddingLeft: '20px' }}>
                <li>Government-issued ID or Passport</li>
                <li>Clear, readable photo</li>
                <li>Valid and not expired</li>
              </ul>
            </div>

            <div style={{
              padding: '16px',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              border: '1px solid rgba(59, 130, 246, 0.3)',
              borderRadius: '8px'
            }}>
              <div style={{ color: '#60a5fa', fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                🤳 Selfie Verification
              </div>
              <ul style={{ color: '#d1d5db', fontSize: '14px', margin: 0, paddingLeft: '20px' }}>
                <li>Hold ID next to your face</li>
                <li>Both face and ID clearly visible</li>
                <li>Good lighting, no shadows</li>
              </ul>
            </div>

            <div style={{
              padding: '16px',
              backgroundColor: 'rgba(245, 158, 11, 0.1)',
              border: '1px solid rgba(245, 158, 11, 0.3)',
              borderRadius: '8px'
            }}>
              <div style={{ color: '#f59e0b', fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                🏠 Address Verification
              </div>
              <ul style={{ color: '#d1d5db', fontSize: '14px', margin: 0, paddingLeft: '20px' }}>
                <li>Utility bill or bank statement</li>
                <li>Dated within last 3 months</li>
                <li>Shows your full address</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Processing Timeline */}
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '20px',
          marginBottom: showAuditLogs ? '20px' : '0'
        }}>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
            ⏰ Processing Timeline
          </h4>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {[
              { step: 'Submit KYC', time: 'Immediate', icon: '📝', color: '#10b981' },
              { step: 'Document Review', time: '24-48 hours', icon: '🔍', color: '#f59e0b' },
              { step: 'Verification Complete', time: '48-72 hours', icon: '✅', color: '#3b82f6' },
              { step: 'Certificate Generation', time: '72-96 hours', icon: '📜', color: '#8b5cf6' }
            ].map((item, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                padding: '12px',
                backgroundColor: 'rgba(75, 85, 99, 0.3)',
                borderRadius: '6px'
              }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  borderRadius: '50%',
                  backgroundColor: `${item.color}20`,
                  border: `2px solid ${item.color}`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '14px'
                }}>
                  {item.icon}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{ color: '#f3f4f6', fontSize: '14px', fontWeight: '600' }}>
                    {item.step}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>
                    {item.time}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Audit Logs */}
        {showAuditLogs && auditLogs.length > 0 && (
          <div style={{
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            borderRadius: '8px',
            padding: '20px'
          }}>
            <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
              📊 Activity Log
            </h4>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {auditLogs.map(log => (
                <div key={log.id} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '12px',
                  backgroundColor: 'rgba(75, 85, 99, 0.3)',
                  borderRadius: '6px'
                }}>
                  <div>
                    <div style={{ color: '#f3f4f6', fontSize: '14px', fontWeight: '500' }}>
                      {formatAuditAction(log.action)}
                    </div>
                    {log.field_changed && (
                      <div style={{ color: '#9ca3af', fontSize: '12px' }}>
                        Field: {log.field_changed}
                      </div>
                    )}
                    {log.performed_by_username && (
                      <div style={{ color: '#9ca3af', fontSize: '12px' }}>
                        By: {log.performed_by_username}
                      </div>
                    )}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px', textAlign: 'right' }}>
                    {new Date(log.performed_at).toLocaleDateString()}
                    <br />
                    {new Date(log.performed_at).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Security & Privacy Notice */}
      <div style={{
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        border: '1px solid rgba(16, 185, 129, 0.3)',
        borderRadius: '12px',
        padding: '20px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
          <span style={{ fontSize: '24px' }}>🔒</span>
          <h3 style={{ color: '#10b981', fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
            Security & Privacy
          </h3>
        </div>
        
        <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
          <p style={{ margin: '0 0 12px 0' }}>
            Your personal information is protected with bank-level security. All data is encrypted 
            and stored securely in compliance with international privacy regulations including GDPR and POPIA.
          </p>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '12px',
            marginTop: '16px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ color: '#10b981' }}>🔐</span>
              <span>256-bit SSL Encryption</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ color: '#10b981' }}>🛡️</span>
              <span>GDPR Compliant</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ color: '#10b981' }}>📋</span>
              <span>POPIA Compliant</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ color: '#10b981' }}>🔍</span>
              <span>Full Audit Trail</span>
            </div>
          </div>
        </div>
      </div>

      {/* KYC Upload Modal */}
      {showUploadModal && (
        <KYCDocumentUpload
          userId={userId}
          onComplete={handleKYCComplete}
          onCancel={() => setShowUploadModal(false)}
        />
      )}
    </div>
  );
};
