/**
 * Password Reset API Endpoint
 * Handles forgot password and password reset functionality using PIN-based verification
 */

import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client with service role
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Resend client
const RESEND_API_KEY = process.env.RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || 'Aureus Alliance';
const resend = RESEND_API_KEY ? new Resend(RESEND_API_KEY) : null;

// Configuration
const VERIFICATION_CODE_LENGTH = 6;
const VERIFICATION_EXPIRY_MINUTES = 15;
const MAX_VERIFICATION_ATTEMPTS = 3;

export default async function handler(req, res) {
  try {
    switch (req.method) {
      case 'POST':
        // Check if this is a PIN verification request
        if (req.body.pin && req.body.email) {
          return await handlePinVerification(req, res);
        }
        // Otherwise, it's a password reset request
        return await handlePasswordResetRequest(req, res);
      case 'PUT':
        return await handlePasswordReset(req, res);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Password reset API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle password reset request (forgot password) - Send PIN code
 */
async function handlePasswordResetRequest(req, res) {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email address is required'
      });
    }

    console.log(`🔄 Password reset PIN request for: ${email}`);

    // Check if user exists
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, username, full_name')
      .eq('email', email)
      .single();

    if (userError || !user) {
      // Don't reveal if email exists or not (security best practice)
      console.log(`⚠️ Password reset requested for non-existent email: ${email}`);
      return res.status(200).json({
        success: true,
        message: 'If an account with this email exists, a verification code has been sent. Please check your email and enter the 6-digit code.'
      });
    }

    // Generate 6-digit PIN code
    const pinCode = generateSecurePin();
    const pinHash = await hashPin(pinCode);

    // Calculate expiry time
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + VERIFICATION_EXPIRY_MINUTES);

    // Store verification code in users table using existing reset columns
    // Format: "PIN:{hash}:{attempts}"
    const resetData = `PIN:${pinHash}:0`;
    const { error: dbError } = await supabase
      .from('users')
      .update({
        reset_token: resetData,
        reset_token_expires: expiresAt.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    if (dbError) {
      console.error('❌ Error storing verification code:', dbError);
      return res.status(500).json({
        success: false,
        message: 'Failed to generate verification code',
        error: dbError.message
      });
    }

    // Send PIN code email
    if (resend) {
      try {
        const userName = user.full_name || user.username || 'User';
        const emailContent = generatePasswordResetEmailContent(pinCode, userName, VERIFICATION_EXPIRY_MINUTES);

        const emailResult = await resend.emails.send({
          from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
          to: [user.email],
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          tags: [
            { name: 'category', value: 'password_reset' },
            { name: 'purpose', value: 'password_reset' }
          ]
        });

        console.log(`✅ Password reset PIN sent to: ${email}`, emailResult);
      } catch (emailError) {
        console.error('❌ Password reset email error:', emailError);
        // Clean up PIN data if email failed
        await supabase
          .from('users')
          .update({
            reset_token: null,
            reset_token_expires: null
          })
          .eq('id', user.id);

        return res.status(500).json({
          success: false,
          message: 'Failed to send verification email'
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: 'If an account with this email exists, a verification code has been sent. Please check your email and enter the 6-digit code.'
    });

  } catch (error) {
    console.error('❌ Password reset request error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to process password reset request',
      error: error.message
    });
  }
}

/**
 * Handle PIN verification for password reset
 */
async function handlePinVerification(req, res) {
  try {
    const { email, pin } = req.body;

    if (!email || !pin) {
      return res.status(400).json({
        success: false,
        message: 'Email and PIN code are required'
      });
    }

    console.log(`🔍 Verifying PIN for password reset: ${email}`);

    // Find user with PIN data
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, username, reset_token, reset_token_expires')
      .eq('email', email)
      .single();

    if (userError || !user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email or PIN code'
      });
    }

    // Check if PIN data exists and is not expired
    if (!user.reset_token || !user.reset_token_expires || !user.reset_token.startsWith('PIN:')) {
      return res.status(400).json({
        success: false,
        message: 'No valid verification code found. Please request a new code.'
      });
    }

    // Parse PIN data: "PIN:{hash}:{attempts}"
    const pinParts = user.reset_token.split(':');
    if (pinParts.length !== 3 || pinParts[0] !== 'PIN') {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification code format. Please request a new code.'
      });
    }

    const pinHash = pinParts[1];
    const attempts = parseInt(pinParts[2]) || 0;

    // Check if PIN has expired
    const now = new Date();
    const expiresAt = new Date(user.reset_token_expires);
    if (now > expiresAt) {
      return res.status(400).json({
        success: false,
        message: 'Verification code has expired. Please request a new code.'
      });
    }

    // Check if too many attempts
    if (attempts >= MAX_VERIFICATION_ATTEMPTS) {
      return res.status(400).json({
        success: false,
        message: 'Too many verification attempts. Please request a new code.'
      });
    }

    // Verify the PIN
    const isValidPin = await verifyPin(pin, pinHash);

    // Increment attempt counter
    const newAttempts = attempts + 1;
    const updatedResetData = `PIN:${pinHash}:${newAttempts}`;

    await supabase
      .from('users')
      .update({
        reset_token: updatedResetData,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    if (!isValidPin) {
      const attemptsRemaining = MAX_VERIFICATION_ATTEMPTS - newAttempts;
      return res.status(400).json({
        success: false,
        message: `Invalid PIN code. ${attemptsRemaining} attempts remaining.`,
        attemptsRemaining
      });
    }

    // Mark verification as successful by changing format to "VERIFIED:{timestamp}"
    const verifiedData = `VERIFIED:${new Date().toISOString()}`;
    await supabase
      .from('users')
      .update({
        reset_token: verifiedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    console.log(`✅ PIN verification successful for: ${email}`);

    return res.status(200).json({
      success: true,
      message: 'PIN verification successful. You can now set your new password.',
      userId: user.id,
      email: user.email
    });

  } catch (error) {
    console.error('❌ PIN verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to verify PIN code',
      error: error.message
    });
  }
}

/**
 * Handle password reset with verified email
 */
async function handlePasswordReset(req, res) {
  try {
    const { email, newPassword } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email address is required'
      });
    }

    if (!newPassword) {
      return res.status(400).json({
        success: false,
        message: 'New password is required'
      });
    }

    console.log(`🔄 Password reset for verified email: ${email}`);

    // Find user with PIN verification data
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, username, reset_token, reset_token_expires')
      .eq('email', email)
      .single();

    if (userError || !user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email address'
      });
    }

    // Verify that the PIN was recently verified for password reset
    if (!user.reset_token || !user.reset_token.startsWith('VERIFIED:')) {
      return res.status(400).json({
        success: false,
        message: 'PIN verification required. Please verify your PIN first.'
      });
    }

    // Parse verification timestamp: "VERIFIED:{timestamp}"
    const verifiedTimestamp = user.reset_token.split(':')[1];
    if (!verifiedTimestamp) {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification data. Please verify your PIN again.'
      });
    }

    // Check if PIN verification is still valid (within last 30 minutes)
    const verifiedAt = new Date(verifiedTimestamp);
    const now = new Date();
    const thirtyMinutesAgo = new Date(now.getTime() - 30 * 60 * 1000);

    if (verifiedAt < thirtyMinutesAgo) {
      return res.status(400).json({
        success: false,
        message: 'PIN verification has expired. Please verify your PIN again.'
      });
    }

    // Validate password strength (basic validation)
    if (newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update user with new password in custom users table
    const { error: updateError } = await supabase
      .from('users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('❌ Failed to update password in users table:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Failed to update password',
        error: updateError.message
      });
    }

    // Also update Supabase Auth password for seamless login
    try {
      const { error: authUpdateError } = await supabase.auth.admin.updateUserById(
        user.auth_user_id || user.id, // Use auth_user_id if available, fallback to user.id
        { password: newPassword }
      );

      if (authUpdateError) {
        console.warn('⚠️ Failed to update Supabase Auth password:', authUpdateError.message);
        // Don't fail the request - the custom table password is updated
        // User can still login through the enhanced login system
      } else {
        console.log('✅ Supabase Auth password updated successfully');
      }
    } catch (authError) {
      console.warn('⚠️ Supabase Auth update error:', authError.message);
      // Continue - the custom table password is still updated
    }

    // Clean up PIN verification data
    await supabase
      .from('users')
      .update({
        reset_token: null,
        reset_token_expires: null
      })
      .eq('id', user.id);

    console.log(`✅ Password reset successful for: ${user.email}`);

    return res.status(200).json({
      success: true,
      message: 'Password has been reset successfully. You can now login with your new password.'
    });

  } catch (error) {
    console.error('❌ Password reset error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to reset password',
      error: error.message
    });
  }
}

/**
 * Generate cryptographically secure 6-digit PIN
 */
function generateSecurePin() {
  // Use crypto.getRandomValues for secure random generation
  const array = new Uint32Array(1);
  crypto.getRandomValues(array);

  // Generate 6-digit code (000000-999999)
  const pin = (array[0] % 1000000).toString().padStart(6, '0');
  return pin;
}

/**
 * Hash PIN code for secure storage
 */
async function hashPin(pin) {
  const saltRounds = 12;
  return await bcrypt.hash(pin, saltRounds);
}

/**
 * Verify PIN against hash
 */
async function verifyPin(pin, hash) {
  return await bcrypt.compare(pin, hash);
}

/**
 * Generate password reset email content
 */
function generatePasswordResetEmailContent(pinCode, userName, expiryMinutes) {
  const subject = 'Reset Your Aureus Alliance Password';

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
      <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #d97706; margin: 0; font-size: 28px;">Aureus Alliance</h1>
          <p style="color: #6b7280; margin: 5px 0 0 0;">Password Reset Request</p>
        </div>

        <h2 style="color: #374151; margin-bottom: 20px;">Hello ${userName},</h2>

        <p style="color: #4b5563; line-height: 1.6; margin-bottom: 20px;">
          You requested to reset your password for your Aureus Alliance account.
          Please use the verification code below to proceed with your password reset:
        </p>

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; text-align: center; margin: 30px 0;">
          <p style="color: #374151; margin: 0 0 10px 0; font-size: 14px; font-weight: 600;">Your Verification Code:</p>
          <div style="font-size: 32px; font-weight: bold; color: #d97706; letter-spacing: 8px; font-family: 'Courier New', monospace;">
            ${pinCode}
          </div>
          <p style="color: #6b7280; margin: 10px 0 0 0; font-size: 12px;">
            This code expires in ${expiryMinutes} minutes
          </p>
        </div>

        <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0;">
          <p style="color: #92400e; margin: 0; font-size: 14px;">
            <strong>Security Notice:</strong> If you didn't request this password reset, please ignore this email.
            Your account remains secure.
          </p>
        </div>

        <p style="color: #4b5563; line-height: 1.6; margin-top: 30px;">
          Best regards,<br>
          <strong>The Aureus Alliance Team</strong>
        </p>

        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">

        <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
          This is an automated message. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;

  const text = `
Aureus Alliance - Password Reset Request

Hello ${userName},

You requested to reset your password for your Aureus Alliance account.

Your verification code is: ${pinCode}

This code expires in ${expiryMinutes} minutes.

If you didn't request this password reset, please ignore this email.

Best regards,
The Aureus Alliance Team
  `;

  return { subject, html, text };
}
