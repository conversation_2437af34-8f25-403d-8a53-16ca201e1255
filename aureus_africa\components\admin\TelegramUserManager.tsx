import React, { useState, useEffect } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'

interface TelegramUser {
  id: string
  user_id: number | null
  telegram_id: number
  username: string
  first_name: string
  last_name: string
  is_registered: boolean
  created_at: string
  users?: {
    id: number
    username: string
    email: string
    full_name: string
  } | null
}

interface TelegramUserManagerProps {
  userId: number
  onClose: () => void
  onUpdate: () => void
  adminUser?: any
}

export const TelegramUserManager: React.FC<TelegramUserManagerProps> = ({
  userId,
  onClose,
  onUpdate,
  adminUser
}) => {
  const [telegramUsers, setTelegramUsers] = useState<TelegramUser[]>([])
  const [unlinkedTelegramUsers, setUnlinkedTelegramUsers] = useState<TelegramUser[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadTelegramUsers()
  }, [userId])

  const loadTelegramUsers = async () => {
    try {
      setLoading(true)
      setError('')

      // Use service role client to bypass RLS policies for admin operations
      const serviceRoleClient = getServiceRoleClient()

      // Get telegram users linked to this user
      const { data: linkedUsers, error: linkedError } = await serviceRoleClient
        .from('telegram_users')
        .select(`
          *,
          users(id, username, email, full_name)
        `)
        .eq('user_id', userId)

      if (linkedError) throw linkedError

      // Get unlinked telegram users for potential linking
      const { data: unlinkedUsers, error: unlinkedError } = await serviceRoleClient
        .from('telegram_users')
        .select(`
          *,
          users(id, username, email, full_name)
        `)
        .is('user_id', null)
        .limit(20)

      if (unlinkedError) throw unlinkedError

      setTelegramUsers(linkedUsers || [])
      setUnlinkedTelegramUsers(unlinkedUsers || [])
    } catch (err: any) {
      console.error('Error loading telegram users:', err)
      setError(err.message || 'Failed to load telegram users')
    } finally {
      setLoading(false)
    }
  }

  const linkTelegramUser = async (telegramUserId: string) => {
    try {
      // Use service role client to bypass RLS policies for admin operations
      const serviceRoleClient = getServiceRoleClient()

      const { error } = await serviceRoleClient
        .from('telegram_users')
        .update({ user_id: userId })
        .eq('id', telegramUserId)

      if (error) throw error

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        'LINK_TELEGRAM_USER',
        'user',
        userId.toString(),
        { telegram_user_id: telegramUserId }
      )

      await loadTelegramUsers()
      onUpdate()
    } catch (err: any) {
      console.error('Error linking telegram user:', err)
      setError(err.message || 'Failed to link telegram user')
    }
  }

  const unlinkTelegramUser = async (telegramUserId: string) => {
    if (!confirm('Are you sure you want to unlink this Telegram account?')) {
      return
    }

    try {
      // Use service role client to bypass RLS policies for admin operations
      const serviceRoleClient = getServiceRoleClient()

      const { error } = await serviceRoleClient
        .from('telegram_users')
        .update({ user_id: null })
        .eq('id', telegramUserId)

      if (error) throw error

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        'UNLINK_TELEGRAM_USER',
        'user',
        userId.toString(),
        { telegram_user_id: telegramUserId }
      )

      await loadTelegramUsers()
      onUpdate()
    } catch (err: any) {
      console.error('Error unlinking telegram user:', err)
      setError(err.message || 'Failed to unlink telegram user')
    }
  }

  const filteredUnlinkedUsers = unlinkedTelegramUsers.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.last_name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-400"></div>
        <span className="ml-3 text-gray-300">Loading telegram users...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">💬 Telegram Account Management</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white"
        >
          ✕
        </button>
      </div>

      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
          <p className="text-red-400 text-sm">❌ {error}</p>
        </div>
      )}

      {/* Currently Linked Accounts */}
      <div>
        <h4 className="text-md font-medium text-white mb-3">🔗 Linked Telegram Accounts</h4>
        {telegramUsers.length > 0 ? (
          <div className="space-y-2">
            {telegramUsers.map((tgUser) => (
              <div key={tgUser.id} className="bg-gray-800/50 rounded-lg p-3 flex items-center justify-between">
                <div>
                  <div className="text-white font-medium">
                    @{tgUser.username}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {tgUser.first_name} {tgUser.last_name}
                  </div>
                  <div className="text-gray-500 text-xs">
                    Telegram ID: {tgUser.telegram_id}
                  </div>
                </div>
                <button
                  onClick={() => unlinkTelegramUser(tgUser.id)}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-500 transition-colors"
                >
                  Unlink
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-gray-800/30 rounded-lg p-4 text-center">
            <p className="text-gray-400">No Telegram accounts linked to this user</p>
          </div>
        )}
      </div>

      {/* Available Accounts to Link */}
      <div>
        <h4 className="text-md font-medium text-white mb-3">📱 Available Telegram Accounts</h4>
        
        {/* Search */}
        <div className="mb-3">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search telegram users..."
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500"
          />
        </div>

        {filteredUnlinkedUsers.length > 0 ? (
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {filteredUnlinkedUsers.map((tgUser) => (
              <div key={tgUser.id} className="bg-gray-800/30 rounded-lg p-3 flex items-center justify-between">
                <div>
                  <div className="text-white font-medium">
                    @{tgUser.username}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {tgUser.first_name} {tgUser.last_name}
                  </div>
                  <div className="text-gray-500 text-xs">
                    Telegram ID: {tgUser.telegram_id} | Created: {new Date(tgUser.created_at).toLocaleDateString()}
                  </div>
                </div>
                <button
                  onClick={() => linkTelegramUser(tgUser.id)}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-500 transition-colors"
                >
                  Link
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-gray-800/30 rounded-lg p-4 text-center">
            <p className="text-gray-400">
              {searchTerm ? 'No matching telegram users found' : 'No unlinked telegram accounts available'}
            </p>
          </div>
        )}
      </div>

      {/* Info Box */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <h5 className="text-blue-400 font-medium mb-2">ℹ️ About Telegram Linking</h5>
        <ul className="text-blue-300 text-sm space-y-1">
          <li>• Users can have multiple Telegram accounts linked</li>
          <li>• Unlinking removes the connection but doesn't delete the Telegram account</li>
          <li>• Only unlinked Telegram accounts can be linked to users</li>
          <li>• Changes are logged for audit purposes</li>
        </ul>
      </div>

      <div className="flex justify-end">
        <button
          onClick={onClose}
          className="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  )
}
