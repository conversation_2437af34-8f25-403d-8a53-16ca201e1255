/**
 * SECURITY DASHBOARD API
 * 
 * Backend API functions for the security monitoring dashboard
 * with real-time data processing and bot-safe operations.
 */

import { supabase } from './supabase';
import { securityMonitoring } from './securityMonitoring';

interface DashboardMetrics {
  overview: {
    totalEvents: number;
    criticalAlerts: number;
    highAlerts: number;
    mediumAlerts: number;
    lowAlerts: number;
    activeThreats: number;
    blockedIPs: number;
    systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  };
  botActivity: {
    totalBotEvents: number;
    botPayments: number;
    botRegistrations: number;
    botErrors: number;
    botHealthStatus: 'OPERATIONAL' | 'WARNING' | 'ERROR';
  };
  timeSeriesData: {
    timestamp: string;
    alerts: number;
    threats: number;
    botEvents: number;
    userEvents: number;
  }[];
  topThreats: {
    type: string;
    count: number;
    severity: string;
  }[];
  recentAlerts: SecurityAlert[];
}

interface SecurityAlert {
  id: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  type: string;
  title: string;
  description: string;
  source: string;
  timestamp: Date;
  resolved: boolean;
  metadata: any;
  responseActions?: string[];
}

class SecurityDashboardAPI {
  /**
   * Get comprehensive dashboard metrics
   */
  async getDashboardMetrics(timeRange: '1h' | '24h' | '7d' | '30d' = '24h'): Promise<DashboardMetrics> {
    try {
      console.log(`📊 Fetching dashboard metrics for ${timeRange}`);

      const startTime = this.getTimeRangeStart(timeRange);

      // Fetch all required data in parallel
      const [
        securityEvents,
        securityAlerts,
        blockedIPs,
        botEvents,
        threatData
      ] = await Promise.all([
        this.getSecurityEvents(startTime),
        this.getSecurityAlerts(startTime),
        this.getBlockedIPs(),
        this.getBotEvents(startTime),
        this.getThreatData(startTime)
      ]);

      // Process overview metrics
      const overview = this.processOverviewMetrics(securityEvents, securityAlerts, blockedIPs);

      // Process bot activity metrics
      const botActivity = this.processBotActivityMetrics(botEvents);

      // Process time series data
      const timeSeriesData = this.processTimeSeriesData(securityEvents, timeRange);

      // Process top threats
      const topThreats = this.processTopThreats(threatData);

      // Get recent alerts
      const recentAlerts = this.processRecentAlerts(securityAlerts);

      console.log('✅ Dashboard metrics processed successfully');

      return {
        overview,
        botActivity,
        timeSeriesData,
        topThreats,
        recentAlerts
      };

    } catch (error) {
      console.error('❌ Error fetching dashboard metrics:', error);
      throw new Error('Failed to fetch dashboard metrics');
    }
  }

  /**
   * Get real-time security events
   */
  private async getSecurityEvents(startTime: Date): Promise<any[]> {
    const { data, error } = await supabase
      .from('admin_audit_logs')
      .select('*')
      .gte('created_at', startTime.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching security events:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get security alerts
   */
  private async getSecurityAlerts(startTime: Date): Promise<any[]> {
    const { data, error } = await supabase
      .from('security_alerts')
      .select('*')
      .gte('created_at', startTime.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching security alerts:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get blocked IPs
   */
  private async getBlockedIPs(): Promise<any[]> {
    const { data, error } = await supabase
      .from('blocked_ips')
      .select('*')
      .eq('is_active', true);

    if (error) {
      console.error('❌ Error fetching blocked IPs:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get bot-specific events
   */
  private async getBotEvents(startTime: Date): Promise<any[]> {
    const { data, error } = await supabase
      .from('admin_audit_logs')
      .select('*')
      .gte('created_at', startTime.toISOString())
      .or('admin_email.like.%bot%,admin_email.like.%system%,admin_email.eq.aureus-bot')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching bot events:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get threat intelligence data
   */
  private async getThreatData(startTime: Date): Promise<any[]> {
    const { data, error } = await supabase
      .from('admin_audit_logs')
      .select('*')
      .gte('created_at', startTime.toISOString())
      .like('action', 'THREAT_DETECTED_%')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching threat data:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Process overview metrics
   */
  private processOverviewMetrics(events: any[], alerts: any[], blockedIPs: any[]) {
    const criticalAlerts = alerts.filter(a => a.severity === 'CRITICAL').length;
    const highAlerts = alerts.filter(a => a.severity === 'HIGH').length;
    const mediumAlerts = alerts.filter(a => a.severity === 'MEDIUM').length;
    const lowAlerts = alerts.filter(a => a.severity === 'LOW').length;

    const activeThreats = alerts.filter(a => 
      !a.resolved && (a.severity === 'HIGH' || a.severity === 'CRITICAL')
    ).length;

    let systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';
    if (criticalAlerts > 0 || activeThreats > 10) {
      systemHealth = 'CRITICAL';
    } else if (highAlerts > 5 || activeThreats > 5) {
      systemHealth = 'WARNING';
    }

    return {
      totalEvents: events.length,
      criticalAlerts,
      highAlerts,
      mediumAlerts,
      lowAlerts,
      activeThreats,
      blockedIPs: blockedIPs.length,
      systemHealth
    };
  }

  /**
   * Process bot activity metrics
   */
  private processBotActivityMetrics(botEvents: any[]) {
    const botPayments = botEvents.filter(e => 
      e.action?.includes('PAYMENT') || e.action?.includes('TRANSACTION')
    ).length;

    const botRegistrations = botEvents.filter(e => 
      e.action?.includes('REGISTRATION') || e.action?.includes('USER_CREATED')
    ).length;

    const botErrors = botEvents.filter(e => 
      e.action?.includes('ERROR') || e.action?.includes('FAILED')
    ).length;

    let botHealthStatus: 'OPERATIONAL' | 'WARNING' | 'ERROR' = 'OPERATIONAL';
    if (botErrors > 10) {
      botHealthStatus = 'ERROR';
    } else if (botErrors > 5) {
      botHealthStatus = 'WARNING';
    }

    return {
      totalBotEvents: botEvents.length,
      botPayments,
      botRegistrations,
      botErrors,
      botHealthStatus
    };
  }

  /**
   * Process time series data for charts
   */
  private processTimeSeriesData(events: any[], timeRange: string) {
    const intervals = this.getTimeIntervals(timeRange);
    const data: any[] = [];

    for (let i = 0; i < intervals.count; i++) {
      const intervalStart = new Date(Date.now() - (intervals.count - i) * intervals.duration);
      const intervalEnd = new Date(intervalStart.getTime() + intervals.duration);

      const intervalEvents = events.filter(e => {
        const eventTime = new Date(e.created_at);
        return eventTime >= intervalStart && eventTime < intervalEnd;
      });

      const alerts = intervalEvents.filter(e => e.action?.includes('ALERT')).length;
      const threats = intervalEvents.filter(e => e.action?.includes('THREAT')).length;
      const botEvents = intervalEvents.filter(e => 
        e.admin_email?.includes('bot') || e.admin_email?.includes('system')
      ).length;
      const userEvents = intervalEvents.length - botEvents;

      data.push({
        timestamp: intervalStart.toISOString(),
        alerts,
        threats,
        botEvents,
        userEvents
      });
    }

    return data;
  }

  /**
   * Process top threats
   */
  private processTopThreats(threatData: any[]) {
    const threatCounts: Record<string, { count: number; severity: string }> = {};

    threatData.forEach(threat => {
      const threatTypes = threat.metadata?.threatTypes || [];
      const severity = threat.action?.includes('CRITICAL') ? 'CRITICAL' :
                     threat.action?.includes('HIGH') ? 'HIGH' :
                     threat.action?.includes('MEDIUM') ? 'MEDIUM' : 'LOW';

      threatTypes.forEach((type: string) => {
        if (!threatCounts[type]) {
          threatCounts[type] = { count: 0, severity };
        }
        threatCounts[type].count++;
      });
    });

    return Object.entries(threatCounts)
      .map(([type, data]) => ({
        type,
        count: data.count,
        severity: data.severity
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Process recent alerts
   */
  private processRecentAlerts(alerts: any[]): SecurityAlert[] {
    return alerts
      .slice(0, 20)
      .map(alert => ({
        id: alert.alert_id,
        severity: alert.severity,
        type: alert.type,
        title: alert.title,
        description: alert.description,
        source: alert.source,
        timestamp: new Date(alert.created_at),
        resolved: alert.resolved,
        metadata: alert.metadata,
        responseActions: alert.response_actions
      }));
  }

  /**
   * Resolve security alert
   */
  async resolveAlert(alertId: string, resolvedBy: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔒 Resolving alert: ${alertId}`);

      const { error } = await supabase
        .from('security_alerts')
        .update({
          resolved: true,
          resolved_at: new Date().toISOString(),
          resolved_by: resolvedBy
        })
        .eq('alert_id', alertId);

      if (error) {
        console.error('❌ Error resolving alert:', error);
        return { success: false, error: 'Failed to resolve alert' };
      }

      // Log the resolution
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: resolvedBy,
          action: 'ALERT_RESOLVED',
          target_type: 'security_alert',
          target_id: alertId,
          metadata: {
            resolvedBy,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

      console.log(`✅ Alert resolved: ${alertId}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error resolving alert:', error);
      return { success: false, error: 'Alert resolution failed' };
    }
  }

  /**
   * Block IP address from dashboard
   */
  async blockIP(ipAddress: string, reason: string, blockedBy: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🚫 Blocking IP from dashboard: ${ipAddress}`);

      // Use the security monitoring system to block IP
      await securityMonitoring.blockIP(ipAddress, `Dashboard block: ${reason}`);

      // Log the action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: blockedBy,
          action: 'IP_BLOCKED_MANUAL',
          target_type: 'ip_address',
          target_id: ipAddress,
          metadata: {
            reason,
            blockedBy,
            source: 'security_dashboard',
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

      console.log(`✅ IP blocked from dashboard: ${ipAddress}`);
      return { success: true };

    } catch (error) {
      console.error('❌ Error blocking IP:', error);
      return { success: false, error: 'IP blocking failed' };
    }
  }

  /**
   * Get bot health status
   */
  async getBotHealthStatus(): Promise<{
    status: 'OPERATIONAL' | 'WARNING' | 'ERROR';
    lastActivity: Date | null;
    errorCount: number;
    details: any;
  }> {
    try {
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);

      // Get recent bot events
      const { data: botEvents } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .gte('created_at', last24Hours.toISOString())
        .or('admin_email.eq.aureus-bot,admin_email.like.%bot%')
        .order('created_at', { ascending: false })
        .limit(100);

      if (!botEvents || botEvents.length === 0) {
        return {
          status: 'ERROR',
          lastActivity: null,
          errorCount: 0,
          details: { message: 'No bot activity detected in last 24 hours' }
        };
      }

      const lastActivity = new Date(botEvents[0].created_at);
      const errorCount = botEvents.filter(e => 
        e.action?.includes('ERROR') || e.action?.includes('FAILED')
      ).length;

      let status: 'OPERATIONAL' | 'WARNING' | 'ERROR' = 'OPERATIONAL';
      if (errorCount > 10) {
        status = 'ERROR';
      } else if (errorCount > 5 || Date.now() - lastActivity.getTime() > 60 * 60 * 1000) {
        status = 'WARNING';
      }

      return {
        status,
        lastActivity,
        errorCount,
        details: {
          totalEvents: botEvents.length,
          recentErrors: botEvents.filter(e => e.action?.includes('ERROR')).slice(0, 5)
        }
      };

    } catch (error) {
      console.error('❌ Error getting bot health status:', error);
      return {
        status: 'ERROR',
        lastActivity: null,
        errorCount: 0,
        details: { error: error.message }
      };
    }
  }

  /**
   * Helper methods
   */
  private getTimeRangeStart(timeRange: string): Date {
    const now = new Date();
    switch (timeRange) {
      case '1h': return new Date(now.getTime() - 60 * 60 * 1000);
      case '24h': return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case '7d': return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d': return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      default: return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
  }

  private getTimeIntervals(timeRange: string): { count: number; duration: number } {
    switch (timeRange) {
      case '1h': return { count: 12, duration: 5 * 60 * 1000 }; // 5-minute intervals
      case '24h': return { count: 24, duration: 60 * 60 * 1000 }; // 1-hour intervals
      case '7d': return { count: 7, duration: 24 * 60 * 60 * 1000 }; // 1-day intervals
      case '30d': return { count: 30, duration: 24 * 60 * 60 * 1000 }; // 1-day intervals
      default: return { count: 24, duration: 60 * 60 * 1000 };
    }
  }
}

// Create singleton instance
export const securityDashboardAPI = new SecurityDashboardAPI();

export default securityDashboardAPI;
