import React, { useState, useEffect, useMemo } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import { printCertificate, downloadCertificateHTML, CertificateData } from '../../lib/utils/certificateGenerator';
import KYCVerificationForm from '../kyc/KYCVerificationForm';
import KYCStatusCard from '../kyc/KYCStatusCard';

// Import the correct constants from the working calculators
const TOTAL_SHARES = 1400000; // Total shares in circulation
const PLANT_CAPACITY_TPH = 200; // Tonnes per hour per plant
const EFFECTIVE_HOURS_PER_DAY = 20; // Operating hours per day
const OPERATING_DAYS_PER_YEAR = 330; // Operating days per year
const BULK_DENSITY_T_PER_M3 = 1.8; // Bulk density
const HA_PER_PLANT = 25; // Hectares per plant

// Default mining parameters (same as other calculators)
const DEFAULT_MINING_PARAMS = {
  avgGravelThickness: 0.8,
  inSituGrade: 0.9,
  recoveryFactor: 70, // percentage
  goldPriceUsdPerKg: 109026,
  opexPercent: 45 // percentage
};

interface PortfolioProps {
  user: any;
  currentPhase?: any;
}

interface ShareHolding {
  id: string;
  shares_purchased: number;
  total_amount: number;
  price_per_share: number;
  phase_name: string;
  phase_number: number;
  purchase_date: string;
  status: string;
  transaction_reference?: string;
}

interface PortfolioSummary {
  totalShares: number;
  totalInvested: number;
  currentValue: number;
  unrealizedGains: number;
  unrealizedGainsPercent: number;
  averageCostPerShare: number;
  projectedAnnualDividend: number;
}

interface DividendRecord {
  id: string;
  amount: number;
  payment_date: string;
  status: string;
  type: string;
}

interface Certificate {
  id: string;
  certificate_number: string;
  shares: number;
  issue_date: string;
  status: 'issued' | 'pending' | 'revoked';
  download_url?: string;
}

export const ComprehensivePortfolio: React.FC<PortfolioProps> = ({ user, currentPhase }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'certificates' | 'kyc' | 'transactions' | 'analytics'>('overview');
  const [holdings, setHoldings] = useState<ShareHolding[]>([]);
  const [dividends, setDividends] = useState<DividendRecord[]>([]);
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [kycStatus, setKycStatus] = useState<string>('pending');
  const [kycData, setKycData] = useState<any>(null);
  const [showKYCForm, setShowKYCForm] = useState(false);

  // Portfolio calculations using CORRECT Aureus Africa dividend formula
  const portfolioSummary = useMemo((): PortfolioSummary => {
    if (!holdings.length) {
      return {
        totalShares: 0,
        totalInvested: 0,
        currentValue: 0,
        unrealizedGains: 0,
        unrealizedGainsPercent: 0,
        averageCostPerShare: 0,
        projectedAnnualDividend: 0
      };
    }

    const totalShares = holdings.reduce((sum, holding) => sum + holding.shares_purchased, 0);
    const totalInvested = holdings.reduce((sum, holding) => sum + holding.total_amount, 0);

    // Calculate current value using latest phase price
    const currentPricePerShare = currentPhase?.price_per_share || 25;
    const currentValue = totalShares * currentPricePerShare;

    const unrealizedGains = currentValue - totalInvested;
    const unrealizedGainsPercent = totalInvested > 0 ? (unrealizedGains / totalInvested) * 100 : 0;
    const averageCostPerShare = totalInvested > 0 ? totalInvested / totalShares : 0;

    // Calculate CORRECT projected annual dividend using Aureus Africa formula
    // Based on baseline 25ha (1 plant) operation as per the working calculators
    const baselineLandHa = 25; // 1 plant baseline
    const numPlants = baselineLandHa / HA_PER_PLANT; // = 1 plant

    // Use the EXACT same formula as the working calculators
    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
    const annualGoldKg = (annualThroughputT * (DEFAULT_MINING_PARAMS.inSituGrade / BULK_DENSITY_T_PER_M3) * (DEFAULT_MINING_PARAMS.recoveryFactor / 100)) / 1000;
    const annualRevenue = annualGoldKg * DEFAULT_MINING_PARAMS.goldPriceUsdPerKg;
    const annualOperatingCost = annualRevenue * (DEFAULT_MINING_PARAMS.opexPercent / 100);
    const annualEbit = annualRevenue - annualOperatingCost;

    // Calculate dividend per share: 100% EBIT distribution across all shares
    const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0;
    const projectedAnnualDividend = dividendPerShare * totalShares;

    return {
      totalShares,
      totalInvested,
      currentValue,
      unrealizedGains,
      unrealizedGainsPercent,
      averageCostPerShare,
      projectedAnnualDividend
    };
  }, [holdings, currentPhase]);

  useEffect(() => {
    console.log('🔄 Portfolio component mounted with user:', user);
    console.log('📊 User database ID:', user?.database_user?.id);
    loadPortfolioData();
  }, [user]);

  const loadPortfolioData = async () => {
    console.log('🔄 Starting portfolio data load...');
    console.log('👤 User object:', user);
    console.log('🆔 Database user ID:', user?.database_user?.id);
    console.log('📧 User email:', user?.database_user?.email || user?.email);
    console.log('🔗 Account type:', user?.account_type);

    // Get user ID with multiple fallback strategies
    let userId = null;

    if (user?.database_user?.id) {
      userId = user.database_user.id;
      console.log('✅ Using database_user.id:', userId);
    } else if (user?.database_user?.telegram_id) {
      userId = user.database_user.telegram_id;
      console.log('✅ Using telegram_id as fallback:', userId);
    } else if (user?.id && typeof user.id === 'number') {
      userId = user.id;
      console.log('✅ Using user.id as fallback:', userId);
    }

    if (!userId) {
      console.log('⚠️ No valid user ID found, loading demo data for testing');
      console.log('🔍 Available user properties:', Object.keys(user || {}));

      // Load demo data for testing when no user is logged in
      const demoHoldings = [
        {
          id: 'demo-1',
          shares_purchased: 1000,
          total_amount: 5000,
          price_per_share: 5.00,
          phase_name: 'Pre Sale',
          phase_number: 0,
          purchase_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          transaction_reference: 'DEMO-TX-001'
        },
        {
          id: 'demo-2',
          shares_purchased: 500,
          total_amount: 5000,
          price_per_share: 10.00,
          phase_name: 'Phase 1',
          phase_number: 1,
          purchase_date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active',
          transaction_reference: 'DEMO-TX-002'
        }
      ];

      setHoldings(demoHoldings);
      setKycStatus('approved');

      // Set demo KYC data
      setKycData({
        id: 'demo-kyc-001',
        user_id: 999,
        first_name: 'Demo',
        last_name: 'User',
        full_legal_name: 'Demo User',
        id_type: 'national_id',
        phone_number: '+27 ************',
        email_address: '<EMAIL>',
        country_name: 'South Africa',
        kyc_status: 'completed',
        kyc_completed_at: '2025-01-15T10:00:00Z',
        data_consent_given: true,
        privacy_policy_accepted: true
      });

      const demoCertificates = demoHoldings.map((holding, index) => ({
        id: holding.id,
        certificate_number: `AUR-DEMO-${String(index + 1).padStart(3, '0')}`,
        shares: holding.shares_purchased,
        issue_date: holding.purchase_date,
        status: 'issued' as const,
      }));

      setCertificates(demoCertificates);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 Loading portfolio data for user ID:', userId);

      // Use service role client for all queries
      const serviceClient = getServiceRoleClient()

      // Try multiple query strategies for better compatibility
      let holdingsData = null;
      let holdingsError = null;

      // Strategy 1: Query with user_id and active status
      console.log('📊 Attempting Strategy 1: Active holdings query...');
      const query1 = await serviceClient
        .from('aureus_share_purchases')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (!query1.error && query1.data && query1.data.length > 0) {
        holdingsData = query1.data;
        console.log('✅ Strategy 1 successful - found', holdingsData.length, 'active holdings');
      } else {
        console.log('⚠️ Strategy 1 result:', query1.error?.message || 'No active holdings found');

        // Strategy 2: Query all statuses (maybe status filter is too restrictive)
        console.log('📊 Attempting Strategy 2: All statuses query...');
        const query2 = await serviceClient
          .from('aureus_share_purchases')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });

        if (!query2.error && query2.data && query2.data.length > 0) {
          holdingsData = query2.data;
          console.log('✅ Strategy 2 successful - found', holdingsData.length, 'purchases (all statuses)');
        } else {
          console.log('⚠️ Strategy 2 result:', query2.error?.message || 'No purchases found');

          // Strategy 3: Check if table exists and is accessible
          console.log('📊 Attempting Strategy 3: Table accessibility check...');
          const query3 = await serviceClient
            .from('aureus_share_purchases')
            .select('count(*)')
            .limit(1);

          if (!query3.error) {
            console.log('✅ Table exists and is accessible, but no data for user', userId);
            holdingsData = [];
          } else {
            console.log('❌ Table access error:', query3.error?.message);
            holdingsError = query3.error;
          }
        }
      }

      if (holdingsError) {
        console.error('❌ All query strategies failed:', holdingsError);
        console.error('Error details:', {
          code: holdingsError.code,
          message: holdingsError.message,
          hint: holdingsError.hint,
          details: holdingsError.details
        });

        // Set empty data but don't crash
        setHoldings([]);
      } else {
        console.log('✅ Final holdings data:', holdingsData);

        const formattedHoldings = holdingsData?.map(holding => ({
          id: holding.id,
          shares_purchased: holding.shares_purchased || 0,
          total_amount: holding.total_amount || 0,
          price_per_share: holding.shares_purchased > 0 ? holding.total_amount / holding.shares_purchased : 0,
          phase_name: holding.package_name || 'Share Purchase',
          phase_number: 0, // We'll determine this from package name or price
          purchase_date: holding.created_at,
          status: holding.status,
          transaction_reference: holding.payment_method || 'N/A'
        })) || [];

        setHoldings(formattedHoldings);
        console.log('📈 Transformed holdings:', formattedHoldings);
      }

      // Load dividend history (placeholder - would need actual dividend table)
      // For now, we'll use mock data
      setDividends([]);

      // Load real certificates from certificates table
      console.log('📜 Loading certificates for user:', userId);
      const { data: certificatesData, error: certificatesError } = await serviceClient
        .from('certificates')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'issued')
        .order('created_at', { ascending: false });

      if (certificatesError) {
        console.error('❌ Error loading certificates:', certificatesError);
        // Fallback to mock certificates if table doesn't exist yet
        const mockCertificates = holdingsData?.map((holding, index) => ({
          id: holding.id,
          certificate_number: `AUR-${String(userId).padStart(4, '0')}-${String(index + 1).padStart(3, '0')}`,
          shares: holding.shares_purchased || 0,
          issue_date: holding.created_at,
          status: 'issued' as const,
        })) || [];
        setCertificates(mockCertificates);
        console.log('📜 Using mock certificates:', mockCertificates);
      } else {
        // Use real certificates data
        const realCertificates = certificatesData?.map(cert => ({
          id: cert.id,
          certificate_number: cert.certificate_number,
          shares: cert.shares_count,
          issue_date: cert.issue_date,
          status: cert.status,
        })) || [];
        setCertificates(realCertificates);
      }

      // Load KYC information from existing kyc_information table
      console.log('🔐 Loading KYC data for user:', userId);
      let kycStatusResult = 'pending';
      let kycDataResult = null;

      const { data: kycInfo, error: kycError } = await supabase
        .from('kyc_information')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (kycError) {
        console.log('ℹ️ No KYC data found (expected for new users):', kycError.message);
      } else if (kycInfo) {
        kycDataResult = kycInfo;
        kycStatusResult = kycInfo.kyc_status === 'completed' ? 'approved' : kycInfo.kyc_status;
        console.log('✅ KYC Status:', kycStatusResult, 'Data:', kycInfo);
      }

      setKycStatus(kycStatusResult);
      setKycData(kycDataResult);

    } catch (error) {
      console.error('❌ Error loading portfolio data:', error);
      // Set empty data on error to prevent crashes
      setHoldings([]);
      setDividends([]);
      setCertificates([]);
      setKycStatus('pending');
      setKycData(null);
    } finally {
      setLoading(false);
    }
  };

  const generateCertificatePDF = async (certificate: Certificate, action: 'view' | 'download' = 'view') => {
    const certificateData: CertificateData = {
      certificateNumber: certificate.certificate_number,
      holderName: `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'Shareholder',
      shares: certificate.shares,
      issueDate: certificate.issue_date,
      userId: user.database_user?.id?.toString() || 'N/A'
    };

    if (action === 'download') {
      downloadCertificateHTML(certificateData);
    } else {
      printCertificate(certificateData);
    }
  };

  const handleKYCComplete = (status: string) => {
    setKycStatus(status === 'completed' ? 'approved' : status);
    setShowKYCForm(false);
    // Reload portfolio data to get updated KYC info
    loadPortfolioData();
  };

  const renderKYCTab = () => (
    <div className="space-y-6">
      <KYCStatusCard
        status={kycStatus === 'approved' ? 'completed' : kycStatus as any}
        kycData={kycData}
        onStartKYC={() => setShowKYCForm(true)}
        onEditKYC={() => setShowKYCForm(true)}
      />

      {showKYCForm && (
        <KYCVerificationForm
          userId={user.database_user.id}
          existingKYC={kycData}
          onKYCComplete={handleKYCComplete}
        />
      )}
    </div>
  );

  const renderCertificatesTab = () => (
    <div className="space-y-6">
      {/* KYC Status Notice */}
      {kycStatus !== 'approved' && (
        <div className="bg-yellow-900/30 rounded-lg p-4 border border-yellow-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-yellow-400 text-xl">⚠️</span>
              <div>
                <h4 className="text-yellow-400 font-semibold">KYC Verification Required</h4>
                <p className="text-yellow-200 text-sm">
                  Complete your KYC verification to access and download your share certificates.
                </p>
              </div>
            </div>
            <button
              onClick={() => setActiveTab('kyc')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium"
            >
              Complete KYC
            </button>
          </div>
        </div>
      )}

      {/* Certificate Status Warning */}
      {certificates.length === 0 && holdings.length > 0 && (
        <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700 mb-6">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">📜</span>
            <div>
              <h4 className="text-blue-400 font-semibold">Certificates Pending</h4>
              <p className="text-blue-200 text-sm">
                Your share certificates are being prepared by our admin team.
                You will be able to download them once they are issued.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Certificates Grid */}
      {certificates.length === 0 ? (
        <div className="bg-gray-800 rounded-lg p-8 border border-gray-700 text-center">
          <div className="text-6xl mb-4">📜</div>
          <h3 className="text-xl font-semibold text-white mb-2">No Certificates Available</h3>
          <p className="text-gray-400 mb-4">
            {holdings.length === 0
              ? "You don't have any share purchases yet."
              : "Your certificates are being prepared by our admin team."
            }
          </p>
          {holdings.length > 0 && (
            <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700">
              <p className="text-blue-200 text-sm">
                💡 <strong>Note:</strong> Certificates are manually issued by admin after share purchase approval.
                Please contact support if you have approved purchases but no certificates after 24 hours.
              </p>
            </div>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {certificates.map((certificate) => (
          <div key={certificate.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="text-center mb-4">
              <div className="text-3xl mb-2">📜</div>
              <h4 className="text-lg font-semibold text-white">
                Certificate #{certificate.certificate_number}
              </h4>
              <p className="text-gray-400 text-sm">
                {certificate.shares.toLocaleString()} shares
              </p>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Issue Date:</span>
                <span className="text-white">
                  {new Date(certificate.issue_date).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Status:</span>
                <span className={`font-semibold ${
                  certificate.status === 'issued' ? 'text-green-400' :
                  certificate.status === 'pending' ? 'text-yellow-400' : 'text-red-400'
                }`}>
                  {certificate.status.charAt(0).toUpperCase() + certificate.status.slice(1)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Certificate ID:</span>
                <span className="text-white font-mono text-xs">
                  {certificate.certificate_number}
                </span>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <button
                onClick={() => generateCertificatePDF(certificate, 'view')}
                disabled={kycStatus !== 'approved'}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                📄 View Certificate
              </button>
              <button
                onClick={() => generateCertificatePDF(certificate, 'download')}
                disabled={kycStatus !== 'approved'}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                📥 Download HTML
              </button>
            </div>
          </div>
        ))}
        </div>
      )}

      {/* Certificate Information */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">📋 Certificate Information</h4>
        <div className="space-y-3 text-sm">
          <div>
            <h5 className="text-blue-400 font-semibold mb-1">What are Share Certificates?</h5>
            <p className="text-gray-300">
              Digital share certificates are official documents that prove your ownership of Aureus Africa shares.
              Each certificate includes your name, share quantity, unique certificate number, and official company seal.
            </p>
          </div>
          <div>
            <h5 className="text-blue-400 font-semibold mb-1">Certificate Features:</h5>
            <ul className="text-gray-300 space-y-1 ml-4">
              <li>• Unique certificate numbers for verification</li>
              <li>• QR codes for authenticity checking</li>
              <li>• Professional PDF format for printing</li>
              <li>• Legally recognized ownership documents</li>
            </ul>
          </div>
          <div>
            <h5 className="text-blue-400 font-semibold mb-1">Requirements:</h5>
            <p className="text-gray-300">
              KYC verification must be completed before certificates can be issued or downloaded.
              This ensures compliance with regulatory requirements and protects your holdings.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Portfolio Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-blue-400">
            {portfolioSummary.totalShares.toLocaleString()}
          </div>
          <div className="text-sm text-gray-400">Total Shares</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-green-400">
            ${portfolioSummary.currentValue.toLocaleString(undefined, { maximumFractionDigits: 0 })}
          </div>
          <div className="text-sm text-gray-400">Current Value</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className={`text-2xl font-bold ${portfolioSummary.unrealizedGains >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            ${portfolioSummary.unrealizedGains.toLocaleString(undefined, { maximumFractionDigits: 0 })}
          </div>
          <div className="text-sm text-gray-400">
            Unrealized {portfolioSummary.unrealizedGains >= 0 ? 'Gains' : 'Losses'}
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-yellow-400">
            ${portfolioSummary.projectedAnnualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
          </div>
          <div className="text-sm text-gray-400">Projected Annual Dividend</div>
        </div>
      </div>

      {/* Holdings Breakdown */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold text-white mb-4">📊 Holdings Breakdown</h3>
        
        {holdings.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-4">📈</div>
            <h4 className="text-lg font-semibold text-white mb-2">No Share Holdings Yet</h4>
            <p className="text-gray-400 mb-4">
              Start building your portfolio by purchasing shares in the current phase.
            </p>
            <div className="text-xs text-gray-500 mb-4 space-y-1">
              <div>Debug Info:</div>
              <div>• User ID: {user?.database_user?.id || 'Not found'}</div>
              <div>• User Email: {user?.database_user?.email || user?.email || 'Not found'}</div>
              <div>• Account Type: {user?.account_type || 'Unknown'}</div>
              <div>• Holdings Count: {holdings.length}</div>
              <div>• Loading: {loading ? 'Yes' : 'No'}</div>
            </div>
            <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500">
              Purchase Shares
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-2 text-gray-400">Phase</th>
                  <th className="text-left py-2 text-gray-400">Shares</th>
                  <th className="text-left py-2 text-gray-400">Purchase Price</th>
                  <th className="text-left py-2 text-gray-400">Current Value</th>
                  <th className="text-left py-2 text-gray-400">Gain/Loss</th>
                  <th className="text-left py-2 text-gray-400">Date</th>
                </tr>
              </thead>
              <tbody>
                {holdings.map((holding) => {
                  const currentValue = holding.shares_purchased * (currentPhase?.price_per_share || 25);
                  const gainLoss = currentValue - holding.total_amount;
                  const gainLossPercent = (gainLoss / holding.total_amount) * 100;
                  
                  return (
                    <tr key={holding.id} className="border-b border-gray-700/50">
                      <td className="py-3 text-white font-semibold">{holding.phase_name}</td>
                      <td className="py-3 text-gray-300">{holding.shares_purchased.toLocaleString()}</td>
                      <td className="py-3 text-gray-300">${holding.total_amount.toLocaleString()}</td>
                      <td className="py-3 text-gray-300">${currentValue.toLocaleString()}</td>
                      <td className={`py-3 font-semibold ${gainLoss >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        ${gainLoss.toLocaleString()} ({gainLossPercent.toFixed(1)}%)
                      </td>
                      <td className="py-3 text-gray-400">
                        {new Date(holding.purchase_date).toLocaleDateString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">💰 Portfolio Metrics</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Total Invested:</span>
              <span className="text-white font-semibold">
                ${portfolioSummary.totalInvested.toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Average Cost/Share:</span>
              <span className="text-white font-semibold">
                ${portfolioSummary.averageCostPerShare.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Current Price/Share:</span>
              <span className="text-white font-semibold">
                ${currentPhase?.price_per_share || 25}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Total Return:</span>
              <span className={`font-semibold ${portfolioSummary.unrealizedGainsPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {portfolioSummary.unrealizedGainsPercent.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">📈 Dividend Projections</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Annual Dividend:</span>
              <span className="text-green-400 font-semibold">
                ${portfolioSummary.projectedAnnualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Quarterly Dividend:</span>
              <span className="text-green-400 font-semibold">
                ${(portfolioSummary.projectedAnnualDividend / 4).toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Dividend per Share:</span>
              <span className="text-green-400 font-semibold">
                ${portfolioSummary.totalShares > 0
                  ? (portfolioSummary.projectedAnnualDividend / portfolioSummary.totalShares).toFixed(2)
                  : '0.00'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Dividend Yield:</span>
              <span className="text-green-400 font-semibold">
                {portfolioSummary.totalInvested > 0
                  ? ((portfolioSummary.projectedAnnualDividend / portfolioSummary.totalInvested) * 100).toFixed(1)
                  : 0}%
              </span>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-900/30 rounded-lg border border-blue-700">
            <p className="text-blue-200 text-sm mb-2">
              💡 <strong>Dividend Calculation Method:</strong>
            </p>
            <ul className="text-blue-200 text-xs space-y-1 ml-4">
              <li>• Based on baseline 25ha (1 plant) mining operation</li>
              <li>• Gold production: {((1 * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR * (DEFAULT_MINING_PARAMS.inSituGrade / BULK_DENSITY_T_PER_M3) * (DEFAULT_MINING_PARAMS.recoveryFactor / 100)) / 1000).toFixed(0)} kg/year</li>
              <li>• Revenue: ${((((1 * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR * (DEFAULT_MINING_PARAMS.inSituGrade / BULK_DENSITY_T_PER_M3) * (DEFAULT_MINING_PARAMS.recoveryFactor / 100)) / 1000) * DEFAULT_MINING_PARAMS.goldPriceUsdPerKg) / 1000000).toFixed(1)}M annually</li>
              <li>• 100% EBIT distributed across {TOTAL_SHARES.toLocaleString()} total shares</li>
              <li>• Actual dividends scale with operational expansion</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTransactionsTab = () => (
    <div className="space-y-6">
      {/* Transaction Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-blue-400">
            {holdings.length}
          </div>
          <div className="text-sm text-gray-400">Share Purchases</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-green-400">
            {dividends.length}
          </div>
          <div className="text-sm text-gray-400">Dividend Payments</div>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="text-2xl font-bold text-yellow-400">
            ${portfolioSummary.totalInvested.toLocaleString()}
          </div>
          <div className="text-sm text-gray-400">Total Invested</div>
        </div>
      </div>

      {/* Transaction History */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-white">📋 Transaction History</h3>
          <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 text-sm">
            📊 Export for Tax Reporting
          </button>
        </div>

        {holdings.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-4">📋</div>
            <h4 className="text-lg font-semibold text-white mb-2">No Transactions Yet</h4>
            <p className="text-gray-400">
              Your share purchases and dividend payments will appear here.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-2 text-gray-400">Date</th>
                  <th className="text-left py-2 text-gray-400">Type</th>
                  <th className="text-left py-2 text-gray-400">Description</th>
                  <th className="text-left py-2 text-gray-400">Shares</th>
                  <th className="text-left py-2 text-gray-400">Amount</th>
                  <th className="text-left py-2 text-gray-400">Status</th>
                </tr>
              </thead>
              <tbody>
                {holdings.map((holding) => (
                  <tr key={holding.id} className="border-b border-gray-700/50">
                    <td className="py-3 text-gray-300">
                      {new Date(holding.purchase_date).toLocaleDateString()}
                    </td>
                    <td className="py-3">
                      <span className="px-2 py-1 bg-blue-600 text-white rounded text-xs">
                        Purchase
                      </span>
                    </td>
                    <td className="py-3 text-gray-300">
                      Share Purchase - {holding.phase_name}
                    </td>
                    <td className="py-3 text-white font-semibold">
                      +{holding.shares_purchased.toLocaleString()}
                    </td>
                    <td className="py-3 text-gray-300">
                      ${holding.total_amount.toLocaleString()}
                    </td>
                    <td className="py-3">
                      <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                        {holding.status}
                      </span>
                    </td>
                  </tr>
                ))}
                {dividends.map((dividend) => (
                  <tr key={dividend.id} className="border-b border-gray-700/50">
                    <td className="py-3 text-gray-300">
                      {new Date(dividend.payment_date).toLocaleDateString()}
                    </td>
                    <td className="py-3">
                      <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                        Dividend
                      </span>
                    </td>
                    <td className="py-3 text-gray-300">
                      {dividend.type} Dividend Payment
                    </td>
                    <td className="py-3 text-gray-400">-</td>
                    <td className="py-3 text-green-400 font-semibold">
                      +${dividend.amount.toLocaleString()}
                    </td>
                    <td className="py-3">
                      <span className="px-2 py-1 bg-green-600 text-white rounded text-xs">
                        {dividend.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Export Options */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">📊 Export Options</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 bg-gray-700 rounded-lg hover:bg-gray-600 text-center">
            <div className="text-2xl mb-2">📄</div>
            <div className="text-white font-semibold">PDF Report</div>
            <div className="text-gray-400 text-sm">Complete transaction history</div>
          </button>
          <button className="p-4 bg-gray-700 rounded-lg hover:bg-gray-600 text-center">
            <div className="text-2xl mb-2">📊</div>
            <div className="text-white font-semibold">Excel Export</div>
            <div className="text-gray-400 text-sm">Spreadsheet format</div>
          </button>
          <button className="p-4 bg-gray-700 rounded-lg hover:bg-gray-600 text-center">
            <div className="text-2xl mb-2">🧾</div>
            <div className="text-white font-semibold">Tax Summary</div>
            <div className="text-gray-400 text-sm">For tax reporting</div>
          </button>
        </div>
      </div>
    </div>
  );

  const renderAnalyticsTab = () => (
    <div className="space-y-6">
      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">📈 Portfolio Performance</h4>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Total Return:</span>
              <span className={`text-xl font-bold ${portfolioSummary.unrealizedGainsPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {portfolioSummary.unrealizedGainsPercent.toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${portfolioSummary.unrealizedGainsPercent >= 0 ? 'bg-green-500' : 'bg-red-500'}`}
                style={{ width: `${Math.min(Math.abs(portfolioSummary.unrealizedGainsPercent), 100)}%` }}
              />
            </div>
            <div className="text-sm text-gray-400">
              {portfolioSummary.unrealizedGainsPercent >= 0 ? 'Profit' : 'Loss'}: ${Math.abs(portfolioSummary.unrealizedGains).toLocaleString()}
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h4 className="text-lg font-semibold text-white mb-4">💰 Dividend Analysis</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Dividend Yield on Cost:</span>
              <span className="text-green-400 font-semibold">
                {portfolioSummary.totalInvested > 0
                  ? ((portfolioSummary.projectedAnnualDividend / portfolioSummary.totalInvested) * 100).toFixed(1)
                  : 0}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Annual Projection:</span>
              <span className="text-green-400 font-semibold">
                ${portfolioSummary.projectedAnnualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Dividend per Share:</span>
              <span className="text-green-400 font-semibold">
                ${portfolioSummary.totalShares > 0
                  ? (portfolioSummary.projectedAnnualDividend / portfolioSummary.totalShares).toFixed(2)
                  : '0.00'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Payback Period:</span>
              <span className="text-white font-semibold">
                {portfolioSummary.projectedAnnualDividend > 0
                  ? (portfolioSummary.totalInvested / portfolioSummary.projectedAnnualDividend).toFixed(1)
                  : 'N/A'} years
              </span>
            </div>
          </div>
          <div className="mt-4 p-3 bg-gray-700 rounded-lg">
            <p className="text-gray-300 text-sm">
              <strong>Calculation Method:</strong> Based on 100% EBIT distribution from baseline mining operations
              (25ha, 1 plant, {DEFAULT_MINING_PARAMS.recoveryFactor}% recovery, ${DEFAULT_MINING_PARAMS.goldPriceUsdPerKg.toLocaleString()}/kg gold price).
            </p>
          </div>
        </div>
      </div>

      {/* Holdings Distribution */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">🥧 Holdings Distribution by Phase</h4>
        {holdings.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-4">📊</div>
            <p className="text-gray-400">No holdings to analyze yet.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {holdings.map((holding, index) => {
              const percentage = (holding.shares_purchased / portfolioSummary.totalShares) * 100;
              return (
                <div key={holding.id} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white font-semibold">{holding.phase_name}</span>
                    <span className="text-gray-400">
                      {holding.shares_purchased.toLocaleString()} shares ({percentage.toFixed(1)}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Market Comparison */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">🏆 Market Comparison</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">
              ${(109026).toLocaleString()}
            </div>
            <div className="text-sm text-gray-400">Gold Price/kg</div>
            <div className="text-xs text-green-400">+2.3% this month</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {portfolioSummary.unrealizedGainsPercent.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-400">Your Portfolio</div>
            <div className="text-xs text-gray-400">vs gold performance</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">
              {portfolioSummary.totalInvested > 0
                ? ((portfolioSummary.projectedAnnualDividend / portfolioSummary.totalInvested) * 100).toFixed(1)
                : 0}%
            </div>
            <div className="text-sm text-gray-400">Dividend Yield on Cost</div>
            <div className="text-xs text-green-400">vs 2.1% market avg</div>
          </div>
        </div>
      </div>

      {/* Investment Insights */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h4 className="text-lg font-semibold text-white mb-4">💡 Investment Insights</h4>
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <div className="text-green-400 text-xl">✅</div>
            <div>
              <div className="text-white font-semibold">Diversification Opportunity</div>
              <div className="text-sm text-gray-300">
                Consider spreading purchases across multiple phases to reduce average cost.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-blue-400 text-xl">📈</div>
            <div>
              <div className="text-white font-semibold">Growth Potential</div>
              <div className="text-sm text-gray-300">
                Early phase purchases historically show higher returns as operations expand.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-yellow-400 text-xl">💰</div>
            <div>
              <div className="text-white font-semibold">Dividend Reinvestment</div>
              <div className="text-sm text-gray-300">
                Reinvesting dividends can compound your returns over time.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-gray-900 rounded-lg border border-gray-700">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <h2 className="text-2xl font-bold text-white mb-2">📊 My Portfolio</h2>
        <p className="text-gray-400">
          Comprehensive view of your Aureus Africa share holdings and performance
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-700">
        {[
          { key: 'overview', label: '📊 Overview', icon: '📊' },
          { key: 'certificates', label: '📜 Certificates', icon: '📜' },
          { key: 'kyc', label: '🔐 KYC Verification', icon: '🔐' },
          { key: 'transactions', label: '📋 Transactions', icon: '📋' },
          { key: 'analytics', label: '📈 Analytics', icon: '📈' }
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 p-4 text-center font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-blue-600 text-white border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-gray-300 hover:bg-gray-800'
            }`}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {loading ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-lg mb-4">⏳</div>
            <p className="text-gray-400">Loading portfolio data...</p>
          </div>
        ) : (
          <>
            {activeTab === 'overview' && renderOverviewTab()}
            {activeTab === 'certificates' && renderCertificatesTab()}
            {activeTab === 'kyc' && renderKYCTab()}
            {activeTab === 'transactions' && renderTransactionsTab()}
            {activeTab === 'analytics' && renderAnalyticsTab()}
          </>
        )}
      </div>
    </div>
  );
};
