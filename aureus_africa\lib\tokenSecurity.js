import crypto from 'crypto';

/**
 * SECURE TOKEN GENERATION UTILITIES
 * 
 * This module provides cryptographically secure token generation
 * for all authentication and security purposes.
 */

class SecureTokenGenerator {
  /**
   * Generate a cryptographically secure authentication token
   * @param {number} bytes - Number of random bytes (default: 32 for 256-bit security)
   * @returns {string} - Hex-encoded secure token
   */
  static generateAuthToken(bytes = 32) {
    if (bytes < 16) {
      throw new Error('Token must be at least 16 bytes (128 bits) for security');
    }
    return crypto.randomBytes(bytes).toString('hex');
  }

  /**
   * Generate a secure web authentication token with prefix
   * @returns {string} - Prefixed secure token for web authentication
   */
  static generateWebAuthToken() {
    const randomPart = crypto.randomBytes(32).toString('hex');
    return `webauth_${randomPart}`;
  }

  /**
   * Generate a secure password reset token
   * @returns {string} - 64-character hex token for password reset
   */
  static generateResetToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate a secure verification token (shorter for user input)
   * @returns {string} - 32-character hex token for verification codes
   */
  static generateVerificationToken() {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Generate a secure session token
   * @returns {string} - 64-character hex token for session management
   */
  static generateSessionToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate a secure API key
   * @returns {string} - 96-character hex token for API authentication
   */
  static generateApiKey() {
    return crypto.randomBytes(48).toString('hex');
  }

  /**
   * Generate a secure CSRF token
   * @returns {string} - 64-character hex token for CSRF protection
   */
  static generateCSRFToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate a secure nonce for cryptographic operations
   * @param {number} bytes - Number of bytes (default: 16)
   * @returns {string} - Hex-encoded nonce
   */
  static generateNonce(bytes = 16) {
    return crypto.randomBytes(bytes).toString('hex');
  }

  /**
   * Check if a token has expired
   * @param {string|Date} expiresAt - Expiration timestamp
   * @returns {boolean} - True if token is expired
   */
  static isTokenExpired(expiresAt) {
    return new Date() > new Date(expiresAt);
  }

  /**
   * Create a token expiration timestamp
   * @param {number} minutes - Minutes until expiration (default: 10)
   * @returns {Date} - Expiration timestamp
   */
  static createTokenExpiry(minutes = 10) {
    return new Date(Date.now() + minutes * 60 * 1000);
  }

  /**
   * Validate token format and security
   * @param {string} token - Token to validate
   * @param {number} minLength - Minimum required length (default: 32)
   * @returns {object} - Validation result with details
   */
  static validateTokenSecurity(token, minLength = 32) {
    const validation = {
      valid: true,
      issues: [],
      securityLevel: 'unknown'
    };

    // Check if token exists
    if (!token || typeof token !== 'string') {
      validation.valid = false;
      validation.issues.push('Token is missing or not a string');
      validation.securityLevel = 'invalid';
      return validation;
    }

    // Check minimum length
    if (token.length < minLength) {
      validation.valid = false;
      validation.issues.push(`Token is too short (${token.length} chars, minimum ${minLength})`);
      validation.securityLevel = 'weak';
    }

    // Check for predictable patterns
    const predictablePatterns = [
      /^[0-9]+$/, // Only numbers
      /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/, // UUID format
      /^webauth_\d+$/, // Old predictable webauth format
      /^token_\d+$/, // Sequential token format
      /^[a-z]+$/, // Only lowercase letters
      /^[A-Z]+$/, // Only uppercase letters
    ];

    for (const pattern of predictablePatterns) {
      if (pattern.test(token)) {
        validation.valid = false;
        validation.issues.push('Token contains predictable patterns');
        validation.securityLevel = 'predictable';
        break;
      }
    }

    // Check entropy (simplified check for hex tokens)
    if (/^[a-f0-9]+$/.test(token)) {
      const uniqueChars = new Set(token.split('')).size;
      if (uniqueChars < 8) {
        validation.issues.push('Token has low entropy (few unique characters)');
        validation.securityLevel = 'low-entropy';
      }
    }

    // Determine security level if still valid
    if (validation.valid) {
      if (token.length >= 64) {
        validation.securityLevel = 'high';
      } else if (token.length >= 32) {
        validation.securityLevel = 'medium';
      } else {
        validation.securityLevel = 'low';
      }
    }

    return validation;
  }

  /**
   * Generate a time-based token with expiration embedded
   * @param {number} validityMinutes - Minutes until expiration
   * @returns {object} - Token and expiration info
   */
  static generateTimedToken(validityMinutes = 10) {
    const timestamp = Date.now();
    const expiresAt = new Date(timestamp + validityMinutes * 60 * 1000);
    const randomPart = crypto.randomBytes(24).toString('hex');
    
    // Embed timestamp in token for validation
    const token = `${timestamp.toString(36)}_${randomPart}`;
    
    return {
      token,
      expiresAt,
      validityMinutes
    };
  }

  /**
   * Validate a time-based token
   * @param {string} token - Time-based token to validate
   * @returns {object} - Validation result
   */
  static validateTimedToken(token) {
    try {
      const parts = token.split('_');
      if (parts.length !== 2) {
        return { valid: false, error: 'Invalid token format' };
      }

      const timestamp = parseInt(parts[0], 36);
      const currentTime = Date.now();
      
      if (isNaN(timestamp)) {
        return { valid: false, error: 'Invalid timestamp in token' };
      }

      // Check if token is expired (assuming 10 minute default)
      const tokenAge = currentTime - timestamp;
      if (tokenAge > 10 * 60 * 1000) {
        return { valid: false, error: 'Token has expired' };
      }

      return { 
        valid: true, 
        timestamp, 
        age: tokenAge,
        expiresAt: new Date(timestamp + 10 * 60 * 1000)
      };
    } catch (error) {
      return { valid: false, error: 'Token validation failed' };
    }
  }

  /**
   * Test the token generation system
   * @returns {Promise<boolean>} - True if all tests pass
   */
  static async testTokenGeneration() {
    console.log('🧪 Testing secure token generation...');

    try {
      // Test 1: Generate different types of tokens
      const authToken = this.generateAuthToken();
      const webAuthToken = this.generateWebAuthToken();
      const resetToken = this.generateResetToken();
      const sessionToken = this.generateSessionToken();

      console.log(`   Auth token: ${authToken.substring(0, 16)}... (${authToken.length} chars)`);
      console.log(`   WebAuth token: ${webAuthToken.substring(0, 24)}... (${webAuthToken.length} chars)`);
      console.log(`   Reset token: ${resetToken.substring(0, 16)}... (${resetToken.length} chars)`);
      console.log(`   Session token: ${sessionToken.substring(0, 16)}... (${sessionToken.length} chars)`);

      // Test 2: Ensure tokens are unique
      const tokens = [];
      for (let i = 0; i < 10; i++) {
        tokens.push(this.generateAuthToken());
      }

      const uniqueTokens = new Set(tokens);
      if (uniqueTokens.size !== tokens.length) {
        console.error('❌ Token uniqueness test failed');
        return false;
      }

      console.log('   ✓ Token uniqueness test passed');

      // Test 3: Validate token security
      const secureToken = this.generateAuthToken();
      const validation = this.validateTokenSecurity(secureToken);
      
      if (!validation.valid) {
        console.error('❌ Token security validation failed:', validation.issues);
        return false;
      }

      console.log(`   ✓ Token security validation passed (${validation.securityLevel} security)`);

      // Test 4: Test timed tokens
      const timedToken = this.generateTimedToken(5);
      const timedValidation = this.validateTimedToken(timedToken.token);
      
      if (!timedValidation.valid) {
        console.error('❌ Timed token test failed:', timedValidation.error);
        return false;
      }

      console.log('   ✓ Timed token test passed');

      console.log('✅ All token generation tests passed');
      return true;

    } catch (error) {
      console.error('❌ Token generation test failed:', error);
      return false;
    }
  }
}

export default SecureTokenGenerator;
