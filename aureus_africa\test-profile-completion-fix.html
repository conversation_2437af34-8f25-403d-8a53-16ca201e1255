<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Completion Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #4CAF50;
            color: white;
        }
        .error {
            background-color: #f44336;
            color: white;
        }
        .warning {
            background-color: #ff9800;
            color: white;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Profile Completion Fix Test</h1>
    <p>This test verifies that the profile completion redirect loop has been fixed.</p>

    <div class="test-section">
        <h2>Test 1: User Object Structure After Profile Completion</h2>
        <p>Testing that the user object has the correct flags after profile completion.</p>
        <button onclick="testUserObjectStructure()">Run Test</button>
        <div id="test1-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Dashboard Route Guard Logic</h2>
        <p>Testing that the dashboard route guard correctly allows access after profile completion.</p>
        <button onclick="testDashboardRouteGuard()">Run Test</button>
        <div id="test2-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Profile Completion Flow Simulation</h2>
        <p>Simulating the complete profile completion flow to ensure no redirect loops.</p>
        <button onclick="testCompleteFlow()">Run Test</button>
        <div id="test3-result"></div>
    </div>

    <div class="test-section">
        <h2>Test Results Summary</h2>
        <div id="summary-result"></div>
    </div>

    <script>
        let testResults = [];

        function addResult(testName, success, message) {
            testResults.push({ testName, success, message });
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('summary-result');
            const passed = testResults.filter(r => r.success).length;
            const total = testResults.length;
            
            summary.innerHTML = `
                <div class="test-result ${passed === total ? 'success' : 'error'}">
                    <strong>Tests Passed: ${passed}/${total}</strong>
                </div>
                ${testResults.map(r => `
                    <div class="test-result ${r.success ? 'success' : 'error'}">
                        ${r.testName}: ${r.message}
                    </div>
                `).join('')}
            `;
        }

        function testUserObjectStructure() {
            const resultDiv = document.getElementById('test1-result');
            
            try {
                // Simulate the user object created after profile completion
                const completedUserData = {
                    id: 'telegram_*********',
                    email: '<EMAIL>',
                    database_user: {
                        email: '<EMAIL>',
                        password_hash: 'hashed_password',
                        full_name: 'Test User',
                        phone: '+**********',
                        country_of_residence: 'US'
                    },
                    account_type: 'telegram_direct',
                    needsProfileCompletion: false,
                    user_metadata: {
                        telegram_id: '*********',
                        full_name: 'Test User',
                        username: 'testuser',
                        profile_completion_required: false
                    }
                };

                // Test the structure
                const hasCorrectFlags = 
                    completedUserData.needsProfileCompletion === false &&
                    completedUserData.user_metadata.profile_completion_required === false;

                const hasRequiredFields = 
                    completedUserData.database_user.email &&
                    completedUserData.database_user.password_hash &&
                    completedUserData.database_user.full_name &&
                    completedUserData.database_user.phone &&
                    completedUserData.database_user.country_of_residence;

                if (hasCorrectFlags && hasRequiredFields) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ User object structure is correct
                            <pre>${JSON.stringify(completedUserData, null, 2)}</pre>
                        </div>
                    `;
                    addResult('User Object Structure', true, 'All flags and fields are correct');
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ User object structure is incorrect
                            <p>Correct flags: ${hasCorrectFlags}</p>
                            <p>Required fields: ${hasRequiredFields}</p>
                        </div>
                    `;
                    addResult('User Object Structure', false, 'Missing flags or required fields');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Test failed: ${error.message}
                    </div>
                `;
                addResult('User Object Structure', false, `Error: ${error.message}`);
            }
        }

        function testDashboardRouteGuard() {
            const resultDiv = document.getElementById('test2-result');
            
            try {
                // Simulate the dashboard route guard logic
                function simulateRouteGuard(user) {
                    console.log('🔍 Dashboard route guard check:', {
                        needsProfileCompletion: user.needsProfileCompletion,
                        profile_completion_required: user.user_metadata?.profile_completion_required,
                        userEmail: user.email,
                        hasDatabase: !!user.database_user
                    });

                    if (user.needsProfileCompletion || user.user_metadata?.profile_completion_required) {
                        return { allowed: false, reason: 'Profile completion required' };
                    }

                    return { allowed: true, reason: 'Profile is complete' };
                }

                // Test with completed user
                const completedUser = {
                    email: '<EMAIL>',
                    needsProfileCompletion: false,
                    user_metadata: {
                        profile_completion_required: false
                    },
                    database_user: {
                        email: '<EMAIL>',
                        password_hash: 'hashed',
                        full_name: 'Test User',
                        phone: '+**********',
                        country_of_residence: 'US'
                    }
                };

                // Test with incomplete user
                const incompleteUser = {
                    email: '<EMAIL>',
                    needsProfileCompletion: true,
                    user_metadata: {
                        profile_completion_required: true
                    }
                };

                const completedResult = simulateRouteGuard(completedUser);
                const incompleteResult = simulateRouteGuard(incompleteUser);

                if (completedResult.allowed && !incompleteResult.allowed) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ Dashboard route guard works correctly
                            <p>Completed user: ${completedResult.reason}</p>
                            <p>Incomplete user: ${incompleteResult.reason}</p>
                        </div>
                    `;
                    addResult('Dashboard Route Guard', true, 'Correctly allows/blocks access');
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ Dashboard route guard logic is incorrect
                            <p>Completed user allowed: ${completedResult.allowed}</p>
                            <p>Incomplete user allowed: ${incompleteResult.allowed}</p>
                        </div>
                    `;
                    addResult('Dashboard Route Guard', false, 'Incorrect access control');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Test failed: ${error.message}
                    </div>
                `;
                addResult('Dashboard Route Guard', false, `Error: ${error.message}`);
            }
        }

        function testCompleteFlow() {
            const resultDiv = document.getElementById('test3-result');
            
            try {
                // Simulate the complete flow
                let currentSection = 'login';
                let user = null;

                // Step 1: User logs in with incomplete profile
                user = {
                    id: 'telegram_*********',
                    email: '<EMAIL>',
                    needsProfileCompletion: true,
                    user_metadata: {
                        profile_completion_required: true
                    }
                };
                currentSection = 'profile-completion';

                // Step 2: User completes profile (simulate handleProfileComplete)
                const completedUserData = {
                    id: 'telegram_*********',
                    email: '<EMAIL>',
                    database_user: {
                        email: '<EMAIL>',
                        password_hash: 'hashed',
                        full_name: 'Test User',
                        phone: '+**********',
                        country_of_residence: 'US'
                    },
                    account_type: 'telegram_direct',
                    needsProfileCompletion: false,
                    user_metadata: {
                        profile_completion_required: false
                    }
                };

                // Apply the fix from handleProfileComplete
                user = {
                    ...completedUserData,
                    needsProfileCompletion: false,
                    user_metadata: {
                        ...completedUserData.user_metadata,
                        profile_completion_required: false
                    }
                };
                currentSection = 'dashboard';

                // Step 3: Check if dashboard route guard allows access
                const shouldRedirect = user.needsProfileCompletion || user.user_metadata?.profile_completion_required;

                if (!shouldRedirect && currentSection === 'dashboard') {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ Complete flow works correctly
                            <p>User successfully reaches dashboard without redirect loop</p>
                            <p>Final section: ${currentSection}</p>
                            <p>Profile completion flags cleared: ✅</p>
                        </div>
                    `;
                    addResult('Complete Flow', true, 'No redirect loop detected');
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ Redirect loop detected
                            <p>Should redirect: ${shouldRedirect}</p>
                            <p>Current section: ${currentSection}</p>
                            <p>needsProfileCompletion: ${user.needsProfileCompletion}</p>
                            <p>profile_completion_required: ${user.user_metadata?.profile_completion_required}</p>
                        </div>
                    `;
                    addResult('Complete Flow', false, 'Redirect loop still exists');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Test failed: ${error.message}
                    </div>
                `;
                addResult('Complete Flow', false, `Error: ${error.message}`);
            }
        }

        // Auto-run all tests on page load
        window.onload = function() {
            setTimeout(() => {
                testUserObjectStructure();
                setTimeout(() => {
                    testDashboardRouteGuard();
                    setTimeout(() => {
                        testCompleteFlow();
                    }, 500);
                }, 500);
            }, 1000);
        };
    </script>
</body>
</html>
