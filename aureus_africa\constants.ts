import { CalculatorInputs } from './types';

export const PLANT_CAPACITY_TPH = 200;
export const EFFECTIVE_HOURS_PER_DAY = 20;
export const OPERATING_DAYS_PER_YEAR = 330;
export const BULK_DENSITY_T_PER_M3 = 1.8;
export const HA_PER_PLANT = 25;
export const TOTAL_SHARES = 1400000;
export const PROJECT_TOTAL_HA = 250;

export const DEFAULT_VALUES: CalculatorInputs = {
  landHa: 25, // Base land size - will be overridden by expansion plan
  avgGravelThickness: 0.8,
  inSituGrade: 0.9,
  recoveryFactor: 70, // as percentage
  goldPriceUsdPerKg: 109026, // Current market price
  opexPercent: 45, // as percentage
  userShares: 1, // Start with 1 share
  dividendPayoutPercent: 50, // as percentage
};

// Updated land size options - now up to 5,000 hectares (200 options × 25 ha)
export const LAND_SIZE_OPTIONS = Array.from({ length: 200 }, (_, i) => (i + 1) * 25);

// 5-Year Plant Expansion Plan (June targets)
export const EXPANSION_PLAN = {
  2026: { plants: 10, hectares: 250, month: 'June' },
  2027: { plants: 25, hectares: 625, month: 'June' },
  2028: { plants: 50, hectares: 1250, month: 'June' },
  2029: { plants: 100, hectares: 2500, month: 'June' },
  2030: { plants: 200, hectares: 5000, month: 'June' }
};

export const EXPANSION_YEARS = Object.keys(EXPANSION_PLAN).map(Number);

// Helper function to get expansion plan details
export const getExpansionPlanForYear = (year: number) => {
  return EXPANSION_PLAN[year as keyof typeof EXPANSION_PLAN] || null;
};