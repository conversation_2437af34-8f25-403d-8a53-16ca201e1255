/**
 * API endpoint for sending verification emails
 * Uses Resend service on the server side for security
 */

import { Resend } from 'resend';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const RESEND_API_KEY = process.env.RESEND_API_KEY;
const RESEND_FROM_EMAIL = process.env.RESEND_FROM_EMAIL || '<EMAIL>';
const RESEND_FROM_NAME = process.env.RESEND_FROM_NAME || 'Aureus Alliance Holdings';

// Initialize Resend client
let resend = null;

try {
  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    console.log('✅ Server-side Resend email service initialized');
  } else {
    console.warn('⚠️ RESEND_API_KEY not found - email service disabled');
  }
} catch (error) {
  console.error('❌ Failed to initialize server-side Resend service:', error);
}

/**
 * Generate verification email content
 */
function generateVerificationEmailContent(code, purpose, userName, expiryMinutes = 15) {
  const greeting = userName ? `Hello ${userName}` : 'Hello';
  const purposeText = {
    registration: 'complete your account registration',
    account_update: 'confirm your account changes',
    withdrawal: 'authorize your withdrawal request',
    password_reset: 'reset your password'
  }[purpose] || 'verify your email';

  const subject = `Your Aureus Alliance verification code: ${code}`;

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Email Verification</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #D4AF37;">Aureus Alliance Holdings</h1>
          </div>
          
          <h2>Email Verification Required</h2>
          
          <p>${greeting},</p>
          
          <p>You need to verify your email address to ${purposeText}.</p>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;">
            <h3 style="margin: 0; color: #D4AF37;">Your Verification Code</h3>
            <div style="font-size: 32px; font-weight: bold; letter-spacing: 8px; margin: 15px 0; color: #333;">
              ${code}
            </div>
            <p style="margin: 0; color: #666; font-size: 14px;">
              This code expires in ${expiryMinutes} minutes
            </p>
          </div>
          
          <p><strong>Security Notice:</strong></p>
          <ul>
            <li>Never share this code with anyone</li>
            <li>Aureus Alliance will never ask for this code via phone or email</li>
            <li>If you didn't request this verification, please ignore this email</li>
          </ul>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          
          <p style="font-size: 12px; color: #666; text-align: center;">
            This email was sent by Aureus Alliance Holdings. If you have questions, 
            please contact our support team.
          </p>
        </div>
      </body>
    </html>
  `;

  const text = `
    Aureus Alliance Holdings - Email Verification
    
    ${greeting},
    
    You need to verify your email address to ${purposeText}.
    
    Your verification code: ${code}
    
    This code expires in ${expiryMinutes} minutes.
    
    Security Notice:
    - Never share this code with anyone
    - Aureus Alliance will never ask for this code via phone or email
    - If you didn't request this verification, please ignore this email
    
    This email was sent by Aureus Alliance Holdings.
  `;

  return { subject, html, text };
}

/**
 * API handler for sending verification emails
 */
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Check if email service is configured
  if (!resend || !RESEND_API_KEY) {
    console.error('❌ Email service not configured');
    return res.status(500).json({ error: 'Email service not configured' });
  }

  try {
    const { email, code, purpose = 'registration', userName, expiryMinutes = 15 } = req.body;

    // Validate required fields
    if (!email || !code) {
      return res.status(400).json({ error: 'Email and code are required' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    console.log(`📧 Sending verification email to ${email} for ${purpose}`);

    // Generate email content
    const emailContent = generateVerificationEmailContent(code, purpose, userName, expiryMinutes);

    // Send email via Resend
    const result = await resend.emails.send({
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [email],
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
      tags: [
        { name: 'category', value: 'verification' },
        { name: 'purpose', value: purpose }
      ]
    });

    if (result.error) {
      console.error('❌ Resend API error:', result.error);
      return res.status(500).json({ error: result.error.message });
    }

    console.log(`✅ Verification email sent successfully to ${email}`);
    
    return res.status(200).json({ 
      success: true, 
      messageId: result.data?.id,
      message: 'Verification email sent successfully'
    });

  } catch (error) {
    console.error('❌ Error sending verification email:', error);
    return res.status(500).json({ 
      error: error.message || 'Failed to send verification email' 
    });
  }
}
