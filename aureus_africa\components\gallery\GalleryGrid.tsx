import React, { useState } from 'react';
import type { GalleryGridProps, GalleryImage } from '../../types/gallery';

export const GalleryGrid: React.FC<GalleryGridProps> = ({
  images,
  onImageClick,
  columns = 3,
  showOverlay = true,
  showCategories = true
}) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [failedImages, setFailedImages] = useState<Set<string>>(new Set());

  const handleImageLoad = (imageId: string) => {
    setLoadedImages(prev => new Set([...prev, imageId]));
  };

  const handleImageError = (imageId: string) => {
    setFailedImages(prev => new Set([...prev, imageId]));
  };

  // Using CSS Grid for better responsive behavior
  const getGridClasses = () => {
    return 'gallery-grid-container';
  };

  if (images.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl text-gray-300 dark:text-gray-600 mb-4">📸</div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No images found
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Try adjusting your filters or check back later.
        </p>
      </div>
    );
  }

  return (
    <div className={getGridClasses()}>
      {images.map((image, index) => (
        <div
          key={image.id}
          className="gallery-image-item"
          style={{ animationDelay: `${index * 0.1}s` }}
        >
          <GalleryImageCard
            image={image}
            onImageClick={onImageClick}
            showOverlay={showOverlay}
            showCategory={showCategories}
            isLoaded={loadedImages.has(image.id)}
            hasFailed={failedImages.has(image.id)}
            onLoad={() => handleImageLoad(image.id)}
            onError={() => handleImageError(image.id)}
          />
        </div>
      ))}
    </div>
  );
};

interface GalleryImageCardProps {
  image: GalleryImage;
  onImageClick?: (image: GalleryImage) => void;
  showOverlay: boolean;
  showCategory: boolean;
  isLoaded: boolean;
  hasFailed: boolean;
  onLoad: () => void;
  onError: () => void;
}

const GalleryImageCard: React.FC<GalleryImageCardProps> = ({
  image,
  onImageClick,
  showOverlay,
  showCategory,
  isLoaded,
  hasFailed,
  onLoad,
  onError
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (onImageClick && !hasFailed) {
      onImageClick(image);
    }
  };

  return (
    <div
      className={`
        group relative overflow-hidden rounded-lg shadow-xl bg-gray-800 border border-gray-700 gallery-image-card
        ${onImageClick && !hasFailed ? 'cursor-pointer hover:border-amber-500/50' : ''}
        ${isLoaded ? 'gallery-image-fade-in' : ''}
        transition-all duration-300 hover:shadow-2xl hover:shadow-amber-500/10
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
    >
      {/* Image Container */}
      <div className="relative aspect-w-16 aspect-h-12 bg-gray-800">
        {/* Loading Skeleton */}
        {!isLoaded && !hasFailed && (
          <div className="absolute inset-0 bg-gray-800 gallery-loading-shimmer">
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500">
                <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        )}

        {/* Error State */}
        {hasFailed && (
          <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-xs">Failed to load</p>
            </div>
          </div>
        )}

        {/* Actual Image */}
        <img
          src={image.image_url}
          alt={image.alt_text || image.title}
          className={`
            w-full h-full object-cover transition-all duration-500 ease-in-out
            ${isLoaded ? 'opacity-100' : 'opacity-0'}
            ${isHovered ? 'transform scale-110' : 'transform scale-100'}
          `}
          onLoad={onLoad}
          onError={onError}
          loading="lazy"
        />




      </div>


    </div>
  );
};

// CSS classes for line clamping (add to your global CSS)
const styles = `
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
`;
