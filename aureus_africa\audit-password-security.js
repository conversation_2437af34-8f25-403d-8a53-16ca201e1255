#!/usr/bin/env node

/**
 * PASSWORD SECURITY AUDIT & VERIFICATION
 * 
 * This script audits the current password security implementation
 * and identifies any vulnerable password hashes that need migration.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Import password security functions directly
const hashPassword = async (password) => {
  const bcrypt = await import('bcryptjs');
  const saltRounds = 12;
  return await bcrypt.default.hash(password, saltRounds);
};

const verifyPassword = async (password, hash) => {
  const bcrypt = await import('bcryptjs');
  return await bcrypt.default.compare(password, hash);
};

const isOldHashFormat = (hash) => {
  return hash && hash.length === 64 && /^[a-f0-9]+$/.test(hash);
};

const isBcryptHash = (hash) => {
  return hash && /^\$2[aby]\$/.test(hash);
};

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

class PasswordSecurityAuditor {
  constructor() {
    this.auditResults = {
      totalUsers: 0,
      bcryptHashes: 0,
      vulnerableHashes: 0,
      nullHashes: 0,
      unknownHashes: 0,
      vulnerableUsers: [],
      migrationNeeded: false
    };
  }

  async runPasswordSecurityAudit() {
    console.log('🔐 PASSWORD SECURITY AUDIT');
    console.log('==========================\n');
    console.log('🔍 Analyzing password hash security across all users');
    console.log('🛡️ Identifying vulnerable SHA-256 hashes with static salt');
    console.log('✅ Verifying bcrypt implementation\n');

    try {
      await this.auditExistingPasswords();
      await this.testPasswordSystem();
      await this.generateMigrationPlan();
      
      this.generateAuditReport();
      
    } catch (error) {
      console.error('❌ Password security audit failed:', error);
    }
  }

  async auditExistingPasswords() {
    console.log('📊 AUDITING EXISTING PASSWORD HASHES');
    console.log('====================================');

    try {
      // Get all users with password hashes
      const { data: users, error } = await supabase
        .from('users')
        .select('id, username, email, password_hash, created_at')
        .not('password_hash', 'is', null);

      if (error) {
        throw new Error(`Failed to fetch users: ${error.message}`);
      }

      this.auditResults.totalUsers = users.length;
      console.log(`📋 Found ${users.length} users with password hashes`);

      for (const user of users) {
        const hash = user.password_hash;
        
        if (!hash) {
          this.auditResults.nullHashes++;
          continue;
        }

        if (isBcryptHash(hash)) {
          this.auditResults.bcryptHashes++;
          console.log(`   ✅ User ${user.username || user.id}: Secure bcrypt hash`);
        } else if (isOldHashFormat(hash)) {
          this.auditResults.vulnerableHashes++;
          this.auditResults.vulnerableUsers.push({
            id: user.id,
            username: user.username,
            email: user.email,
            created_at: user.created_at,
            hashType: 'SHA-256 (VULNERABLE)'
          });
          console.log(`   ❌ User ${user.username || user.id}: VULNERABLE SHA-256 hash`);
        } else {
          this.auditResults.unknownHashes++;
          console.log(`   ⚠️ User ${user.username || user.id}: Unknown hash format (${hash.substring(0, 20)}...)`);
        }
      }

      if (this.auditResults.vulnerableHashes > 0) {
        this.auditResults.migrationNeeded = true;
        console.log(`\n🚨 CRITICAL: ${this.auditResults.vulnerableHashes} users have vulnerable password hashes!`);
      } else {
        console.log(`\n✅ All password hashes are secure (bcrypt)`);
      }

    } catch (error) {
      console.error('❌ Password audit failed:', error.message);
    }
  }

  async testPasswordSystem() {
    console.log('\n🧪 TESTING PASSWORD SYSTEM');
    console.log('==========================');

    try {
      console.log('🔧 Testing bcrypt implementation...');
      
      const testPassword = 'TestSecurePassword123!';
      
      // Test hashing
      console.log('   📝 Testing password hashing...');
      const hash1 = await hashPassword(testPassword);
      const hash2 = await hashPassword(testPassword);
      
      // Verify hashes are different (different salts)
      if (hash1 === hash2) {
        console.log('   ❌ CRITICAL: Identical hashes detected (salt issue)');
        return false;
      } else {
        console.log('   ✅ Hash uniqueness verified (different salts)');
      }

      // Test verification
      console.log('   🔍 Testing password verification...');
      const valid1 = await verifyPassword(testPassword, hash1);
      const valid2 = await verifyPassword(testPassword, hash2);
      const invalid = await verifyPassword('WrongPassword123!', hash1);

      if (valid1 && valid2 && !invalid) {
        console.log('   ✅ Password verification working correctly');
      } else {
        console.log('   ❌ Password verification failed');
        return false;
      }

      // Test hash format detection
      console.log('   🔍 Testing hash format detection...');
      if (isBcryptHash(hash1) && isBcryptHash(hash2)) {
        console.log('   ✅ Hash format detection working');
      } else {
        console.log('   ❌ Hash format detection failed');
        return false;
      }

      console.log('✅ Password system test PASSED');
      return true;

    } catch (error) {
      console.error('❌ Password system test failed:', error.message);
      return false;
    }
  }

  async generateMigrationPlan() {
    console.log('\n📋 GENERATING MIGRATION PLAN');
    console.log('============================');

    if (this.auditResults.vulnerableHashes === 0) {
      console.log('✅ No migration needed - all passwords are secure');
      return;
    }

    console.log(`🔧 Migration required for ${this.auditResults.vulnerableHashes} users`);
    console.log('\n📝 MIGRATION STRATEGY:');
    console.log('1. Force password reset for vulnerable users');
    console.log('2. Send secure password reset emails');
    console.log('3. Temporarily disable vulnerable accounts');
    console.log('4. Log all migration activities');

    // Create migration SQL script
    const migrationScript = `
-- Password Security Migration Script
-- Generated: ${new Date().toISOString()}

-- Step 1: Identify vulnerable users
SELECT 
  id, 
  username, 
  email, 
  LENGTH(password_hash) as hash_length,
  CASE 
    WHEN LENGTH(password_hash) = 64 AND password_hash ~ '^[a-f0-9]+$' THEN 'VULNERABLE_SHA256'
    WHEN password_hash ~ '^\\$2[aby]\\$' THEN 'SECURE_BCRYPT'
    ELSE 'UNKNOWN'
  END as hash_type
FROM users 
WHERE password_hash IS NOT NULL;

-- Step 2: Mark vulnerable accounts for password reset
UPDATE users 
SET 
  password_reset_required = true,
  password_reset_reason = 'Security upgrade - vulnerable hash detected',
  updated_at = NOW()
WHERE 
  LENGTH(password_hash) = 64 
  AND password_hash ~ '^[a-f0-9]+$';

-- Step 3: Log migration activity
INSERT INTO admin_audit_logs (
  admin_email,
  action,
  target_type,
  target_id,
  metadata,
  created_at
) VALUES (
  'security_system',
  'PASSWORD_MIGRATION_INITIATED',
  'user_security',
  'bulk_migration',
  jsonb_build_object(
    'vulnerable_users', ${this.auditResults.vulnerableHashes},
    'migration_date', NOW(),
    'reason', 'SHA-256 to bcrypt migration'
  ),
  NOW()
);
`;

    // Save migration script
    console.log('\n💾 Saving migration script...');
    try {
      const fs = await import('fs');
      fs.writeFileSync('password-migration-script.sql', migrationScript);
      console.log('✅ Migration script saved: password-migration-script.sql');
    } catch (error) {
      console.log('⚠️ Could not save migration script:', error.message);
    }
  }

  generateAuditReport() {
    console.log('\n📊 PASSWORD SECURITY AUDIT REPORT');
    console.log('==================================');
    
    console.log(`📈 STATISTICS:`);
    console.log(`   Total Users: ${this.auditResults.totalUsers}`);
    console.log(`   Secure (bcrypt): ${this.auditResults.bcryptHashes}`);
    console.log(`   Vulnerable (SHA-256): ${this.auditResults.vulnerableHashes}`);
    console.log(`   Null Hashes: ${this.auditResults.nullHashes}`);
    console.log(`   Unknown Format: ${this.auditResults.unknownHashes}`);

    const securityScore = this.auditResults.totalUsers > 0 ? 
      ((this.auditResults.bcryptHashes / this.auditResults.totalUsers) * 100).toFixed(1) : 0;
    
    console.log(`\n🎯 PASSWORD SECURITY SCORE: ${securityScore}%`);

    if (securityScore >= 95) {
      console.log('✅ EXCELLENT: Password security is highly secure');
    } else if (securityScore >= 80) {
      console.log('✅ GOOD: Password security is mostly secure');
    } else if (securityScore >= 60) {
      console.log('⚠️ FAIR: Password security needs improvement');
    } else {
      console.log('❌ POOR: Password security is critically vulnerable');
    }

    if (this.auditResults.vulnerableUsers.length > 0) {
      console.log('\n🚨 VULNERABLE USERS REQUIRING MIGRATION:');
      console.log('========================================');
      
      this.auditResults.vulnerableUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.username || 'Unknown'} (${user.email})`);
        console.log(`   User ID: ${user.id}`);
        console.log(`   Hash Type: ${user.hashType}`);
        console.log(`   Created: ${new Date(user.created_at).toLocaleDateString()}`);
        console.log('');
      });

      console.log('🔧 IMMEDIATE ACTIONS REQUIRED:');
      console.log('1. Run password migration script');
      console.log('2. Force password reset for vulnerable users');
      console.log('3. Send security notification emails');
      console.log('4. Monitor for successful password updates');
    }

    console.log('\n🛡️ SECURITY RECOMMENDATIONS:');
    console.log('=============================');
    console.log('✅ bcrypt implementation is active and working');
    console.log('✅ Password strength validation is implemented');
    console.log('✅ Hash format detection is working');
    
    if (this.auditResults.migrationNeeded) {
      console.log('🚨 CRITICAL: Execute password migration immediately');
      console.log('⚠️ Vulnerable users are at risk of account compromise');
    } else {
      console.log('✅ No immediate migration required');
    }

    console.log('\n📋 NEXT STEPS:');
    if (this.auditResults.migrationNeeded) {
      console.log('1. Execute password migration script');
      console.log('2. Implement forced password reset flow');
      console.log('3. Monitor migration progress');
      console.log('4. Verify all users have secure hashes');
    } else {
      console.log('1. Continue monitoring password security');
      console.log('2. Ensure all new registrations use bcrypt');
      console.log('3. Regular security audits');
    }

    // Log audit results to database
    this.logAuditResults();
  }

  async logAuditResults() {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'security_system',
          action: 'PASSWORD_SECURITY_AUDIT',
          target_type: 'user_security',
          target_id: 'all_users',
          metadata: {
            audit_date: new Date().toISOString(),
            total_users: this.auditResults.totalUsers,
            secure_hashes: this.auditResults.bcryptHashes,
            vulnerable_hashes: this.auditResults.vulnerableHashes,
            security_score: this.auditResults.totalUsers > 0 ? 
              ((this.auditResults.bcryptHashes / this.auditResults.totalUsers) * 100) : 0,
            migration_needed: this.auditResults.migrationNeeded
          },
          created_at: new Date().toISOString()
        });
      
      console.log('\n📋 Audit results logged to database');
    } catch (error) {
      console.log('\n⚠️ Could not log audit results:', error.message);
    }
  }
}

// Run the password security audit
const auditor = new PasswordSecurityAuditor();
auditor.runPasswordSecurityAudit().catch(console.error);
