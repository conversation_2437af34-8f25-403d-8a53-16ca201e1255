import React, { createContext, useContext, ReactNode } from 'react';
import { useSiteContent } from '../hooks/useSiteContent';

interface SiteContentContextType {
  content: Record<string, any>;
  loading: boolean;
  error: string | null;
  getContent: (section: string, key: string, defaultValue?: any) => any;
  refreshContent: () => Promise<void>;
}

const SiteContentContext = createContext<SiteContentContextType | undefined>(undefined);

interface SiteContentProviderProps {
  children: ReactNode;
}

export const SiteContentProvider: React.FC<SiteContentProviderProps> = ({ children }) => {
  const siteContent = useSiteContent();

  // Don't block rendering if there's an error loading content
  return (
    <SiteContentContext.Provider value={siteContent}>
      {children}
    </SiteContentContext.Provider>
  );
};

export const useSiteContentContext = () => {
  const context = useContext(SiteContentContext);
  if (context === undefined) {
    // Return a fallback context instead of throwing an error
    console.warn('useSiteContentContext used outside of SiteContentProvider, using fallback');
    return {
      content: {},
      loading: false,
      error: null,
      getContent: (section: string, key: string, defaultValue: any = '') => defaultValue,
      refreshContent: async () => {}
    };
  }
  return context;
};

// Helper hook for easy content access
export const useContent = (section: string, key: string, defaultValue: any = '') => {
  try {
    const { getContent } = useSiteContentContext();
    return getContent(section, key, defaultValue);
  } catch (error) {
    console.warn('Error accessing site content:', error);
    return defaultValue;
  }
};
