import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseKey);

// Hash password using the same method as the web app
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'aureus_salt_2024');
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

async function fixAdminPassword() {
  try {
    console.log('🔧 Fixing admin password hash...');
    
    const email = '<EMAIL>';
    const password = 'admin123';
    
    // Generate the correct password hash
    const correctHash = await hashPassword(password);
    console.log('✅ Generated password hash:', correctHash);
    
    // Update the user's password hash in the database
    const { data, error } = await supabase
      .from('users')
      .update({ 
        password_hash: correctHash,
        updated_at: new Date().toISOString()
      })
      .eq('email', email)
      .select();
    
    if (error) {
      console.error('❌ Error updating password hash:', error);
      return;
    }
    
    console.log('✅ Password hash updated successfully for:', email);
    console.log('📋 Updated user data:', data);
    
    // Verify the hash works
    const testHash = await hashPassword(password);
    if (testHash === correctHash) {
      console.log('✅ Password hash verification successful!');
      console.log('🎉 You can now login with:');
      console.log('   Email:', email);
      console.log('   Password:', password);
    } else {
      console.error('❌ Password hash verification failed!');
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the fix
fixAdminPassword();
