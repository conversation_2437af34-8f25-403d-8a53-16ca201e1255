# 3.1 Environment Setup - Aureus Alliance Web Dashboard

## Executive Summary
This document outlines the complete development environment setup for the Aureus Alliance Web Dashboard, including Next.js 14+ project initialization, development toolchain configuration, CI/CD pipeline setup, testing frameworks, code quality tools, and environment management strategies.

## Development Environment Architecture

### Technology Stack Configuration
```typescript
// Project Technology Stack
interface TechStack {
  // Frontend Framework
  framework: {
    name: 'Next.js';
    version: '14.2.0+';
    features: ['App Router', 'Server Components', 'TypeScript'];
  };
  
  // Runtime Environment
  runtime: {
    node: '18.17.0+';
    packageManager: 'npm' | 'yarn' | 'pnpm';
    environment: 'development' | 'staging' | 'production';
  };
  
  // Development Tools
  devTools: {
    typescript: '5.0+';
    eslint: '8.0+';
    prettier: '3.0+';
    husky: '8.0+';
    commitlint: '17.0+';
  };
  
  // Testing Framework
  testing: {
    unit: 'Jest + React Testing Library';
    e2e: 'Playwright';
    integration: 'Supertest';
    coverage: 'NYC/Istanbul';
  };
  
  // Build & Deployment
  deployment: {
    platform: 'Vercel';
    domain: 'aureus.africa';
    cdn: 'Vercel Edge Network';
    monitoring: 'Vercel Analytics + Sentry';
  };
}
```

## Project Initialization

### Next.js 14+ Project Setup
```bash
# Create Next.js project with TypeScript and App Router
npx create-next-app@latest aureus-alliance-dashboard \
  --typescript \
  --tailwind \
  --eslint \
  --app \
  --src-dir \
  --import-alias "@/*"

# Navigate to project directory
cd aureus-alliance-dashboard

# Install additional dependencies
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
npm install zustand
npm install zod
npm install react-hook-form @hookform/resolvers
npm install lucide-react
npm install recharts
npm install date-fns
npm install clsx tailwind-merge
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select
npm install @radix-ui/react-toast @radix-ui/react-tooltip @radix-ui/react-tabs

# Development dependencies
npm install -D @types/node @types/react @types/react-dom
npm install -D prettier prettier-plugin-tailwindcss
npm install -D husky lint-staged @commitlint/cli @commitlint/config-conventional
npm install -D jest @testing-library/react @testing-library/jest-dom
npm install -D @playwright/test
npm install -D cypress
npm install -D @storybook/nextjs
```

### Package.json Configuration
```json
{
  "name": "aureus-alliance-dashboard",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:cypress": "cypress open",
    "test:cypress:headless": "cypress run",
    "storybook": "storybook dev -p 6006",
    "build-storybook": "storybook build",
    "prepare": "husky install",
    "commit": "git-cz",
    "pre-commit": "lint-staged",
    "validate": "npm run type-check && npm run lint && npm run test",
    "clean": "rm -rf .next out dist coverage",
    "analyze": "ANALYZE=true npm run build"
  },
  "dependencies": {
    "next": "14.2.5",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "@supabase/supabase-js": "^2.39.0",
    "@supabase/auth-helpers-nextjs": "^0.10.0",
    "zustand": "^4.5.0",
    "zod": "^3.22.4",
    "react-hook-form": "^7.48.2",
    "@hookform/resolvers": "^3.3.2",
    "lucide-react": "^0.302.0",
    "recharts": "^2.8.0",
    "date-fns": "^3.0.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.2.0",
    "@radix-ui/react-dialog": "^1.0.5",
    "@radix-ui/react-dropdown-menu": "^2.0.6",
    "@radix-ui/react-select": "^2.0.0",
    "@radix-ui/react-toast": "^1.1.5",
    "@radix-ui/react-tooltip": "^1.0.7",
    "@radix-ui/react-tabs": "^1.0.4"
  },
  "devDependencies": {
    "typescript": "^5.3.3",
    "@types/node": "^20.10.6",
    "@types/react": "^18.2.46",
    "@types/react-dom": "^18.2.18",
    "eslint": "^8.56.0",
    "eslint-config-next": "14.0.4",
    "prettier": "^3.1.1",
    "prettier-plugin-tailwindcss": "^0.5.9",
    "husky": "^8.0.3",
    "lint-staged": "^15.2.0",
    "@commitlint/cli": "^18.4.4",
    "@commitlint/config-conventional": "^18.4.4",
    "jest": "^29.7.0",
    "@testing-library/react": "^14.1.2",
    "@testing-library/jest-dom": "^6.1.6",
    "@playwright/test": "^1.40.1",
    "cypress": "^13.6.2",
    "@storybook/nextjs": "^7.6.6",
    "tailwindcss": "^3.4.0",
    "autoprefixer": "^10.4.16",
    "postcss": "^8.4.32"
  },
  "engines": {
    "node": ">=18.17.0",
    "npm": ">=9.0.0"
  }
}
```

## Version Control and Branching Strategy

### Git Configuration
```bash
# Initialize git repository
git init

# Set up remote repository
git remote add origin https://github.com/JPRademeyer84/aureus_africa.git

# Create .gitignore
cat > .gitignore << 'EOF'
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Next.js build output
.next/
out/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/
cypress/videos/
cypress/screenshots/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# ESLint cache
.eslintcache

# Storybook build outputs
storybook-static/

# Temporary folders
tmp/
temp/

# Vercel
.vercel

# Turbo
.turbo
EOF
```

### Branching Strategy (GitFlow)
```yaml
# Git Branching Strategy
branching_strategy:
  main_branches:
    - name: main
      purpose: Production-ready code
      protection: 
        - require_pull_request
        - require_status_checks
        - restrict_pushes
    
    - name: develop
      purpose: Integration branch for features
      protection:
        - require_pull_request
        - require_status_checks
  
  feature_branches:
    naming: feature/TASK-123-description
    base: develop
    merge_target: develop
    lifecycle: temporary
  
  release_branches:
    naming: release/v1.0.0
    base: develop
    merge_target: [main, develop]
    lifecycle: temporary
  
  hotfix_branches:
    naming: hotfix/v1.0.1-critical-fix
    base: main
    merge_target: [main, develop]
    lifecycle: temporary

# Branch Protection Rules
protection_rules:
  main:
    - required_status_checks: [build, test, security-scan]
    - required_reviewers: 2
    - dismiss_stale_reviews: true
    - require_code_owner_reviews: true
    - restrict_pushes_to_admin: true
  
  develop:
    - required_status_checks: [build, test]
    - required_reviewers: 1
    - dismiss_stale_reviews: true
```

### Commit Convention Setup
```bash
# Install commitizen for standardized commits
npm install -g commitizen cz-conventional-changelog

# Configure commitizen
echo '{ "path": "cz-conventional-changelog" }' > ~/.czrc

# Set up commitlint configuration
cat > .commitlintrc.js << 'EOF'
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',     // New feature
        'fix',      // Bug fix
        'docs',     // Documentation
        'style',    // Formatting, missing semi colons, etc
        'refactor', // Code change that neither fixes a bug nor adds a feature
        'perf',     // Performance improvement
        'test',     // Adding missing tests
        'chore',    // Maintain
        'revert',   // Revert to previous commit
        'build',    // Build system changes
        'ci'        // CI/CD changes
      ]
    ],
    'subject-case': [2, 'always', 'sentence-case'],
    'subject-max-length': [2, 'always', 72],
    'body-max-line-length': [2, 'always', 100]
  }
};
EOF
```

## CI/CD Pipeline Configuration

### GitHub Actions Workflow
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18.17.0'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  # Code Quality Checks
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Type checking
        run: npm run type-check

      - name: Linting
        run: npm run lint

      - name: Format checking
        run: npm run format:check

      - name: Security audit
        run: npm audit --audit-level=moderate

  # Unit and Integration Tests
  test:
    name: Testing
    runs-on: ubuntu-latest
    needs: quality
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/lcov.info

  # End-to-End Tests
  e2e-tests:
    name: E2E Testing
    runs-on: ubuntu-latest
    needs: [quality, test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Start application
        run: npm start &

      - name: Wait for application
        run: npx wait-on http://localhost:3000

      - name: Run Playwright tests
        run: npm run test:e2e

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: quality
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: Run CodeQL analysis
        uses: github/codeql-action/init@v2
        with:
          languages: typescript

      - name: Perform CodeQL analysis
        uses: github/codeql-action/analyze@v2

  # Build and Deploy
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, e2e-tests, security]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel environment
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build project artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy to Vercel
        run: vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, e2e-tests, security]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel environment
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build project artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}

      - name: Post-deployment tests
        run: |
          sleep 30
          curl -f https://aureus.africa/api/health || exit 1
```

### Vercel Configuration
```json
{
  "version": 2,
  "name": "aureus-alliance-dashboard",
  "build": {
    "env": {
      "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
      "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
      "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-key",
      "TELEGRAM_BOT_TOKEN": "@telegram-bot-token",
      "JWT_SECRET": "@jwt-secret",
      "ENCRYPTION_KEY": "@encryption-key"
    }
  },
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        },
        {
          "key": "Permissions-Policy",
          "value": "camera=(), microphone=(), geolocation=()"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/api/health",
      "destination": "/api/health"
    }
  ],
  "redirects": [
    {
      "source": "/dashboard",
      "destination": "/",
      "permanent": false
    }
  ]
}
```

## Testing Framework Configuration

### Jest Configuration
```javascript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/app/layout.tsx',
    '!src/app/page.tsx',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/cypress/',
    '<rootDir>/e2e/',
  ],
}

// CreateJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)
```

```javascript
// jest.setup.js
import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(),
      signInWithOAuth: jest.fn(),
      signOut: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
    })),
  })),
}))

// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))
```

### Playwright Configuration
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }],
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

## Code Quality Tools

### ESLint Configuration
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    'next/core-web-vitals',
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  root: true,
  env: {
    node: true,
    browser: true,
    es2022: true,
  },
  parserOptions: {
    ecmaVersion: 2022,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  rules: {
    // TypeScript specific rules
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    
    // React specific rules
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': 'warn',
    
    // General rules
    'no-console': 'warn',
    'no-debugger': 'error',
    'prefer-const': 'error',
    'no-var': 'error',
    
    // Import rules
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index',
        ],
        'newlines-between': 'always',
        alphabetize: {
          order: 'asc',
          caseInsensitive: true,
        },
      },
    ],
  },
  overrides: [
    {
      files: ['*.test.ts', '*.test.tsx', '*.spec.ts', '*.spec.tsx'],
      env: {
        jest: true,
      },
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
      },
    },
  ],
};
```

### Prettier Configuration
```javascript
// .prettierrc.js
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 80,
  tabWidth: 2,
  useTabs: false,
  endOfLine: 'lf',
  arrowParens: 'avoid',
  bracketSpacing: true,
  jsxSingleQuote: true,
  plugins: ['prettier-plugin-tailwindcss'],
  tailwindConfig: './tailwind.config.js',
  overrides: [
    {
      files: '*.md',
      options: {
        printWidth: 120,
        proseWrap: 'always',
      },
    },
  ],
};
```

### Husky and Lint-Staged Setup
```bash
# Initialize Husky
npx husky install

# Add pre-commit hook
npx husky add .husky/pre-commit "npx lint-staged"

# Add commit-msg hook
npx husky add .husky/commit-msg "npx commitlint --edit $1"

# Add pre-push hook
npx husky add .husky/pre-push "npm run validate"
```

```javascript
// .lintstagedrc.js
module.exports = {
  '*.{js,jsx,ts,tsx}': [
    'eslint --fix',
    'prettier --write',
    'jest --bail --findRelatedTests',
  ],
  '*.{json,md,yml,yaml}': ['prettier --write'],
  '*.{css,scss}': ['prettier --write'],
}
```

## Environment Management

### Environment Variables Setup
```bash
# .env.local (Development)
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-bot-token
TELEGRAM_BOT_USERNAME=AureusAllianceBot

# Authentication
JWT_SECRET=your-super-secret-jwt-key
ENCRYPTION_KEY=your-32-byte-hex-encryption-key

# API Configuration
API_BASE_URL=http://localhost:3000/api
WEBHOOK_SECRET=your-webhook-secret

# Analytics and Monitoring
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
```

```typescript
// src/lib/env.ts - Environment validation
import { z } from 'zod';

const envSchema = z.object({
  // Public environment variables
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string(),
  NEXT_PUBLIC_APP_ENV: z.enum(['development', 'staging', 'production']),
  
  // Server-side environment variables
  SUPABASE_SERVICE_ROLE_KEY: z.string(),
  TELEGRAM_BOT_TOKEN: z.string(),
  TELEGRAM_BOT_USERNAME: z.string(),
  JWT_SECRET: z.string().min(32),
  ENCRYPTION_KEY: z.string().length(64), // 32 bytes in hex
  WEBHOOK_SECRET: z.string(),
  
  // Optional environment variables
  SENTRY_DSN: z.string().url().optional(),
  VERCEL_URL: z.string().optional(),
});

export const env = envSchema.parse(process.env);
```

## Development and Staging Environments

### Development Environment Setup
```yaml
# docker-compose.yml (Optional local development)
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: aureus_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### VS Code Configuration
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/.next": true,
    "**/coverage": true
  },
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "tailwindCSS.experimental.classRegex": [
    ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

```json
// .vscode/extensions.json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-playwright.playwright",
    "orta.vscode-jest"
  ]
}
```

## Performance and Monitoring Setup

### Performance Monitoring
```typescript
// src/lib/monitoring.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

export function reportWebVitals() {
  getCLS(console.log);
  getFID(console.log);
  getFCP(console.log);
  getLCP(console.log);
  getTTFB(console.log);
}

// Performance monitoring for production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  reportWebVitals();
}
```

### Bundle Analysis Setup
```javascript
// next.config.js
const { withSentryConfig } = require('@sentry/nextjs');

/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['avatars.githubusercontent.com', 't.me'],
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
};

const sentryWebpackPluginOptions = {
  silent: true,
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
};

// Analyze bundle if ANALYZE=true
if (process.env.ANALYZE === 'true') {
  const withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: true,
  });
  module.exports = withBundleAnalyzer(nextConfig);
} else {
  module.exports = withSentryConfig(nextConfig, sentryWebpackPluginOptions);
}
```

## Implementation Checklist

### Phase 3.1 Environment Setup Checklist
- [ ] **Project Initialization**
  - [ ] Create Next.js 14+ project with TypeScript
  - [ ] Install and configure all dependencies
  - [ ] Set up project folder structure
  - [ ] Configure package.json scripts

- [ ] **Version Control Setup**
  - [ ] Initialize Git repository
  - [ ] Set up branch protection rules
  - [ ] Configure commit conventions
  - [ ] Set up Husky hooks

- [ ] **CI/CD Pipeline**
  - [ ] Create GitHub Actions workflows
  - [ ] Configure Vercel deployment
  - [ ] Set up environment secrets
  - [ ] Test deployment pipeline

- [ ] **Testing Framework**
  - [ ] Configure Jest for unit tests
  - [ ] Set up Playwright for E2E tests
  - [ ] Configure test coverage reporting
  - [ ] Create sample tests

- [ ] **Code Quality Tools**
  - [ ] Configure ESLint with custom rules
  - [ ] Set up Prettier with Tailwind plugin
  - [ ] Configure lint-staged
  - [ ] Test pre-commit hooks

- [ ] **Environment Management**
  - [ ] Set up environment variables
  - [ ] Configure development environment
  - [ ] Set up staging environment
  - [ ] Validate environment schema

### Success Criteria
- [ ] ✅ Project builds successfully without errors
- [ ] ✅ All tests pass in CI/CD pipeline
- [ ] ✅ Code quality checks pass (linting, formatting, type checking)
- [ ] ✅ Deployment to staging environment successful
- [ ] ✅ Performance metrics meet baseline requirements
- [ ] ✅ Security scans pass without critical issues

---

**Environment Status**: SETUP READY
**Pipeline Status**: CI/CD CONFIGURED
**Quality Gates**: ALL CONFIGURED
**Deployment**: STAGING READY

*This environment setup provides a robust foundation for enterprise-grade development with automated quality assurance, comprehensive testing, and streamlined deployment processes.*
