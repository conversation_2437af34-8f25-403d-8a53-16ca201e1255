-- =====================================================
-- AUREUS AFRICA - REFERRAL SYSTEM DATABASE FIX
-- =====================================================
-- This script fixes all missing tables and columns for the referral system

-- 1. Add missing columns to users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS total_referrals INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_earnings DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS telegram_username VARCHAR(100);

-- 2. Create commission_balances table
CREATE TABLE IF NOT EXISTS public.commission_balances (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    usdt_balance DECIMAL(10,2) DEFAULT 0.00,
    share_balance DECIMAL(10,2) DEFAULT 0.00,
    total_earned_usdt DECIMAL(10,2) DEFAULT 0.00,
    total_earned_shares DECIMAL(10,2) DEFAULT 0.00,
    escrowed_amount DECIMAL(10,2) DEFAULT 0.00,
    total_withdrawn DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 3. Create user_notifications table
CREATE TABLE IF NOT EXISTS public.user_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info', -- 'info', 'success', 'warning', 'error', 'payment', 'commission', 'system'
    is_read BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create user_messages table
CREATE TABLE IF NOT EXISTS public.user_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    recipient_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'sent', -- 'sent', 'read', 'replied'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create marketing_content_generated table
CREATE TABLE IF NOT EXISTS public.marketing_content_generated (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL, -- 'facebook', 'instagram', 'twitter', etc.
    content_type VARCHAR(50) NOT NULL, -- 'post', 'story', 'tweet', etc.
    template_id VARCHAR(100),
    generated_content TEXT NOT NULL,
    hashtags TEXT,
    referral_link VARCHAR(500),
    character_count INTEGER,
    image_url VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create marketing_training_progress table
CREATE TABLE IF NOT EXISTS public.marketing_training_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    module_id VARCHAR(100) NOT NULL,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    completed BOOLEAN DEFAULT FALSE,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, module_id)
);

-- 7. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_commission_balances_user_id ON public.commission_balances(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_id ON public.user_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_created_at ON public.user_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_messages_sender_id ON public.user_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_user_messages_recipient_id ON public.user_messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_marketing_content_user_id ON public.marketing_content_generated(user_id);
CREATE INDEX IF NOT EXISTS idx_marketing_training_user_id ON public.marketing_training_progress(user_id);

-- 8. Enable Row Level Security (RLS)
ALTER TABLE public.commission_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_content_generated ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_training_progress ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS Policies (with safe handling of existing policies)

-- Commission Balances - Users can only see their own
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'commission_balances' AND policyname = 'Users can view own commission balance') THEN
        CREATE POLICY "Users can view own commission balance" ON public.commission_balances
            FOR SELECT USING (user_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'commission_balances' AND policyname = 'Users can update own commission balance') THEN
        CREATE POLICY "Users can update own commission balance" ON public.commission_balances
            FOR UPDATE USING (user_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

-- User Notifications - Users can only see their own
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_notifications' AND policyname = 'Users can view own notifications') THEN
        CREATE POLICY "Users can view own notifications" ON public.user_notifications
            FOR SELECT USING (user_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_notifications' AND policyname = 'Users can update own notifications') THEN
        CREATE POLICY "Users can update own notifications" ON public.user_notifications
            FOR UPDATE USING (user_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

-- User Messages - Users can see messages they sent or received
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_messages' AND policyname = 'Users can view own messages') THEN
        CREATE POLICY "Users can view own messages" ON public.user_messages
            FOR SELECT USING (
                sender_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id) OR
                recipient_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id)
            );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'user_messages' AND policyname = 'Users can send messages') THEN
        CREATE POLICY "Users can send messages" ON public.user_messages
            FOR INSERT WITH CHECK (sender_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

-- Marketing Content - Users can only see their own
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'marketing_content_generated' AND policyname = 'Users can view own marketing content') THEN
        CREATE POLICY "Users can view own marketing content" ON public.marketing_content_generated
            FOR SELECT USING (user_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'marketing_content_generated' AND policyname = 'Users can create marketing content') THEN
        CREATE POLICY "Users can create marketing content" ON public.marketing_content_generated
            FOR INSERT WITH CHECK (user_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

-- Marketing Training - Users can only see their own progress
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'marketing_training_progress' AND policyname = 'Users can view own training progress') THEN
        CREATE POLICY "Users can view own training progress" ON public.marketing_training_progress
            FOR SELECT USING (user_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'marketing_training_progress' AND policyname = 'Users can update own training progress') THEN
        CREATE POLICY "Users can update own training progress" ON public.marketing_training_progress
            FOR ALL USING (user_id = (SELECT id FROM public.users WHERE auth.uid() = auth_user_id));
    END IF;
END $$;

-- 10. Create functions to update user stats (only if referrals table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'referrals' AND table_schema = 'public') THEN
        -- Create or replace the function
        CREATE OR REPLACE FUNCTION update_user_referral_stats()
        RETURNS TRIGGER AS $func$
        BEGIN
            -- Update total_referrals for the referrer
            UPDATE public.users
            SET total_referrals = (
                SELECT COUNT(*)
                FROM public.referrals
                WHERE referrer_id = NEW.referrer_id AND status = 'active'
            )
            WHERE id = NEW.referrer_id;

            RETURN NEW;
        END;
        $func$ LANGUAGE plpgsql;

        -- Create trigger to automatically update referral stats
        DROP TRIGGER IF EXISTS trigger_update_referral_stats ON public.referrals;
        CREATE TRIGGER trigger_update_referral_stats
            AFTER INSERT OR UPDATE OR DELETE ON public.referrals
            FOR EACH ROW
            EXECUTE FUNCTION update_user_referral_stats();

        RAISE NOTICE '✅ Referral stats trigger created successfully';
    ELSE
        RAISE NOTICE '⚠️  Referrals table not found - skipping trigger creation';
    END IF;
END $$;

-- 11. Initialize commission balances for existing users
INSERT INTO public.commission_balances (user_id, usdt_balance, share_balance, total_earned_usdt, total_earned_shares, escrowed_amount, total_withdrawn)
SELECT 
    id,
    0.00,
    0.00,
    0.00,
    0.00,
    0.00,
    0.00
FROM public.users
WHERE id NOT IN (SELECT user_id FROM public.commission_balances)
ON CONFLICT (user_id) DO NOTHING;

-- 12. Update existing user referral counts (only if referrals table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'referrals' AND table_schema = 'public') THEN
        UPDATE public.users
        SET total_referrals = (
            SELECT COUNT(*)
            FROM public.referrals
            WHERE referrer_id = users.id AND status = 'active'
        );
        RAISE NOTICE '✅ User referral counts updated successfully';
    ELSE
        RAISE NOTICE '⚠️  Referrals table not found - skipping referral count update';
    END IF;
END $$;

-- 13. Create sample notifications for testing (optional)
INSERT INTO public.user_notifications (user_id, title, message, type)
SELECT 
    id,
    'Welcome to Aureus Africa!',
    'Thank you for joining our gold mining community. Start by exploring your dashboard and setting up your referral links.',
    'info'
FROM public.users
WHERE id NOT IN (SELECT DISTINCT user_id FROM public.user_notifications WHERE title = 'Welcome to Aureus Africa!')
LIMIT 10; -- Only for first 10 users to avoid spam

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Referral system database setup completed successfully!';
    RAISE NOTICE '📊 Tables created: commission_balances, user_notifications, user_messages, marketing_content_generated, marketing_training_progress';
    RAISE NOTICE '🔧 Columns added: users.total_referrals, users.total_earnings, users.phone, users.telegram_username';
    RAISE NOTICE '🔒 Row Level Security policies applied';
    RAISE NOTICE '⚡ Triggers and functions created for automatic stats updates';
END $$;
