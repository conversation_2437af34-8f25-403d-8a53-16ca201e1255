import React, { useState, useEffect, useRef } from 'react'

interface InlineEditProps {
  value: string
  onSave: (newValue: string) => Promise<void>
  type?: 'text' | 'email' | 'select'
  options?: string[]
  placeholder?: string
  className?: string
  disabled?: boolean
}

export const InlineEdit: React.FC<InlineEditProps> = ({
  value,
  onSave,
  type = 'text',
  options = [],
  placeholder = '',
  className = '',
  disabled = false
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(value)
  const [loading, setLoading] = useState(false)
  const inputRef = useRef<HTMLInputElement | HTMLSelectElement>(null)

  useEffect(() => {
    setEditValue(value)
  }, [value])

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      if (inputRef.current instanceof HTMLInputElement) {
        inputRef.current.select()
      }
    }
  }, [isEditing])

  const handleSave = async () => {
    if (editValue === value) {
      setIsEditing(false)
      return
    }

    setLoading(true)
    try {
      await onSave(editValue)
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to save:', error)
      setEditValue(value) // Reset to original value on error
      alert('Failed to save changes')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setEditValue(value)
    setIsEditing(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancel()
    }
  }

  if (disabled) {
    return (
      <span className={`text-gray-400 ${className}`}>
        {value || placeholder}
      </span>
    )
  }

  if (isEditing) {
    return (
      <div className="flex items-center space-x-2">
        {type === 'select' ? (
          <select
            ref={inputRef as React.RefObject<HTMLSelectElement>}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleSave}
            className="px-2 py-1 text-xs bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:border-yellow-500"
            disabled={loading}
          >
            {options.map(option => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        ) : (
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type={type}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleSave}
            className="px-2 py-1 text-xs bg-gray-800 border border-gray-600 rounded text-white focus:outline-none focus:border-yellow-500"
            placeholder={placeholder}
            disabled={loading}
          />
        )}
        
        {loading && (
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-yellow-500"></div>
        )}
      </div>
    )
  }

  return (
    <span
      onClick={() => setIsEditing(true)}
      className={`cursor-pointer hover:bg-gray-700/50 px-1 py-0.5 rounded text-xs ${className}`}
      title="Click to edit"
    >
      {value || (
        <span className="text-gray-500 italic">{placeholder || 'Click to edit'}</span>
      )}
    </span>
  )
}

// Hook for managing multiple inline edits
export const useInlineEdit = () => {
  const [editingField, setEditingField] = useState<string | null>(null)

  const startEdit = (fieldName: string) => {
    setEditingField(fieldName)
  }

  const stopEdit = () => {
    setEditingField(null)
  }

  const isEditing = (fieldName: string) => {
    return editingField === fieldName
  }

  return {
    startEdit,
    stopEdit,
    isEditing,
    editingField
  }
}
