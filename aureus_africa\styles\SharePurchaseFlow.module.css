/* Mobile-Responsive Styles for SharePurchaseFlow */

.purchaseFlowContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
}

.purchaseFlowModal {
  background-color: rgba(17, 24, 39, 0.95);
  border-radius: 16px;
  border: 1px solid #374151;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .purchaseFlowContainer {
    padding: 8px;
    align-items: flex-start;
    padding-top: 20px;
  }
  
  .purchaseFlowModal {
    max-height: 95vh;
    border-radius: 12px;
  }
}

@media (max-width: 480px) {
  .purchaseFlowContainer {
    padding: 4px;
    padding-top: 10px;
  }
  
  .purchaseFlowModal {
    border-radius: 8px;
    max-height: 98vh;
  }
}

/* Touch-Optimized Buttons */
.quickAmountButton {
  padding: 16px 12px;
  background-color: rgba(55, 65, 81, 0.5);
  border: 1px solid #4b5563;
  border-radius: 8px;
  color: #d1d5db;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px; /* Touch-friendly minimum */
  display: flex;
  align-items: center;
  justify-content: center;
}

.quickAmountButton:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
  color: #60a5fa;
}

.quickAmountButton.active {
  background-color: rgba(59, 130, 246, 0.2);
  border: 2px solid #3b82f6;
  color: #60a5fa;
}

.quickAmountButton:active {
  transform: scale(0.98);
}

/* Mobile Grid Adjustments */
.quickAmountGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

@media (max-width: 480px) {
  .quickAmountGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }
  
  .quickAmountButton {
    padding: 14px 8px;
    font-size: 13px;
    min-height: 44px;
  }
}

/* Input Field Optimizations */
.customAmountInput {
  width: 100%;
  padding: 16px 16px 16px 40px;
  background-color: rgba(31, 41, 55, 0.8);
  border: 2px solid #374151;
  border-radius: 12px;
  color: white;
  font-size: 18px;
  font-weight: 600;
  transition: border-color 0.2s ease;
}

.customAmountInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

@media (max-width: 480px) {
  .customAmountInput {
    padding: 14px 14px 14px 36px;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 8px;
  }
}

/* Purchase Preview Cards */
.purchasePreviewGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

@media (max-width: 480px) {
  .purchasePreviewGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.previewCard {
  text-align: center;
  padding: 16px;
  background-color: rgba(55, 65, 81, 0.3);
  border-radius: 8px;
  border: 1px solid #4b5563;
}

@media (max-width: 480px) {
  .previewCard {
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }
}

/* One-Click Purchase Buttons */
.oneClickButton {
  padding: 16px;
  background-color: rgba(16, 185, 129, 0.1);
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  color: #10b981;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  min-height: 56px; /* Touch-friendly */
}

.oneClickButton:hover {
  background-color: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
}

.oneClickButton:active {
  transform: scale(0.98);
}

@media (max-width: 480px) {
  .oneClickButton {
    padding: 14px;
    font-size: 15px;
    min-height: 52px;
    border-radius: 8px;
  }
}

/* Continue Button */
.continueButton {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 56px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.continueButton:disabled {
  background: rgba(55, 65, 81, 0.5);
  cursor: not-allowed;
}

.continueButton:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.continueButton:not(:disabled):active {
  transform: translateY(0);
}

@media (max-width: 480px) {
  .continueButton {
    padding: 14px;
    font-size: 16px;
    min-height: 52px;
    border-radius: 8px;
  }
}

/* Real-time Price Display */
.priceDisplay {
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  animation: priceUpdate 0.3s ease;
}

@keyframes priceUpdate {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@media (max-width: 480px) {
  .priceDisplay {
    padding: 12px;
    border-radius: 8px;
  }
}

/* Loading States */
.loadingSpinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #374151;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Accessibility Improvements */
.srOnly {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus Indicators */
.focusable:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth Scrolling */
.purchaseFlowModal {
  scroll-behavior: smooth;
}

/* iOS Safari Specific Fixes */
@supports (-webkit-touch-callout: none) {
  .customAmountInput {
    font-size: 16px; /* Prevents zoom */
  }
  
  .purchaseFlowModal {
    -webkit-overflow-scrolling: touch;
  }
}
