import React, { useState, useEffect, Component } from 'react';
import { DynamicGallery } from './DynamicGallery';
import { StaticGallery } from './StaticGallery';
import type { GalleryDisplayProps } from '../../types/gallery';

interface EnhancedGalleryProps extends GalleryDisplayProps {
  fallbackMode?: 'auto' | 'static' | 'dynamic';
  showConnectionStatus?: boolean;
}

export const EnhancedGallery: React.FC<EnhancedGalleryProps> = ({
  categoryFilter,
  showCategories = true,
  showSearch = false, // Disable search for static fallback
  itemsPerPage = 12,
  layout = 'grid',
  showFeaturedFirst = true,
  fallbackMode = 'auto',
  showConnectionStatus = false
}) => {
  const [useStaticFallback, setUseStaticFallback] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isTestingConnection, setIsTestingConnection] = useState(true);

  // Test database connection on mount
  useEffect(() => {
    const testConnection = async () => {
      if (fallbackMode === 'static') {
        setUseStaticFallback(true);
        setIsTestingConnection(false);
        return;
      }

      if (fallbackMode === 'dynamic') {
        setUseStaticFallback(false);
        setIsTestingConnection(false);
        return;
      }

      // Auto mode - test connection
      try {
        setIsTestingConnection(true);
        
        // Import supabase client dynamically to avoid issues
        const { supabase } = await import('../../lib/supabase');
        
        // Quick connection test with timeout
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Connection timeout')), 3000);
        });

        const testPromise = supabase
          .from('gallery_images')
          .select('id', { count: 'exact', head: true })
          .limit(1);

        await Promise.race([testPromise, timeoutPromise]);
        
        // If we get here, connection is working
        setUseStaticFallback(false);
        setConnectionError(null);
        console.log('✅ Gallery database connection successful');
        
      } catch (error) {
        console.warn('⚠️ Gallery database connection failed, using static fallback:', error);
        setUseStaticFallback(true);
        setConnectionError(error instanceof Error ? error.message : 'Connection failed');
      } finally {
        setIsTestingConnection(false);
      }
    };

    testConnection();
  }, [fallbackMode]);

  // Loading state during connection test
  if (isTestingConnection) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col items-center justify-center py-16">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-600"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-amber-500 border-t-transparent absolute top-0 left-0"></div>
          </div>
          <p className="text-gray-400 mt-4 text-center">
            Initializing gallery...
          </p>
        </div>
      </div>
    );
  }

  // Connection status indicator (optional)
  const ConnectionStatus = () => {
    if (!showConnectionStatus) return null;

    return (
      <div className="mb-4">
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
          useStaticFallback 
            ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400'
            : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
        }`}>
          <div className={`w-2 h-2 rounded-full mr-2 ${
            useStaticFallback ? 'bg-amber-500' : 'bg-green-500'
          }`}></div>
          {useStaticFallback ? 'Static Gallery Mode' : 'Live Database Mode'}
        </div>
      </div>
    );
  };

  // Error boundary for dynamic gallery
  const DynamicGalleryWithErrorBoundary = () => {
    const [hasError, setHasError] = useState(false);

    // Create a custom error boundary component
    class GalleryErrorBoundary extends React.Component<
      { children: React.ReactNode; onError: () => void },
      { hasError: boolean }
    > {
      constructor(props: { children: React.ReactNode; onError: () => void }) {
        super(props);
        this.state = { hasError: false };
      }

      static getDerivedStateFromError(error: Error) {
        return { hasError: true };
      }

      componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.warn('Dynamic gallery error caught by boundary, switching to static fallback:', error);
        this.props.onError();
      }

      render() {
        if (this.state.hasError) {
          return (
            <StaticGallery
              showCategories={showCategories}
              itemsPerPage={itemsPerPage}
            />
          );
        }

        return this.props.children;
      }
    }

    const handleError = () => {
      setHasError(true);
      setUseStaticFallback(true);
    };

    if (hasError) {
      return (
        <StaticGallery
          showCategories={showCategories}
          itemsPerPage={itemsPerPage}
        />
      );
    }

    return (
      <GalleryErrorBoundary onError={handleError}>
        <DynamicGallery
          categoryFilter={categoryFilter}
          showCategories={showCategories}
          showSearch={showSearch}
          itemsPerPage={itemsPerPage}
          layout={layout}
          showFeaturedFirst={showFeaturedFirst}
        />
      </GalleryErrorBoundary>
    );
  };

  return (
    <div className="space-y-6">
      <ConnectionStatus />
      
      {useStaticFallback ? (
        <StaticGallery
          showCategories={showCategories}
          itemsPerPage={itemsPerPage}
        />
      ) : (
        <DynamicGalleryWithErrorBoundary />
      )}
    </div>
  );
};
