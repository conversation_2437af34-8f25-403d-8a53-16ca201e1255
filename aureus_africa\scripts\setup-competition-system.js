const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase configuration
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupCompetitionSystem() {
  console.log('🏆 Setting up Gold Diggers Club Competition System');
  console.log('================================================');

  try {
    // Step 1: Create database tables
    console.log('\n📋 Step 1: Creating database tables...');
    
    const sqlFilePath = path.join(__dirname, '..', 'sql', 'create_competition_tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Split SQL content by statements (rough split on semicolons)
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          const { error } = await supabase.rpc('exec_sql', { sql: statement });
          if (error) {
            console.error(`❌ Error executing SQL statement:`, error);
            console.log('Statement:', statement.substring(0, 100) + '...');
          }
        } catch (err) {
          console.error(`❌ Error with statement:`, err);
          console.log('Statement:', statement.substring(0, 100) + '...');
        }
      }
    }

    console.log('✅ Database tables created successfully');

    // Step 2: Check if we have an active phase
    console.log('\n📊 Step 2: Checking for active investment phase...');
    
    const { data: activePhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .order('phase_number', { ascending: false })
      .limit(1)
      .single();

    if (phaseError && phaseError.code !== 'PGRST116') {
      console.error('❌ Error fetching active phase:', phaseError);
      return;
    }

    let currentPhaseId = activePhase?.id;

    // If no active phase, create a default one
    if (!activePhase) {
      console.log('📝 No active phase found, creating default phase...');
      
      const { data: newPhase, error: createPhaseError } = await supabase
        .from('investment_phases')
        .insert({
          phase_number: 1,
          phase_name: 'Phase 1 - Early Bird',
          price_per_share: 0.50,
          total_shares_available: 70000,
          shares_sold: 0,
          is_active: true,
          start_date: new Date().toISOString(),
          description: 'Early bird phase with maximum discount'
        })
        .select('id')
        .single();

      if (createPhaseError) {
        console.error('❌ Error creating default phase:', createPhaseError);
        return;
      }

      currentPhaseId = newPhase.id;
      console.log('✅ Default phase created with ID:', currentPhaseId);
    } else {
      console.log('✅ Found active phase:', activePhase.phase_name, 'ID:', currentPhaseId);
    }

    // Step 3: Create default competition
    console.log('\n🏆 Step 3: Setting up default competition...');
    
    // Check if there's already an active competition
    const { data: existingCompetition, error: compError } = await supabase
      .from('competitions')
      .select('*')
      .eq('is_active', true)
      .eq('status', 'active')
      .limit(1)
      .single();

    if (compError && compError.code !== 'PGRST116') {
      console.error('❌ Error checking existing competition:', compError);
      return;
    }

    let competitionId;

    if (existingCompetition) {
      competitionId = existingCompetition.id;
      console.log('✅ Found existing active competition:', existingCompetition.name);
    } else {
      // Create new competition
      const { data: newCompetition, error: createCompError } = await supabase
        .from('competitions')
        .insert({
          name: `Gold Diggers Club - Phase ${activePhase?.phase_number || 1}`,
          description: 'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
          phase_id: currentPhaseId,
          start_date: new Date().toISOString(),
          minimum_qualification_amount: 2500.00,
          total_prize_pool: 150000.00, // $60k + $30k + $18k + 7×$6k = $150k total
          status: 'active',
          is_active: true
        })
        .select('id')
        .single();

      if (createCompError) {
        console.error('❌ Error creating competition:', createCompError);
        return;
      }

      competitionId = newCompetition.id;
      console.log('✅ Created new competition with ID:', competitionId);

      // Step 4: Create prize tiers
      console.log('\n🏅 Step 4: Setting up prize tiers...');
      
      const prizeTiers = [
        { tier_name: '1st Place', tier_rank_start: 1, tier_rank_end: 1, prize_amount: 60000, display_order: 1, emoji: '🥇' },
        { tier_name: '2nd Place', tier_rank_start: 2, tier_rank_end: 2, prize_amount: 30000, display_order: 2, emoji: '🥈' },
        { tier_name: '3rd Place', tier_rank_start: 3, tier_rank_end: 3, prize_amount: 18000, display_order: 3, emoji: '🥉' },
        { tier_name: '4th Place', tier_rank_start: 4, tier_rank_end: 4, prize_amount: 6000, display_order: 4, emoji: '🏆' },
        { tier_name: '5th Place', tier_rank_start: 5, tier_rank_end: 5, prize_amount: 6000, display_order: 5, emoji: '🏆' },
        { tier_name: '6th Place', tier_rank_start: 6, tier_rank_end: 6, prize_amount: 6000, display_order: 6, emoji: '🏆' },
        { tier_name: '7th Place', tier_rank_start: 7, tier_rank_end: 7, prize_amount: 6000, display_order: 7, emoji: '🏆' },
        { tier_name: '8th Place', tier_rank_start: 8, tier_rank_end: 8, prize_amount: 6000, display_order: 8, emoji: '🏆' },
        { tier_name: '9th Place', tier_rank_start: 9, tier_rank_end: 9, prize_amount: 6000, display_order: 9, emoji: '🏆' },
        { tier_name: '10th Place', tier_rank_start: 10, tier_rank_end: 10, prize_amount: 6000, display_order: 10, emoji: '🏆' }
      ];

      const { error: prizeTiersError } = await supabase
        .from('competition_prize_tiers')
        .insert(
          prizeTiers.map(tier => ({
            competition_id: competitionId,
            ...tier
          }))
        );

      if (prizeTiersError) {
        console.error('❌ Error creating prize tiers:', prizeTiersError);
        return;
      }

      console.log('✅ Prize tiers created successfully');
    }

    // Step 5: Verify setup
    console.log('\n🔍 Step 5: Verifying setup...');
    
    const { data: competition, error: verifyError } = await supabase
      .from('competitions')
      .select(`
        *,
        competition_prize_tiers (*)
      `)
      .eq('id', competitionId)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying setup:', verifyError);
      return;
    }

    console.log('✅ Competition setup verified:');
    console.log(`   - Name: ${competition.name}`);
    console.log(`   - Prize Pool: $${competition.total_prize_pool.toLocaleString()}`);
    console.log(`   - Minimum Qualification: $${competition.minimum_qualification_amount.toLocaleString()}`);
    console.log(`   - Prize Tiers: ${competition.competition_prize_tiers.length}`);

    console.log('\n🎉 Gold Diggers Club Competition System setup complete!');
    console.log('\n📋 System Overview:');
    console.log('✅ Database tables created');
    console.log('✅ Default competition created');
    console.log('✅ Prize tiers configured');
    console.log('✅ Phase integration ready');

    console.log('\n🏆 Competition Criteria:');
    console.log('• Rankings based on REFERRAL SALES VOLUME (not personal purchases)');
    console.log('• Rewards network leaders who bring in the most business');
    console.log('• Minimum $2,500 in referral sales to qualify');
    console.log('• Real-time leaderboard updates');

    console.log('\n📋 Next steps:');
    console.log('1. Set up automatic referral data sync (every 30 minutes)');
    console.log('2. Use the admin interface to create competitions for new phases');
    console.log('3. Monitor network leader performance and rankings');
    console.log('4. Test the dynamic leaderboard display on your website');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

// Run the setup
if (require.main === module) {
  setupCompetitionSystem()
    .then(() => {
      console.log('\n✅ Setup script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Setup script failed:', error);
      process.exit(1);
    });
}

module.exports = { setupCompetitionSystem };
