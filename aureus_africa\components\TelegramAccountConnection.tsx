import React, { useState, useEffect } from 'react';
import { supabase, pollForAuthConfirmation } from '../lib/supabase';

interface TelegramAccountConnectionProps {
  userId: number;
  userEmail: string;
  onConnectionSuccess: () => void;
  className?: string;
}

interface ConnectionStep {
  step: 'prompt' | 'connecting' | 'success';
}

export const TelegramAccountConnection: React.FC<TelegramAccountConnectionProps> = ({
  userId,
  userEmail,
  onConnectionSuccess,
  className = ''
}) => {
  const [currentStep, setCurrentStep] = useState<ConnectionStep['step']>('prompt');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [checkingConnection, setCheckingConnection] = useState(true);
  const [connectionPin, setConnectionPin] = useState<string | null>(null);
  const [pinExpiresAt, setPinExpiresAt] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  // Check if user already has a connected Telegram account
  useEffect(() => {
    checkTelegramConnection();
  }, [userId]);

  const checkTelegramConnection = async () => {
    try {
      setCheckingConnection(true);
      const response = await fetch(`/api/telegram-connection?user_id=${userId}`);
      const result = await response.json();
      
      if (result.success && result.isConnected) {
        setIsConnected(true);
      }
    } catch (err) {
      console.error('Error checking Telegram connection:', err);
    } finally {
      setCheckingConnection(false);
    }
  };

  const handleInitiateConnection = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/telegram-sync-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          email: userEmail
        }),
      });

      const result = await response.json();

      if (result.success) {
        setConnectionPin(result.pin);
        setPinExpiresAt(result.expiresAt);
        setCurrentStep('connecting');
        setIsPolling(true);
        setSuccess('Connection PIN generated! Please follow the instructions below.');

        // Start automatic polling for confirmation
        console.log('🔄 Starting automatic authentication polling...');
        const { success: pollSuccess, error: pollError, userData } = await pollForAuthConfirmation(result.pin);

        setIsPolling(false);

        if (!pollSuccess) {
          setError(pollError || 'Authentication failed');
          return;
        }

        console.log('✅ Telegram authentication successful!', userData);
        setSuccess('Successfully connected with Telegram!');
        setCurrentStep('success');

        // Call the success callback after a short delay
        setTimeout(() => {
          onConnectionSuccess();
        }, 1500);
      } else {
        setError(result.message || 'Failed to generate connection PIN');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteConnection = async () => {
    if (!connectionPin) {
      setError('No connection PIN available. Please restart the process.');
      return;
    }

    console.log('🚀 [CONNECTION] Starting connection completion process');
    console.log('🔑 [CONNECTION] Using PIN:', connectionPin);

    setLoading(true);
    setError(null);

    try {
      // First, test database connectivity
      console.log('🔍 [CONNECTION] Testing database connectivity...');
      const { data: testData, error: testError } = await supabase
        .from('auth_tokens')
        .select('count')
        .limit(1);

      if (testError) {
        console.error('❌ [CONNECTION] Database connectivity test failed:', testError);
        setError('Database connection error. Please try again.');
        return;
      }

      console.log('✅ [CONNECTION] Database connectivity confirmed');

      // Check if token already exists before polling
      console.log('🔍 [CONNECTION] Checking if token already exists...');
      const { data: existingToken, error: existingError } = await supabase
        .from('auth_tokens')
        .select('*')
        .eq('token', connectionPin)
        .single();

      if (existingToken) {
        console.log('📋 [CONNECTION] Token found immediately:', existingToken);
        if (existingToken.confirmed) {
          console.log('🎉 [CONNECTION] Token already confirmed! Skipping polling.');
          // Process immediately without polling
        }
      } else {
        console.log('⏳ [CONNECTION] Token not found yet, will start polling...');
      }

      // Start polling for confirmation using the working function from UnifiedAuthPage
      console.log('🔄 Starting authentication polling...');
      const { success: pollSuccess, error: pollError, userData } = await pollForAuthConfirmation(connectionPin);

      const confirmed = pollSuccess;

      if (!confirmed) {
        setError(pollError || 'Connection timeout. Please make sure you confirmed the connection in Telegram and try again.');
        return;
      }

      // Get the confirmed token data to extract telegram_id
      const { data: confirmedToken, error: tokenError } = await supabase
        .from('auth_tokens')
        .select('*')
        .eq('token', connectionPin)
        .single();

      if (tokenError || !confirmedToken.telegram_id) {
        setError('Failed to retrieve connection details. Please try again.');
        return;
      }

      // Check if this Telegram account is already connected to another user
      const { data: existingConnection, error: connectionError } = await supabase
        .from('telegram_users')
        .select('user_id, telegram_id')
        .eq('telegram_id', confirmedToken.telegram_id)
        .not('user_id', 'is', null)
        .single();

      if (connectionError && connectionError.code !== 'PGRST116') {
        console.error('Error checking existing connections:', connectionError);
        setError('Failed to verify connection status. Please try again.');
        return;
      }

      if (existingConnection && existingConnection.user_id !== parseInt(userId)) {
        setError('This Telegram account is already connected to another user account.');
        return;
      }

      if (existingConnection && existingConnection.user_id === parseInt(userId)) {
        setError('This Telegram account is already connected to your account.');
        return;
      }

      // Process the connection
      const response = await fetch('/api/telegram-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          telegram_id: confirmedToken.telegram_id
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCurrentStep('success');
        setSuccess('🎉 Telegram account connected successfully! Your bot data is now synced with your web account.');
        setTimeout(() => {
          onConnectionSuccess();
        }, 3000);
      } else {
        setError(result.error || 'Failed to connect accounts. Please try again.');
      }
    } catch (err: any) {
      console.error('Connection completion error:', err);
      setError('Network error. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Don't show if already connected or still checking
  if (checkingConnection) {
    return null;
  }

  if (isConnected) {
    return null;
  }

  const renderPromptStep = () => (
    <div className={`bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-2 border-blue-500/30 rounded-xl p-6 mb-6 ${className}`}>
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-2xl">
            📱
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-xl font-bold text-white mb-2">
            🚀 Connect Your Telegram Account
          </h3>
          <p className="text-gray-300 mb-4 leading-relaxed">
            <strong>Don't see your purchased shares from the Telegram bot?</strong> You need to connect your Telegram account to sync all your data! 
            This will import all your purchases, referrals, and commissions from the bot to your web dashboard.
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleInitiateConnection}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Initiating...
                </>
              ) : (
                <>
                  🔗 Connect Telegram Account
                </>
              )}
            </button>
            <div className="text-sm text-gray-400 flex items-center">
              ✅ Quick & secure connection
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderConnectionInstructionsStep = () => (
    <div className={`bg-gradient-to-r from-green-600/20 to-blue-600/20 border-2 border-green-500/30 rounded-xl p-6 mb-6 ${className}`}>
      <div className="text-center">
        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
          📱
        </div>
        <h3 className="text-xl font-bold text-white mb-2">
          Complete Connection in Telegram
        </h3>
        <p className="text-gray-300 mb-4">
          Your 6-digit connection PIN has been generated. Follow these steps:
        </p>

        {/* Display the PIN prominently */}
        {connectionPin && (
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-4 mb-6">
            <p className="text-white text-sm font-semibold mb-2">Your Connection PIN:</p>
            <div className="bg-white/20 rounded-lg p-3 mb-3">
              <span className="text-3xl font-mono font-bold text-white tracking-widest">
                {connectionPin}
              </span>
            </div>
            <button
              onClick={() => navigator.clipboard?.writeText(connectionPin || '')}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors mb-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              Copy PIN
            </button>
            <p className="text-blue-100 text-xs">
              ⏰ Expires in 15 minutes • Enter this PIN in the Telegram bot
            </p>
          </div>
        )}

        <div className="bg-gray-800/50 rounded-lg p-4 mb-6 text-left">
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</span>
              <div>
                <p className="text-white font-semibold">Open Telegram</p>
                <p className="text-gray-400 text-sm">Go to @AureusAllianceBot</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</span>
              <div>
                <p className="text-white font-semibold">Send your PIN</p>
                <p className="text-gray-400 text-sm">Simply type the 6-digit PIN and send it (no commands needed)</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</span>
              <div>
                <p className="text-white font-semibold">Confirm connection</p>
                <p className="text-gray-400 text-sm">Click "Confirm" when the bot asks to link your accounts</p>
              </div>
            </div>
          </div>
        </div>

        {/* Waiting for confirmation status */}
        {isPolling && (
          <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-center gap-3 mb-3">
              <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
              <span className="text-blue-300 font-medium">Waiting for confirmation...</span>
            </div>
            <div className="text-center">
              <button
                onClick={() => {
                  setIsPolling(false);
                  setConnectionPin(null);
                  setCurrentStep('prompt');
                }}
                className="text-sm text-gray-400 hover:text-white underline"
              >
                Cancel Authentication
              </button>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={handleCompleteConnection}
            disabled={loading || isPolling}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
          >
            {loading ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Verifying Connection...
              </>
            ) : (
              <>✅ I've Connected My Account</>
            )}
          </button>
          <button
            onClick={() => {
              setCurrentStep('prompt');
              setConnectionPin(null);
              setIsPolling(false);
            }}
            disabled={isPolling}
            className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-500 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
          >
            ← Back
          </button>
        </div>
      </div>
    </div>
  );

  const renderConnectingStep = () => (
    <div className={`bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-2 border-purple-500/30 rounded-xl p-6 mb-6 ${className}`}>
      <div className="text-center">
        <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
          <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin" />
        </div>
        <h3 className="text-xl font-bold text-white mb-2">
          Connecting Your Account...
        </h3>
        <p className="text-gray-300">
          Please wait while we set up the connection between your web account and Telegram bot.
        </p>
      </div>
    </div>
  );

  const renderSuccessStep = () => (
    <div className={`bg-gradient-to-r from-green-600/20 to-emerald-600/20 border-2 border-green-500/30 rounded-xl p-6 mb-6 ${className}`}>
      <div className="text-center">
        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
          ✅
        </div>
        <h3 className="text-xl font-bold text-white mb-2">
          Connection Initiated!
        </h3>
        <p className="text-gray-300 mb-4">
          Please check your Telegram bot (@AureusAllianceBot) for further instructions to complete the connection.
        </p>
        <div className="text-sm text-gray-400">
          Your data will be synced automatically once the connection is complete.
        </div>
      </div>
    </div>
  );

  return (
    <div>
      {error && (
        <div className="bg-red-600/20 border border-red-500/30 rounded-lg p-4 mb-4">
          <p className="text-red-300">❌ {error}</p>
        </div>
      )}
      
      {success && (
        <div className="bg-green-600/20 border border-green-500/30 rounded-lg p-4 mb-4">
          <p className="text-green-300">✅ {success}</p>
        </div>
      )}

      {currentStep === 'prompt' && renderPromptStep()}
      {currentStep === 'connecting' && renderConnectionInstructionsStep()}
      {currentStep === 'success' && renderSuccessStep()}
    </div>
  );
};

export default TelegramAccountConnection;
