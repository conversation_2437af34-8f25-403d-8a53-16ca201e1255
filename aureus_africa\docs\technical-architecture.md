# 2.2 Technical Architecture - Aureus Alliance Web Dashboard

## Executive Summary
This document defines the comprehensive technical architecture for the Aureus Alliance Web Dashboard, including frontend architecture, component hierarchy, state management strategy, API integration layer, and optimization plans. The architecture ensures scalability, maintainability, and perfect synchronization with the existing Telegram bot system.

## Frontend Architecture Overview

### Next.js 14+ App Router Structure
```
aureus-dashboard/
├── app/                           # Next.js App Router
│   ├── (auth)/                   # Authentication route group
│   │   ├── login/                # Telegram login page
│   │   ├── country-selection/    # Country selection onboarding
│   │   └── terms/               # Terms acceptance
│   ├── dashboard/               # Protected dashboard routes
│   │   ├── page.tsx            # Main dashboard
│   │   ├── shares/             # Share purchase & portfolio
│   │   │   ├── page.tsx        # Share purchase interface
│   │   │   ├── purchase/       # Purchase flow
│   │   │   └── portfolio/      # User portfolio view
│   │   ├── kyc/                # KYC document management
│   │   │   ├── page.tsx        # KYC status & upload
│   │   │   └── upload/         # Document upload flow
│   │   ├── referrals/          # Referral management
│   │   │   ├── page.tsx        # Referral dashboard
│   │   │   └── analytics/      # Referral analytics
│   │   ├── commissions/        # Commission management
│   │   │   ├── page.tsx        # Commission overview
│   │   │   ├── withdraw/       # Withdrawal requests
│   │   │   └── convert/        # Commission to shares
│   │   └── profile/            # User profile management
│   ├── admin/                   # Admin panel (role-based)
│   │   ├── page.tsx            # Admin dashboard
│   │   ├── users/              # User management
│   │   ├── payments/           # Payment approvals
│   │   ├── kyc/                # KYC reviews
│   │   └── analytics/          # System analytics
│   ├── api/                     # API routes
│   │   ├── auth/               # Authentication endpoints
│   │   │   ├── telegram/       # Telegram OAuth
│   │   │   ├── session/        # Session management
│   │   │   └── logout/         # Logout handler
│   │   ├── user/               # User management
│   │   │   ├── profile/        # Profile CRUD
│   │   │   ├── onboarding/     # Onboarding status
│   │   │   └── preferences/    # User preferences
│   │   ├── shares/             # Share operations
│   │   │   ├── phases/         # Current phase info
│   │   │   ├── purchase/       # Purchase processing
│   │   │   └── portfolio/      # Portfolio data
│   │   ├── payments/           # Payment processing
│   │   │   ├── crypto/         # USDT payments
│   │   │   ├── bank/           # Bank transfers
│   │   │   └── status/         # Payment status
│   │   ├── kyc/                # KYC operations
│   │   │   ├── upload/         # Document upload
│   │   │   ├── status/         # KYC status
│   │   │   └── documents/      # Document management
│   │   ├── referrals/          # Referral system
│   │   │   ├── link/           # Referral links
│   │   │   ├── stats/          # Statistics
│   │   │   └── commissions/    # Commission data
│   │   └── admin/              # Admin operations
│   ├── globals.css             # Global styles
│   ├── layout.tsx              # Root layout
│   ├── loading.tsx             # Global loading UI
│   ├── error.tsx               # Global error UI
│   └── not-found.tsx           # 404 page
├── components/                  # Reusable UI components
│   ├── ui/                     # Base UI components
│   │   ├── Button.tsx          # Button variants
│   │   ├── Card.tsx            # Card components
│   │   ├── Input.tsx           # Form inputs
│   │   ├── Modal.tsx           # Modal dialogs
│   │   ├── Table.tsx           # Data tables
│   │   ├── Badge.tsx           # Status badges
│   │   ├── Spinner.tsx         # Loading spinners
│   │   └── Toast.tsx           # Notification toasts
│   ├── layout/                 # Layout components
│   │   ├── Header.tsx          # Main header
│   │   ├── Sidebar.tsx         # Navigation sidebar
│   │   ├── Footer.tsx          # Footer component
│   │   └── Navigation.tsx      # Navigation menu
│   ├── auth/                   # Authentication components
│   │   ├── TelegramLogin.tsx   # Telegram login widget
│   │   ├── CountrySelector.tsx # Country selection
│   │   ├── TermsAcceptance.tsx # Terms acceptance
│   │   └── AuthGuard.tsx       # Route protection
│   ├── dashboard/              # Dashboard-specific components
│   │   ├── KPICards.tsx        # Key performance indicators
│   │   ├── QuickActions.tsx    # Quick action buttons
│   │   ├── ActivityFeed.tsx    # Recent activity
│   │   └── StatsOverview.tsx   # Statistics overview
│   ├── shares/                 # Share-related components
│   │   ├── PhaseInfo.tsx       # Current phase display
│   │   ├── ShareCalculator.tsx # Share purchase calculator
│   │   ├── PaymentSelector.tsx # Payment method selection
│   │   ├── Portfolio.tsx       # Portfolio display
│   │   └── PurchaseFlow.tsx    # Purchase wizard
│   ├── kyc/                    # KYC components
│   │   ├── DocumentUpload.tsx  # File upload interface
│   │   ├── DocumentPreview.tsx # Document preview
│   │   ├── StatusTracker.tsx   # KYC status tracking
│   │   └── RequirementsList.tsx # KYC requirements
│   ├── referrals/              # Referral components
│   │   ├── ReferralLink.tsx    # Referral link generator
│   │   ├── ReferralStats.tsx   # Statistics display
│   │   ├── CommissionCard.tsx  # Commission balance
│   │   └── ReferralList.tsx    # Referred users list
│   └── admin/                  # Admin components
│       ├── UserTable.tsx       # User management table
│       ├── PaymentApproval.tsx # Payment approval interface
│       ├── KYCReview.tsx       # KYC review interface
│       └── Analytics.tsx       # Admin analytics
├── lib/                        # Utility libraries
│   ├── supabase/              # Supabase configuration
│   │   ├── client.ts          # Browser client
│   │   ├── server.ts          # Server client
│   │   ├── middleware.ts      # Middleware client
│   │   └── types.ts           # Database types
│   ├── auth/                  # Authentication utilities
│   │   ├── telegram.ts        # Telegram OAuth helpers
│   │   ├── session.ts         # Session management
│   │   └── middleware.ts      # Auth middleware
│   ├── utils/                 # General utilities
│   │   ├── calculations.ts    # Share calculations
│   │   ├── formatting.ts      # Number/date formatting
│   │   ├── validation.ts      # Data validation
│   │   └── constants.ts       # Application constants
│   ├── hooks/                 # Custom React hooks
│   │   ├── useAuth.ts         # Authentication hook
│   │   ├── useSupabase.ts     # Supabase hooks
│   │   ├── useRealtime.ts     # Real-time subscriptions
│   │   └── useLocalStorage.ts # Local storage hook
│   └── api/                   # API client utilities
│       ├── client.ts          # API client configuration
│       ├── endpoints.ts       # Endpoint definitions
│       └── types.ts           # API types
├── store/                      # State management
│   ├── authStore.ts           # Authentication state
│   ├── userStore.ts           # User data state
│   ├── sharesStore.ts         # Share data state
│   ├── notificationStore.ts   # Notifications state
│   └── index.ts               # Store configuration
├── types/                      # TypeScript definitions
│   ├── auth.ts                # Authentication types
│   ├── user.ts                # User types
│   ├── shares.ts              # Share types
│   ├── payments.ts            # Payment types
│   ├── kyc.ts                 # KYC types
│   ├── referrals.ts           # Referral types
│   └── api.ts                 # API response types
├── styles/                     # Styling
│   ├── globals.css            # Global styles
│   ├── components.css         # Component styles
│   └── animations.css         # Animation definitions
├── public/                     # Static assets
│   ├── icons/                 # Application icons
│   ├── images/                # Images and logos
│   └── documents/             # Legal documents
├── middleware.ts              # Next.js middleware
├── next.config.js             # Next.js configuration
├── tailwind.config.js         # Tailwind configuration
├── tsconfig.json              # TypeScript configuration
└── package.json               # Dependencies
```

## Component Hierarchy and Data Flow

### Component Architecture Pattern
```typescript
// Atomic Design Methodology Implementation
interface ComponentHierarchy {
  atoms: 'Button' | 'Input' | 'Badge' | 'Spinner';
  molecules: 'SearchBox' | 'PaymentCard' | 'StatusBadge';
  organisms: 'Header' | 'SharePurchaseForm' | 'KYCUploader';
  templates: 'DashboardLayout' | 'AuthLayout' | 'AdminLayout';
  pages: 'Dashboard' | 'SharePurchase' | 'KYCManagement';
}

// Example: Share Purchase Component Hierarchy
SharePurchasePage
├── SharePurchaseTemplate
│   ├── Header (organism)
│   │   ├── Navigation (molecule)
│   │   └── UserProfile (molecule)
│   ├── PhaseInfoCard (organism)
│   │   ├── PhaseProgress (molecule)
│   │   │   ├── ProgressBar (atom)
│   │   │   └── Badge (atom)
│   │   └── PhaseStats (molecule)
│   ├── PurchaseForm (organism)
│   │   ├── ShareCalculator (molecule)
│   │   │   ├── NumberInput (atom)
│   │   │   └── Button (atom)
│   │   ├── PaymentSelector (molecule)
│   │   │   ├── RadioButton (atom)
│   │   │   └── PaymentCard (molecule)
│   │   └── SubmitButton (atom)
│   └── Footer (organism)
```

### Data Flow Architecture
```typescript
// Unidirectional Data Flow Pattern
interface DataFlowPattern {
  // 1. User Action
  userAction: 'onClick' | 'onSubmit' | 'onChange';
  
  // 2. State Update
  stateUpdate: 'updateStore' | 'optimisticUpdate';
  
  // 3. API Call
  apiCall: 'POST /api/shares/purchase';
  
  // 4. Database Update
  databaseUpdate: 'INSERT INTO crypto_payment_transactions';
  
  // 5. Real-time Sync
  realtimeSync: 'Supabase subscription update';
  
  // 6. UI Update
  uiUpdate: 'Component re-render with new data';
}

// Example: Share Purchase Data Flow
const sharePurchaseFlow = {
  1: 'User clicks "Purchase Shares"',
  2: 'Form validates input data',
  3: 'Optimistic UI update (loading state)',
  4: 'API call to /api/shares/purchase',
  5: 'Insert payment record to database',
  6: 'Real-time notification to admin',
  7: 'Update UI with confirmation',
  8: 'Redirect to payment instructions'
};
```

## State Management Strategy

### Zustand Store Architecture
```typescript
// Authentication Store
interface AuthStore {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  login: (telegramData: TelegramAuthData) => Promise<void>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
}

// User Store
interface UserStore {
  profile: UserProfile | null;
  onboardingStatus: OnboardingStatus;
  preferences: UserPreferences;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  completeOnboardingStep: (step: OnboardingStep) => Promise<void>;
}

// Shares Store
interface SharesStore {
  currentPhase: SharePhase | null;
  userPortfolio: ShareHolding[];
  purchaseHistory: Purchase[];
  isLoading: boolean;
  fetchCurrentPhase: () => Promise<void>;
  purchaseShares: (request: PurchaseRequest) => Promise<void>;
  fetchPortfolio: () => Promise<void>;
}

// Notifications Store
interface NotificationStore {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Notification) => void;
  markAsRead: (id: string) => void;
  clearAll: () => void;
}

// Store Implementation Example
export const useAuthStore = create<AuthStore>((set, get) => ({
  user: null,
  session: null,
  isLoading: false,
  
  login: async (telegramData) => {
    set({ isLoading: true });
    try {
      const response = await authAPI.loginWithTelegram(telegramData);
      set({ 
        user: response.user, 
        session: response.session,
        isLoading: false 
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },
  
  logout: async () => {
    await authAPI.logout();
    set({ user: null, session: null });
  },
  
  refreshSession: async () => {
    const session = await authAPI.getSession();
    set({ session });
  }
}));
```

### State Persistence Strategy
```typescript
// Local Storage Integration
interface PersistenceConfig {
  // Persist authentication state
  auth: {
    storage: 'localStorage',
    key: 'aureus-auth',
    whitelist: ['user', 'session']
  };
  
  // Persist user preferences
  preferences: {
    storage: 'localStorage',
    key: 'aureus-preferences',
    whitelist: ['theme', 'language', 'notifications']
  };
  
  // Session storage for temporary data
  temporary: {
    storage: 'sessionStorage',
    key: 'aureus-temp',
    whitelist: ['formData', 'purchaseFlow']
  };
}

// Hydration handling for SSR
export const useHydratedStore = <T>(store: StateCreator<T>) => {
  const [hydrated, setHydrated] = useState(false);
  
  useEffect(() => {
    setHydrated(true);
  }, []);
  
  return hydrated ? store : null;
};
```

## API Integration Layer

### API Client Architecture
```typescript
// Base API Client Configuration
class APIClient {
  private baseURL: string;
  private headers: Record<string, string>;
  
  constructor(config: APIConfig) {
    this.baseURL = config.baseURL;
    this.headers = {
      'Content-Type': 'application/json',
      ...config.headers
    };
  }
  
  // Request interceptor for authentication
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const session = await getSession();
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.headers,
        ...(session && { Authorization: `Bearer ${session.access_token}` }),
        ...options.headers
      }
    };
    
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new APIError(response.status, response.statusText);
    }
    
    return response.json();
  }
  
  // HTTP methods
  get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }
  
  post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
  
  put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }
  
  delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// API Service Classes
export class AuthAPI {
  constructor(private client: APIClient) {}
  
  async loginWithTelegram(data: TelegramAuthData): Promise<AuthResponse> {
    return this.client.post('/api/auth/telegram', data);
  }
  
  async logout(): Promise<void> {
    return this.client.post('/api/auth/logout', {});
  }
  
  async getSession(): Promise<Session | null> {
    return this.client.get('/api/auth/session');
  }
}

export class SharesAPI {
  constructor(private client: APIClient) {}
  
  async getCurrentPhase(): Promise<SharePhase> {
    return this.client.get('/api/shares/phases/current');
  }
  
  async purchaseShares(request: PurchaseRequest): Promise<PurchaseResponse> {
    return this.client.post('/api/shares/purchase', request);
  }
  
  async getPortfolio(): Promise<Portfolio> {
    return this.client.get('/api/shares/portfolio');
  }
}
```

### Error Handling Strategy
```typescript
// Error Types
export class APIError extends Error {
  constructor(
    public status: number,
    public statusText: string,
    public details?: any
  ) {
    super(`API Error ${status}: ${statusText}`);
    this.name = 'APIError';
  }
}

export class ValidationError extends Error {
  constructor(public field: string, message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Error Boundary Component
export class ErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error boundary caught error:', error, errorInfo);
    // Log to monitoring service
    logError(error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }
    
    return this.props.children;
  }
}

// Global Error Handler
export const handleAPIError = (error: unknown): string => {
  if (error instanceof APIError) {
    switch (error.status) {
      case 401:
        // Redirect to login
        window.location.href = '/login';
        return 'Authentication required';
      case 403:
        return 'Access denied';
      case 404:
        return 'Resource not found';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return error.statusText || 'An error occurred';
    }
  }
  
  if (error instanceof ValidationError) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};
```

## Database Schema for Frontend-Specific Data

### Client-Side Data Models
```typescript
// User Interface Models
export interface UserProfile {
  id: string;
  telegramId: number;
  username?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  countryOfResidence?: string;
  countrySelectionCompleted: boolean;
  termsAccepted: boolean;
  termsAcceptedAt?: string;
  kycStatus: 'pending' | 'approved' | 'rejected' | 'not_started';
  createdAt: string;
  updatedAt: string;
}

export interface SharePhase {
  id: string;
  phaseNumber: number;
  pricePerShare: number;
  totalShares: number;
  availableShares: number;
  soldShares: number;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  description?: string;
}

export interface ShareHolding {
  id: string;
  userId: string;
  phaseId: string;
  shares: number;
  pricePerShare: number;
  totalValue: number;
  purchaseDate: string;
  status: 'pending' | 'approved' | 'rejected';
}

export interface PaymentTransaction {
  id: string;
  userId: string;
  amount: number;
  currency: 'USD' | 'ZAR' | 'USDT';
  paymentMethod: 'bank_transfer' | 'crypto' | 'commission';
  status: 'pending' | 'approved' | 'rejected';
  reference?: string;
  proofOfPayment?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Commission {
  id: string;
  userId: string;
  referredUserId: string;
  amount: number;
  type: 'referral' | 'purchase' | 'bonus';
  status: 'pending' | 'available' | 'withdrawn';
  earnedAt: string;
  withdrawnAt?: string;
}
```

### Real-time Subscription Models
```typescript
// Supabase Real-time Subscriptions
export const useRealtimeSubscriptions = (userId: string) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  
  useEffect(() => {
    // Subscribe to payment status updates
    const paymentSubscription = supabase
      .channel('payment-updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'crypto_payment_transactions',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          const payment = payload.new as PaymentTransaction;
          if (payment.status === 'approved') {
            addNotification({
              type: 'success',
              title: 'Payment Approved',
              message: `Your payment of ${payment.amount} ${payment.currency} has been approved.`
            });
          }
        }
      )
      .subscribe();
    
    // Subscribe to commission updates
    const commissionSubscription = supabase
      .channel('commission-updates')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'commission_accounts',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          const commission = payload.new as Commission;
          addNotification({
            type: 'info',
            title: 'Commission Earned',
            message: `You earned $${commission.amount} in commission.`
          });
        }
      )
      .subscribe();
    
    return () => {
      paymentSubscription.unsubscribe();
      commissionSubscription.unsubscribe();
    };
  }, [userId]);
  
  return { notifications };
};
```

## Caching and Optimization Strategies

### Client-Side Caching
```typescript
// SWR Configuration for Data Fetching
export const swrConfig: SWRConfiguration = {
  revalidateOnFocus: false,
  revalidateOnReconnect: true,
  refreshInterval: 30000, // 30 seconds for real-time data
  errorRetryCount: 3,
  errorRetryInterval: 5000,
  
  // Custom fetcher with error handling
  fetcher: async (url: string) => {
    const response = await fetch(url);
    if (!response.ok) {
      throw new APIError(response.status, response.statusText);
    }
    return response.json();
  },
  
  // Cache key generation
  keyGenerator: (key) => Array.isArray(key) ? key.join('-') : key
};

// Custom hooks with caching
export const useSharePhase = () => {
  const { data, error, mutate } = useSWR(
    '/api/shares/phases/current',
    {
      refreshInterval: 60000, // 1 minute
      revalidateOnMount: true
    }
  );
  
  return {
    phase: data,
    isLoading: !error && !data,
    isError: error,
    refresh: mutate
  };
};

export const useUserPortfolio = (userId: string) => {
  const { data, error, mutate } = useSWR(
    userId ? [`/api/shares/portfolio`, userId] : null,
    {
      refreshInterval: 300000, // 5 minutes
      revalidateOnMount: true
    }
  );
  
  return {
    portfolio: data,
    isLoading: !error && !data,
    isError: error,
    refresh: mutate
  };
};
```

### Performance Optimization
```typescript
// Code Splitting and Lazy Loading
const SharePurchasePage = lazy(() => import('./pages/SharePurchase'));
const KYCManagementPage = lazy(() => import('./pages/KYCManagement'));
const ReferralDashboard = lazy(() => import('./pages/ReferralDashboard'));

// Image Optimization
export const OptimizedImage: React.FC<{
  src: string;
  alt: string;
  width: number;
  height: number;
}> = ({ src, alt, width, height }) => (
  <Image
    src={src}
    alt={alt}
    width={width}
    height={height}
    priority={false}
    placeholder="blur"
    blurDataURL="data:image/jpeg;base64,..."
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  />
);

// Memoization for expensive calculations
export const ShareCalculator: React.FC<{
  shares: number;
  pricePerShare: number;
}> = memo(({ shares, pricePerShare }) => {
  const totalCost = useMemo(() => {
    return shares * pricePerShare;
  }, [shares, pricePerShare]);
  
  const projectedValue = useMemo(() => {
    // Complex calculation based on bot logic
    return calculateProjectedValue(shares, pricePerShare);
  }, [shares, pricePerShare]);
  
  return (
    <div>
      <p>Total Cost: ${totalCost.toLocaleString()}</p>
      <p>Projected Value: ${projectedValue.toLocaleString()}</p>
    </div>
  );
});

// Virtual scrolling for large lists
export const VirtualizedTable: React.FC<{
  data: any[];
  columns: Column[];
}> = ({ data, columns }) => {
  const rowVirtualizer = useVirtualizer({
    count: data.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 50,
    overscan: 10
  });
  
  return (
    <div ref={parentRef} className="h-96 overflow-auto">
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            <TableRow data={data[virtualItem.index]} columns={columns} />
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Bundle Optimization
```typescript
// Next.js Configuration
export default {
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@supabase/supabase-js']
  },
  
  // Bundle analysis
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Bundle analyzer in development
    if (dev && !isServer) {
      config.plugins.push(
        new (require('@next/bundle-analyzer'))({
          enabled: process.env.ANALYZE === 'true'
        })
      );
    }
    
    // Optimize bundle splitting
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        },
        supabase: {
          test: /[\\/]node_modules[\\/]@supabase[\\/]/,
          name: 'supabase',
          chunks: 'all'
        }
      }
    };
    
    return config;
  }
};
```

## Security Architecture

### Authentication Middleware
```typescript
// Next.js Middleware for Route Protection
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Public routes that don't require authentication
  const publicRoutes = ['/login', '/terms', '/privacy'];
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }
  
  // Check authentication
  const session = await getSession(request);
  if (!session) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  // Check onboarding completion
  if (pathname.startsWith('/dashboard')) {
    const user = await getUser(session.user.id);
    if (!user.countrySelectionCompleted) {
      return NextResponse.redirect(new URL('/country-selection', request.url));
    }
    if (!user.termsAccepted) {
      return NextResponse.redirect(new URL('/terms', request.url));
    }
  }
  
  // Admin route protection
  if (pathname.startsWith('/admin')) {
    const user = await getUser(session.user.id);
    if (!user.isAdmin) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)']
};
```

### Data Validation
```typescript
// Zod schemas for runtime validation
export const SharePurchaseSchema = z.object({
  shares: z.number().min(1).max(10000),
  paymentMethod: z.enum(['bank_transfer', 'crypto', 'commission']),
  agreedToTerms: z.boolean().refine(val => val === true, {
    message: 'You must agree to the terms and conditions'
  })
});

export const KYCUploadSchema = z.object({
  documentType: z.enum(['identity', 'proof_of_address', 'additional']),
  file: z.instanceof(File).refine(
    file => file.size <= 10 * 1024 * 1024, // 10MB
    'File size must be less than 10MB'
  ).refine(
    file => ['application/pdf', 'image/jpeg', 'image/png'].includes(file.type),
    'File must be PDF, JPEG, or PNG'
  )
});

// Form validation hook
export const useFormValidation = <T>(schema: z.ZodSchema<T>) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const validate = (data: unknown): data is T => {
    try {
      schema.parse(data);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMap = error.errors.reduce((acc, err) => {
          acc[err.path.join('.')] = err.message;
          return acc;
        }, {} as Record<string, string>);
        setErrors(errorMap);
      }
      return false;
    }
  };
  
  return { validate, errors };
};
```

## Monitoring and Analytics

### Performance Monitoring
```typescript
// Performance metrics collection
export const performanceMonitor = {
  // Core Web Vitals
  trackCoreWebVitals: () => {
    getCLS(console.log);
    getFID(console.log);
    getFCP(console.log);
    getLCP(console.log);
    getTTFB(console.log);
  },
  
  // Custom metrics
  trackPageLoad: (pageName: string) => {
    const startTime = performance.now();
    return () => {
      const loadTime = performance.now() - startTime;
      console.log(`Page ${pageName} loaded in ${loadTime}ms`);
      // Send to analytics service
    };
  },
  
  // API performance
  trackAPICall: (endpoint: string) => {
    const startTime = performance.now();
    return () => {
      const duration = performance.now() - startTime;
      console.log(`API call to ${endpoint} took ${duration}ms`);
      // Send to monitoring service
    };
  }
};

// Error tracking
export const errorTracker = {
  captureError: (error: Error, context?: Record<string, any>) => {
    console.error('Error captured:', error, context);
    // Send to error tracking service (Sentry, LogRocket, etc.)
  },
  
  captureMessage: (message: string, level: 'info' | 'warning' | 'error') => {
    console.log(`[${level.toUpperCase()}] ${message}`);
    // Send to logging service
  }
};
```

## Deployment Configuration

### Environment Configuration
```typescript
// Environment variables schema
export const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']),
  NEXTAUTH_URL: z.string().url(),
  NEXTAUTH_SECRET: z.string().min(32),
  SUPABASE_URL: z.string().url(),
  SUPABASE_ANON_KEY: z.string(),
  SUPABASE_SERVICE_ROLE_KEY: z.string(),
  TELEGRAM_BOT_TOKEN: z.string(),
  TELEGRAM_BOT_USERNAME: z.string()
});

// Environment validation
export const env = envSchema.parse(process.env);

// Feature flags
export const featureFlags = {
  enableRealTimeNotifications: env.NODE_ENV === 'production',
  enableAdvancedAnalytics: env.NODE_ENV === 'production',
  enableBetaFeatures: env.NODE_ENV === 'development'
};
```

---

**Architecture Status**: COMPLETE
**Implementation Ready**: Frontend architecture fully defined
**Scalability**: Designed for 10,000+ users
**Performance**: Optimized for <2 second load times
**Security**: Enterprise-grade authentication and validation

*This technical architecture provides a solid foundation for building a scalable, maintainable, and secure web dashboard that perfectly integrates with the existing Telegram bot system while delivering superior user experience.*
