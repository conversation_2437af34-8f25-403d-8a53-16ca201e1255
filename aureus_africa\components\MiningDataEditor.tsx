import React, { useState, useEffect } from 'react'
import { supabase, getCurrentUser } from '../lib/supabase'

interface MiningData {
  id: string
  site_name: string
  kg_produced: number
  value_100k: number
  value_150k: number
  is_total: boolean
  display_order: number
}

export const MiningDataEditor: React.FC = () => {
  const [data, setData] = useState<MiningData[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState('')
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    loadData()
    loadUser()
  }, [])

  const loadUser = async () => {
    const currentUser = await getCurrentUser()
    setUser(currentUser)
  }

  const loadData = async () => {
    setLoading(true)
    try {
      const { data: miningData, error } = await supabase
        .from('mining_data')
        .select('*')
        .order('display_order', { ascending: true })

      if (error) {
        console.error('Error loading mining data:', error)
        setMessage('Error loading mining data')
      } else {
        setData(miningData || [])
      }
    } catch (error) {
      console.error('Error loading mining data:', error)
      setMessage('Error loading mining data')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async (item: MiningData) => {
    if (!user) return
    
    setSaving(true)
    try {
      const { error } = await supabase
        .from('mining_data')
        .update({
          site_name: item.site_name,
          kg_produced: item.kg_produced,
          value_100k: item.value_100k,
          value_150k: item.value_150k,
          is_total: item.is_total,
          display_order: item.display_order,
          updated_by: user.email
        })
        .eq('id', item.id)

      if (error) {
        console.error('Error saving mining data:', error)
        setMessage('Error saving mining data')
      } else {
        setMessage('Mining data saved successfully!')
        setTimeout(() => setMessage(''), 3000)
      }
    } catch (error) {
      console.error('Error saving mining data:', error)
      setMessage('Error saving mining data')
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (id: string, field: keyof MiningData, value: any) => {
    setData(prev => prev.map(item => 
      item.id === id ? { ...item, [field]: value } : item
    ))
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num)
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Loading mining data...</p>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">
          Mining Data Management
        </h2>
        {saving && (
          <div className="flex items-center text-yellow-400">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-400 mr-2"></div>
            Saving...
          </div>
        )}
      </div>

      {message && (
        <div className={`p-3 rounded-lg mb-6 ${
          message.includes('Error') 
            ? 'bg-red-900/50 border border-red-700 text-red-300' 
            : 'bg-green-900/50 border border-green-700 text-green-300'
        }`}>
          {message}
        </div>
      )}

      <div className="space-y-6">
        {data.map((item) => (
          <div key={item.id} className="glass-card p-6 border border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Site Name
                </label>
                <input
                  type="text"
                  value={item.site_name}
                  onChange={(e) => handleInputChange(item.id, 'site_name', e.target.value)}
                  onBlur={() => handleSave(item)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  KG Produced
                </label>
                <input
                  type="number"
                  value={item.kg_produced}
                  onChange={(e) => handleInputChange(item.id, 'kg_produced', parseInt(e.target.value) || 0)}
                  onBlur={() => handleSave(item)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Display Order
                </label>
                <input
                  type="number"
                  value={item.display_order}
                  onChange={(e) => handleInputChange(item.id, 'display_order', parseInt(e.target.value) || 0)}
                  onBlur={() => handleSave(item)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Value at $100k/kg
                </label>
                <input
                  type="number"
                  value={item.value_100k}
                  onChange={(e) => handleInputChange(item.id, 'value_100k', parseInt(e.target.value) || 0)}
                  onBlur={() => handleSave(item)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-white"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Formatted: ${formatNumber(item.value_100k)}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Value at $150k/kg
                </label>
                <input
                  type="number"
                  value={item.value_150k}
                  onChange={(e) => handleInputChange(item.id, 'value_150k', parseInt(e.target.value) || 0)}
                  onBlur={() => handleSave(item)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-white"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Formatted: ${formatNumber(item.value_150k)}
                </p>
              </div>

              <div className="flex items-center">
                <label className="flex items-center space-x-2 text-gray-300">
                  <input
                    type="checkbox"
                    checked={item.is_total}
                    onChange={(e) => handleInputChange(item.id, 'is_total', e.target.checked)}
                    onBlur={() => handleSave(item)}
                    className="rounded border-gray-700 bg-gray-800 text-yellow-500 focus:ring-yellow-500"
                  />
                  <span>Is Total Row</span>
                </label>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
