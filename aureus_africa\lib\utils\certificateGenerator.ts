export interface CertificateData {
  certificateNumber: string;
  holderName: string;
  shares: number;
  issueDate: string;
  userId: string;
}

export const generateCertificateHTML = (data: CertificateData): string => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Aureus Africa Share Certificate - ${data.certificateNumber}</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@300;400;500;600&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Inter', sans-serif;
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
        }
        
        .certificate-container {
          background: white;
          border: 12px solid #d4af37;
          border-radius: 20px;
          padding: 60px;
          max-width: 900px;
          width: 100%;
          box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
          position: relative;
          overflow: hidden;
        }
        
        .certificate-container::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: 
            radial-gradient(circle at 20% 20%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
          pointer-events: none;
        }
        
        .certificate-header {
          text-align: center;
          margin-bottom: 40px;
          position: relative;
          z-index: 1;
        }
        
        .company-logo {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, #d4af37, #f1c40f);
          border-radius: 50%;
          margin: 0 auto 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 36px;
          font-weight: bold;
          color: white;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .company-name {
          font-family: 'Playfair Display', serif;
          font-size: 48px;
          font-weight: 700;
          color: #d4af37;
          margin-bottom: 10px;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .certificate-title {
          font-family: 'Playfair Display', serif;
          font-size: 32px;
          font-weight: 400;
          color: #2d3748;
          letter-spacing: 3px;
          text-transform: uppercase;
        }
        
        .certificate-body {
          text-align: center;
          margin: 50px 0;
          position: relative;
          z-index: 1;
        }
        
        .certificate-text {
          font-size: 18px;
          color: #4a5568;
          line-height: 1.8;
          margin-bottom: 30px;
        }
        
        .holder-name {
          font-family: 'Playfair Display', serif;
          font-size: 42px;
          font-weight: 700;
          color: #d4af37;
          margin: 30px 0;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
          border-bottom: 3px solid #d4af37;
          padding-bottom: 10px;
          display: inline-block;
        }
        
        .shares-amount {
          font-family: 'Playfair Display', serif;
          font-size: 64px;
          font-weight: 700;
          color: #d4af37;
          margin: 40px 0;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .certificate-details {
          background: rgba(212, 175, 55, 0.05);
          border: 2px solid rgba(212, 175, 55, 0.2);
          border-radius: 15px;
          padding: 30px;
          margin: 40px 0;
          position: relative;
          z-index: 1;
        }
        
        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          font-size: 16px;
        }
        
        .detail-row:last-child {
          margin-bottom: 0;
        }
        
        .detail-label {
          color: #4a5568;
          font-weight: 500;
        }
        
        .detail-value {
          color: #2d3748;
          font-weight: 600;
          font-family: 'Courier New', monospace;
        }
        
        .signatures {
          display: flex;
          justify-content: space-between;
          margin-top: 60px;
          position: relative;
          z-index: 1;
        }
        
        .signature-block {
          text-align: center;
          flex: 1;
          margin: 0 30px;
        }
        
        .signature-line {
          border-bottom: 2px solid #2d3748;
          width: 200px;
          margin: 0 auto 15px;
          height: 50px;
        }
        
        .signature-title {
          font-size: 14px;
          color: #4a5568;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 1px;
        }
        
        .qr-code {
          position: absolute;
          top: 30px;
          right: 30px;
          background: white;
          padding: 15px;
          border-radius: 10px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          z-index: 2;
        }
        
        .qr-placeholder {
          width: 80px;
          height: 80px;
          background: linear-gradient(45deg, #e2e8f0, #cbd5e0);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: #4a5568;
          text-align: center;
          line-height: 1.2;
        }
        
        .certificate-footer {
          text-align: center;
          margin-top: 40px;
          padding-top: 30px;
          border-top: 2px solid rgba(212, 175, 55, 0.3);
          position: relative;
          z-index: 1;
        }
        
        .footer-text {
          font-size: 12px;
          color: #718096;
          line-height: 1.5;
        }
        
        .verification-url {
          color: #d4af37;
          text-decoration: none;
          font-weight: 500;
        }
        
        @media print {
          body {
            background: white;
            padding: 0;
          }
          
          .certificate-container {
            box-shadow: none;
            border-radius: 0;
            max-width: none;
          }
        }
      </style>
    </head>
    <body>
      <div class="certificate-container">
        <div class="qr-code">
          <div class="qr-placeholder">
            QR Code<br>
            ${data.certificateNumber}
          </div>
        </div>
        
        <div class="certificate-header">
          <div class="company-logo">AU</div>
          <h1 class="company-name">AUREUS AFRICA</h1>
          <h2 class="certificate-title">Share Certificate</h2>
        </div>
        
        <div class="certificate-body">
          <p class="certificate-text">
            This is to certify that
          </p>
          
          <div class="holder-name">${data.holderName}</div>
          
          <p class="certificate-text">
            is the registered holder of
          </p>
          
          <div class="shares-amount">${data.shares.toLocaleString()}</div>
          
          <p class="certificate-text">
            fully paid ordinary shares in<br>
            <strong>Aureus Africa Limited</strong>
          </p>
          
          <div class="certificate-details">
            <div class="detail-row">
              <span class="detail-label">Certificate Number:</span>
              <span class="detail-value">${data.certificateNumber}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Issue Date:</span>
              <span class="detail-value">${new Date(data.issueDate).toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Shareholder ID:</span>
              <span class="detail-value">${data.userId}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">Share Class:</span>
              <span class="detail-value">Ordinary Shares</span>
            </div>
          </div>
        </div>
        
        <div class="signatures">
          <div class="signature-block">
            <div class="signature-line"></div>
            <div class="signature-title">Company Secretary</div>
          </div>
          <div class="signature-block">
            <div class="signature-line"></div>
            <div class="signature-title">Director</div>
          </div>
        </div>
        
        <div class="certificate-footer">
          <p class="footer-text">
            This certificate is issued in accordance with the Articles of Association of Aureus Africa Limited.<br>
            For verification, visit: <a href="#" class="verification-url">verify.aureusafrica.com/${data.certificateNumber}</a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
};

export const printCertificate = (data: CertificateData): void => {
  const certificateHTML = generateCertificateHTML(data);
  
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(certificateHTML);
    printWindow.document.close();
    printWindow.focus();
    
    // Auto-print after a short delay to ensure content is loaded
    setTimeout(() => {
      printWindow.print();
    }, 1000);
  }
};

export const downloadCertificateHTML = (data: CertificateData): void => {
  const certificateHTML = generateCertificateHTML(data);
  const blob = new Blob([certificateHTML], { type: 'text/html' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `Aureus_Africa_Certificate_${data.certificateNumber}.html`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};
