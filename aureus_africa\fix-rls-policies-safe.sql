-- SAFE RLS POLICY FIXES - SIMPLIFIED VERSION
-- This script fixes the missing functions and broken policies
-- Execute each section carefully and test after each step

-- =====================================================
-- STEP 1: CREATE MISSING SECURITY FUNCTIONS
-- =====================================================

-- Drop existing functions if they exist (to avoid conflicts)
DROP FUNCTION IF EXISTS public.is_admin_user(TEXT);
DROP FUNCTION IF EXISTS public.get_current_user_id();
DROP FUNCTION IF EXISTS public.is_current_user_admin();

-- Create the missing is_admin_user function (SIMPLIFIED)
CREATE OR REPLACE FUNCTION public.is_admin_user(user_email TEXT DEFAULT NULL)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- If no email provided, return false for safety
  IF user_email IS NULL OR user_email = '' THEN
    RETURN false;
  END IF;

  -- Check if user is admin in admin_users table
  RETURN EXISTS (
    SELECT 1 FROM public.admin_users
    WHERE email = user_email
    AND COALESCE(is_active, false) = true
  );
EXCEPTION WHEN OTHERS THEN
  -- If any error occurs, return false for safety
  RETURN false;
END;
$$;

-- Create get_current_user_id function
CREATE OR REPLACE FUNCTION public.get_current_user_id() 
RETURNS INTEGER AS $$
DECLARE
  user_email TEXT;
  user_id INTEGER;
  auth_user_uuid UUID;
BEGIN
  -- Try to get email from JWT first
  BEGIN
    user_email := current_setting('request.jwt.claims', true)::json->>'email';
  EXCEPTION WHEN OTHERS THEN
    user_email := NULL;
  END;
  
  -- Try to get auth.uid() if available
  BEGIN
    auth_user_uuid := auth.uid();
  EXCEPTION WHEN OTHERS THEN
    auth_user_uuid := NULL;
  END;
  
  -- Look up user by auth_user_id first (preferred method)
  IF auth_user_uuid IS NOT NULL THEN
    SELECT id INTO user_id FROM public.users WHERE auth_user_id = auth_user_uuid;
    IF user_id IS NOT NULL THEN
      RETURN user_id;
    END IF;
  END IF;
  
  -- Fallback to email lookup
  IF user_email IS NOT NULL THEN
    SELECT id INTO user_id FROM public.users WHERE email = user_email;
    IF user_id IS NOT NULL THEN
      RETURN user_id;
    END IF;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to check if current user is admin by user record
CREATE OR REPLACE FUNCTION public.is_current_user_admin() 
RETURNS BOOLEAN AS $$
DECLARE
  auth_user_uuid UUID;
  user_admin_status BOOLEAN;
BEGIN
  -- Try to get auth.uid()
  BEGIN
    auth_user_uuid := auth.uid();
  EXCEPTION WHEN OTHERS THEN
    RETURN false;
  END;
  
  -- If no auth user, return false
  IF auth_user_uuid IS NULL THEN
    RETURN false;
  END IF;
  
  -- Check if user is admin in users table
  SELECT is_admin INTO user_admin_status 
  FROM public.users 
  WHERE auth_user_id = auth_user_uuid;
  
  RETURN COALESCE(user_admin_status, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- STEP 2: TEST THE FUNCTIONS
-- =====================================================

-- Test the functions (run these to verify they work)
-- SELECT public.is_admin_user('<EMAIL>');
-- SELECT public.get_current_user_id();
-- SELECT public.is_current_user_admin();

-- =====================================================
-- STEP 3: GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant execute permissions to public role
GRANT EXECUTE ON FUNCTION public.is_admin_user(TEXT) TO public;
GRANT EXECUTE ON FUNCTION public.get_current_user_id() TO public;
GRANT EXECUTE ON FUNCTION public.is_current_user_admin() TO public;

-- Grant execute permissions to service role
GRANT EXECUTE ON FUNCTION public.is_admin_user(TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION public.get_current_user_id() TO service_role;
GRANT EXECUTE ON FUNCTION public.is_current_user_admin() TO service_role;
