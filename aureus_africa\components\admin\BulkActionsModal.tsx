import React, { useState } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'

interface User {
  id: number
  username: string
  full_name: string | null
  email: string
  is_active: boolean
  is_admin: boolean
  role: string
  created_at: string
  total_shares?: number
  total_invested?: number
  total_commissions?: number
  total_referrals?: number
  kyc_information?: any
}

interface BulkActionsModalProps {
  isOpen: boolean
  onClose: () => void
  selectedUsers: User[]
  adminUser?: any
  onUpdate: () => void
  onClearSelection: () => void
}

export const BulkActionsModal: React.FC<BulkActionsModalProps> = ({
  isOpen,
  onClose,
  selectedUsers,
  adminUser,
  onUpdate,
  onClearSelection
}) => {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'actions' | 'export' | 'message'>('actions')
  
  // Bulk action states
  const [bulkAction, setBulkAction] = useState<'activate' | 'deactivate' | 'delete' | 'role'>('activate')
  const [newRole, setNewRole] = useState('user')
  
  // Bulk message states
  const [messageTitle, setMessageTitle] = useState('')
  const [messageContent, setMessageContent] = useState('')
  const [messageCategory, setMessageCategory] = useState<'system' | 'payment' | 'commission'>('system')
  
  // Export states
  const [exportFormat, setExportFormat] = useState<'csv' | 'json'>('csv')
  const [exportFields, setExportFields] = useState({
    basic: true,
    financial: true,
    kyc: true,
    referrals: true
  })

  if (!isOpen) return null

  const handleBulkAction = async () => {
    if (selectedUsers.length === 0) {
      alert('No users selected')
      return
    }

    const confirmMessage = `Are you sure you want to ${bulkAction} ${selectedUsers.length} users?`
    if (!confirm(confirmMessage)) return

    setLoading(true)
    try {
      const userIds = selectedUsers.map(u => u.id)

      // Use service role client to bypass RLS policies for admin operations
      const serviceRoleClient = getServiceRoleClient()

      switch (bulkAction) {
        case 'activate':
          await serviceRoleClient
            .from('users')
            .update({ is_active: true })
            .in('id', userIds)
          break

        case 'deactivate':
          await serviceRoleClient
            .from('users')
            .update({ is_active: false })
            .in('id', userIds)
          break

        case 'role':
          await serviceRoleClient
            .from('users')
            .update({
              role: newRole,
              is_admin: newRole === 'admin'
            })
            .in('id', userIds)
          break

        case 'delete':
          // Soft delete by deactivating
          await serviceRoleClient
            .from('users')
            .update({ is_active: false })
            .in('id', userIds)
          break
      }

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        `BULK_${bulkAction.toUpperCase()}`,
        'users',
        userIds.join(','),
        {
          action: bulkAction,
          user_count: selectedUsers.length,
          usernames: selectedUsers.map(u => u.username),
          new_role: bulkAction === 'role' ? newRole : undefined
        }
      )

      alert(`Successfully ${bulkAction}d ${selectedUsers.length} users`)
      onUpdate()
      onClearSelection()
      onClose()
    } catch (err: any) {
      console.error('Error performing bulk action:', err)
      alert('Failed to perform bulk action: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleBulkMessage = async () => {
    if (selectedUsers.length === 0) {
      alert('No users selected')
      return
    }

    if (!messageTitle.trim() || !messageContent.trim()) {
      alert('Please fill in both title and message')
      return
    }

    setLoading(true)
    try {
      const notifications = selectedUsers.map(user => ({
        user_id: user.id,
        title: messageTitle,
        message: messageContent,
        category: messageCategory,
        is_read: false,
        admin_sender: adminUser?.email || 'admin',
        created_at: new Date().toISOString()
      }))

      const { error } = await serviceRoleClient
        .from('notifications')
        .insert(notifications)

      if (error) throw error

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        'BULK_MESSAGE',
        'notifications',
        selectedUsers.map(u => u.id).join(','),
        {
          title: messageTitle,
          message: messageContent,
          category: messageCategory,
          user_count: selectedUsers.length,
          usernames: selectedUsers.map(u => u.username)
        }
      )

      alert(`Successfully sent message to ${selectedUsers.length} users`)
      setMessageTitle('')
      setMessageContent('')
      onUpdate()
      onClearSelection()
      onClose()
    } catch (err: any) {
      console.error('Error sending bulk message:', err)
      alert('Failed to send bulk message: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = () => {
    const exportData = selectedUsers.map(user => {
      const data: any = {}
      
      if (exportFields.basic) {
        data.id = user.id
        data.username = user.username
        data.full_name = user.full_name
        data.email = user.email
        data.is_active = user.is_active
        data.role = user.role
        data.created_at = user.created_at
      }
      
      if (exportFields.financial) {
        data.total_shares = user.total_shares || 0
        data.total_invested = user.total_invested || 0
        data.total_commissions = user.total_commissions || 0
      }
      
      if (exportFields.kyc) {
        data.kyc_status = user.kyc_information?.kyc_status || 'none'
        data.kyc_legal_name = user.kyc_information?.full_legal_name || ''
        data.kyc_country = user.kyc_information?.country_name || ''
      }
      
      if (exportFields.referrals) {
        data.total_referrals = user.total_referrals || 0
      }
      
      return data
    })

    if (exportFormat === 'csv') {
      const headers = Object.keys(exportData[0] || {})
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => 
            JSON.stringify(row[header] || '')
          ).join(',')
        )
      ].join('\n')
      
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      URL.revokeObjectURL(url)
    } else {
      const jsonContent = JSON.stringify(exportData, null, 2)
      const blob = new Blob([jsonContent], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `users_export_${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)
    }

    // Log admin action
    logAdminAction(
      adminUser?.email || 'unknown',
      'EXPORT_USERS',
      'users',
      selectedUsers.map(u => u.id).join(','),
      {
        format: exportFormat,
        fields: exportFields,
        user_count: selectedUsers.length
      }
    )

    alert(`Exported ${selectedUsers.length} users as ${exportFormat.toUpperCase()}`)
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-white">
              🔧 Bulk Operations
            </h2>
            <p className="text-gray-400 mt-1">
              {selectedUsers.length} users selected
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        {/* Selected Users Preview */}
        <div className="p-4 bg-gray-800/50 border-b border-gray-700">
          <div className="flex flex-wrap gap-2 max-h-20 overflow-y-auto">
            {selectedUsers.slice(0, 10).map(user => (
              <span key={user.id} className="px-2 py-1 bg-blue-600/20 text-blue-400 text-xs rounded">
                {user.username}
              </span>
            ))}
            {selectedUsers.length > 10 && (
              <span className="px-2 py-1 bg-gray-600/20 text-gray-400 text-xs rounded">
                +{selectedUsers.length - 10} more
              </span>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700">
          <button
            onClick={() => setActiveTab('actions')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'actions'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            ⚡ Bulk Actions
          </button>
          <button
            onClick={() => setActiveTab('message')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'message'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            📨 Bulk Message
          </button>
          <button
            onClick={() => setActiveTab('export')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'export'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            📊 Export Data
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-300px)]">
          {/* Bulk Actions Tab */}
          {activeTab === 'actions' && (
            <div className="space-y-6">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Select Action</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Action to Perform
                    </label>
                    <select
                      value={bulkAction}
                      onChange={(e) => setBulkAction(e.target.value as any)}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    >
                      <option value="activate">✅ Activate Users</option>
                      <option value="deactivate">❌ Deactivate Users</option>
                      <option value="role">👑 Change Role</option>
                      <option value="delete">🗑️ Delete Users (Deactivate)</option>
                    </select>
                  </div>

                  {bulkAction === 'role' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        New Role
                      </label>
                      <select
                        value={newRole}
                        onChange={(e) => setNewRole(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                      >
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                      </select>
                    </div>
                  )}

                  <div className="bg-yellow-900/20 border border-yellow-500/20 rounded-lg p-4">
                    <h4 className="text-yellow-400 font-medium mb-2">⚠️ Warning</h4>
                    <p className="text-gray-300 text-sm">
                      This action will affect {selectedUsers.length} users. This action cannot be undone.
                    </p>
                  </div>

                  <button
                    onClick={handleBulkAction}
                    disabled={loading}
                    className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50"
                  >
                    {loading ? 'Processing...' : `Perform ${bulkAction} on ${selectedUsers.length} users`}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Bulk Message Tab */}
          {activeTab === 'message' && (
            <div className="space-y-6">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Send Message to All Selected Users</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Category
                    </label>
                    <select
                      value={messageCategory}
                      onChange={(e) => setMessageCategory(e.target.value as any)}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    >
                      <option value="system">📢 System Notification</option>
                      <option value="payment">💰 Payment Related</option>
                      <option value="commission">🤝 Commission Related</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Title *
                    </label>
                    <input
                      type="text"
                      value={messageTitle}
                      onChange={(e) => setMessageTitle(e.target.value)}
                      placeholder="Enter message title..."
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Message *
                    </label>
                    <textarea
                      value={messageContent}
                      onChange={(e) => setMessageContent(e.target.value)}
                      placeholder="Enter your message..."
                      rows={6}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    />
                  </div>

                  <button
                    onClick={handleBulkMessage}
                    disabled={loading || !messageTitle.trim() || !messageContent.trim()}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50"
                  >
                    {loading ? 'Sending...' : `Send Message to ${selectedUsers.length} Users`}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Export Tab */}
          {activeTab === 'export' && (
            <div className="space-y-6">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Export User Data</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Export Format
                    </label>
                    <select
                      value={exportFormat}
                      onChange={(e) => setExportFormat(e.target.value as any)}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    >
                      <option value="csv">📊 CSV (Excel Compatible)</option>
                      <option value="json">📄 JSON</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Fields to Export
                    </label>
                    <div className="space-y-2">
                      {Object.entries(exportFields).map(([key, value]) => (
                        <label key={key} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setExportFields(prev => ({
                              ...prev,
                              [key]: e.target.checked
                            }))}
                            className="mr-2"
                          />
                          <span className="text-gray-300 capitalize">{key} Information</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={handleExport}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg"
                  >
                    📊 Export {selectedUsers.length} Users as {exportFormat.toUpperCase()}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
