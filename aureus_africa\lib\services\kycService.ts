import { supabase, getServiceRoleClient } from '../supabase';

export interface KYCInformation {
  id: string;
  user_id: number;
  first_name: string;
  last_name: string;
  full_legal_name: string;
  id_type: 'national_id' | 'passport';
  id_number_encrypted: string;
  id_number_hash: string;
  phone_number: string;
  email_address: string;
  street_address: string;
  city: string;
  postal_code: string;
  country_code: string;
  country_name: string;
  data_consent_given: boolean;
  privacy_policy_accepted: boolean;
  kyc_status: 'pending' | 'completed' | 'rejected' | 'expired';
  kyc_completed_at: string;
  certificate_requested: boolean;
  certificate_generated_at?: string;
  certificate_sent_at?: string;
  created_at: string;
  updated_at: string;
}

export interface KYCAuditLog {
  id: string;
  kyc_id: string;
  user_id: number;
  action: string;
  field_changed?: string;
  old_value_hash?: string;
  new_value_hash?: string;
  performed_by_telegram_id?: number;
  performed_by_username?: string;
  ip_address?: string;
  user_agent?: string;
  performed_at: string;
}

export interface KYCSubmissionData {
  firstName: string;
  lastName: string;
  idType: 'national_id' | 'passport';
  idNumber: string;
  phoneNumber: string;
  emailAddress: string;
  streetAddress: string;
  city: string;
  postalCode: string;
  countryCode: string;
  countryName: string;
  dataConsent: boolean;
  privacyPolicyAccepted: boolean;
}

export class KYCService {
  /**
   * Get KYC information for a user
   */
  static async getKYCByUserId(userId: number): Promise<KYCInformation | null> {
    try {
      const serviceClient = getServiceRoleClient()
      const { data, error } = await serviceClient
        .from('kyc_information')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching KYC information:', error);
      throw error;
    }
  }

  /**
   * Check if user has completed KYC
   */
  static async checkKYCCompletion(userId: number): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('check_kyc_completion', { p_user_id: userId });

      if (error) throw error;
      return data || false;
    } catch (error) {
      console.error('Error checking KYC completion:', error);
      return false;
    }
  }

  /**
   * Submit new KYC information
   */
  static async submitKYC(userId: number, kycData: KYCSubmissionData): Promise<KYCInformation> {
    try {
      // Hash the ID number for duplicate checking
      const idNumberHash = await this.hashIdNumber(kycData.idNumber);
      
      // Encrypt sensitive data (in production, this should be done server-side)
      const encryptedIdNumber = btoa(kycData.idNumber); // Simple base64 encoding for demo

      const { data, error } = await supabase
        .from('kyc_information')
        .insert({
          user_id: userId,
          first_name: kycData.firstName,
          last_name: kycData.lastName,
          id_type: kycData.idType,
          id_number_encrypted: encryptedIdNumber,
          id_number_hash: idNumberHash,
          phone_number: kycData.phoneNumber,
          email_address: kycData.emailAddress,
          street_address: kycData.streetAddress,
          city: kycData.city,
          postal_code: kycData.postalCode,
          country_code: kycData.countryCode,
          country_name: kycData.countryName,
          data_consent_given: kycData.dataConsent,
          privacy_policy_accepted: kycData.privacyPolicyAccepted,
          kyc_status: 'pending'
        })
        .select()
        .single();

      if (error) throw error;

      // Log the KYC creation
      await this.logKYCAction(data.id, userId, 'created', undefined, 'web_dashboard');

      return data;
    } catch (error) {
      console.error('Error submitting KYC:', error);
      throw error;
    }
  }

  /**
   * Update existing KYC information
   */
  static async updateKYC(kycId: string, userId: number, updates: Partial<KYCSubmissionData>): Promise<KYCInformation> {
    try {
      const updateData: any = { ...updates };

      // If ID number is being updated, hash it
      if (updates.idNumber) {
        updateData.id_number_hash = await this.hashIdNumber(updates.idNumber);
        updateData.id_number_encrypted = btoa(updates.idNumber);
        delete updateData.idNumber; // Remove plain text
      }

      // Add updated timestamp
      updateData.updated_at = new Date().toISOString();

      const { data, error } = await supabase
        .from('kyc_information')
        .update(updateData)
        .eq('id', kycId)
        .eq('user_id', userId) // Ensure user can only update their own KYC
        .select()
        .single();

      if (error) throw error;

      // Log the KYC update
      await this.logKYCAction(kycId, userId, 'updated', undefined, 'web_dashboard');

      return data;
    } catch (error) {
      console.error('Error updating KYC:', error);
      throw error;
    }
  }

  /**
   * Get KYC audit logs for a user
   */
  static async getKYCAuditLogs(userId: number, limit: number = 10): Promise<KYCAuditLog[]> {
    try {
      const { data, error } = await supabase
        .from('kyc_audit_log')
        .select('*')
        .eq('user_id', userId)
        .order('performed_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching KYC audit logs:', error);
      return [];
    }
  }

  /**
   * Request certificate generation
   */
  static async requestCertificate(userId: number): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('kyc_information')
        .update({ 
          certificate_requested: true,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('kyc_status', 'completed')
        .select()
        .single();

      if (error) throw error;

      // Log the certificate request
      await this.logKYCAction(data.id, userId, 'certificate_requested', undefined, 'web_dashboard');

      return true;
    } catch (error) {
      console.error('Error requesting certificate:', error);
      return false;
    }
  }

  /**
   * Get certificate timeline
   */
  static async getCertificateTimeline(): Promise<string> {
    try {
      const { data, error } = await supabase.rpc('get_certificate_timeline');
      if (error) throw error;
      return data || '48 hours';
    } catch (error) {
      console.error('Error getting certificate timeline:', error);
      return '48 hours';
    }
  }

  /**
   * Sync KYC data with Telegram bot
   */
  static async syncWithTelegramBot(userId: number): Promise<boolean> {
    try {
      // This would typically call an API endpoint that syncs with the Telegram bot
      // For now, we'll just log the sync attempt
      console.log(`Syncing KYC data for user ${userId} with Telegram bot`);
      
      // In a real implementation, this might:
      // 1. Call a webhook to notify the Telegram bot
      // 2. Update a sync status table
      // 3. Queue a background job for synchronization
      
      return true;
    } catch (error) {
      console.error('Error syncing with Telegram bot:', error);
      return false;
    }
  }

  /**
   * Validate KYC data before submission
   */
  static validateKYCData(kycData: KYCSubmissionData): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // Name validation
    if (!kycData.firstName.trim() || !/^[a-zA-Z\s'-]+$/.test(kycData.firstName)) {
      errors.firstName = 'Valid first name is required';
    }
    if (!kycData.lastName.trim() || !/^[a-zA-Z\s'-]+$/.test(kycData.lastName)) {
      errors.lastName = 'Valid last name is required';
    }

    // ID validation
    if (kycData.idType === 'national_id' && kycData.countryCode === 'ZAF') {
      if (!/^\d{13}$/.test(kycData.idNumber)) {
        errors.idNumber = 'South African ID must be 13 digits';
      }
    } else if (kycData.idType === 'passport') {
      if (!/^[A-Z0-9]{6,20}$/i.test(kycData.idNumber)) {
        errors.idNumber = 'Invalid passport number format';
      }
    }

    // Contact validation
    if (!kycData.phoneNumber.trim()) {
      errors.phoneNumber = 'Phone number is required';
    }
    if (!kycData.emailAddress.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(kycData.emailAddress)) {
      errors.emailAddress = 'Valid email address is required';
    }

    // Address validation
    if (!kycData.streetAddress.trim()) {
      errors.streetAddress = 'Street address is required';
    }
    if (!kycData.city.trim()) {
      errors.city = 'City is required';
    }
    if (!kycData.postalCode.trim()) {
      errors.postalCode = 'Postal code is required';
    }

    // Consent validation
    if (!kycData.dataConsent) {
      errors.dataConsent = 'Data consent is required';
    }
    if (!kycData.privacyPolicyAccepted) {
      errors.privacyPolicyAccepted = 'Privacy policy acceptance is required';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Hash ID number for duplicate detection
   */
  private static async hashIdNumber(idNumber: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(idNumber);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Log KYC action for audit trail
   */
  private static async logKYCAction(
    kycId: string,
    userId: number,
    action: string,
    fieldChanged?: string,
    performedBy?: string
  ): Promise<void> {
    try {
      await supabase.rpc('log_kyc_action', {
        p_kyc_id: kycId,
        p_user_id: userId,
        p_action: action,
        p_field_changed: fieldChanged,
        p_performed_by_username: performedBy || 'web_dashboard'
      });
    } catch (error) {
      console.error('Error logging KYC action:', error);
      // Don't throw here as this is just logging
    }
  }
}
