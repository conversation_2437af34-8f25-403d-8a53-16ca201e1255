#!/usr/bin/env node

/**
 * EMERGENCY DIAGNOSTICS
 * 
 * Comprehensive investigation of login failures and network connectivity issues
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey);

const emergencyDiagnostics = async () => {
  try {
    console.log('🚨 EMERGENCY DIAGNOSTICS - Investigating critical issues...\n');

    // Test 1: Database Connectivity
    console.log('📋 Test 1: Database Connectivity Check');
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .select('count')
        .limit(1);
      
      if (error) {
        console.log('❌ Database connectivity FAILED:', error.message);
      } else {
        console.log('✅ Database connectivity OK');
      }
    } catch (err) {
      console.log('❌ Database connectivity ERROR:', err.message);
    }

    // Test 2: Supabase Auth Service Check
    console.log('\n📋 Test 2: Supabase Auth Service Check');
    try {
      const { data: { user }, error } = await supabaseClient.auth.getUser();
      console.log('✅ Auth service responding (no user logged in is expected)');
    } catch (err) {
      console.log('❌ Auth service ERROR:', err.message);
    }

    // Test 3: Detailed Telegram User Analysis
    console.log('\n📋 Test 3: Detailed Telegram User Analysis');
    const telegramId = '1393852532';
    
    // Check telegram_users table
    const { data: telegramUser, error: telegramError } = await supabaseAdmin
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (telegramError) {
      console.log('❌ Telegram user lookup FAILED:', telegramError.message);
      return;
    }

    console.log('📋 Telegram User Details:');
    console.log(`   Telegram ID: ${telegramUser.telegram_id}`);
    console.log(`   Username: ${telegramUser.username}`);
    console.log(`   User ID (linked): ${telegramUser.user_id}`);
    console.log(`   Registration Status: ${telegramUser.is_registered}`);

    if (!telegramUser.user_id) {
      console.log('❌ CRITICAL: Telegram user is not linked to any user record');
      return;
    }

    // Check linked user in users table
    const { data: linkedUser, error: linkedError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', telegramUser.user_id)
      .single();

    if (linkedError) {
      console.log('❌ Linked user lookup FAILED:', linkedError.message);
      return;
    }

    console.log('\n📋 Linked User Details:');
    console.log(`   User ID: ${linkedUser.id}`);
    console.log(`   Username: ${linkedUser.username}`);
    console.log(`   Email: ${linkedUser.email}`);
    console.log(`   Is Active: ${linkedUser.is_active}`);
    console.log(`   Is Admin: ${linkedUser.is_admin}`);
    console.log(`   Password Hash: ${linkedUser.password_hash ? 'SET' : 'NOT SET'}`);

    // Test 4: Password Verification
    console.log('\n📋 Test 4: Password Verification');
    const testPassword = 'Gunst0n5o0!@#';
    
    if (linkedUser.password_hash) {
      try {
        const isValid = await bcrypt.compare(testPassword, linkedUser.password_hash);
        console.log(`   Password verification: ${isValid ? '✅ SUCCESS' : '❌ FAILED'}`);
        
        if (!isValid) {
          console.log('   🔍 Testing alternative passwords...');
          const altPasswords = ['Gunst0n5o0!', 'admin123', 'password'];
          for (const pwd of altPasswords) {
            const altValid = await bcrypt.compare(pwd, linkedUser.password_hash);
            if (altValid) {
              console.log(`   ✅ FOUND WORKING PASSWORD: "${pwd}"`);
              break;
            }
          }
        }
      } catch (err) {
        console.log('   ❌ Password verification ERROR:', err.message);
      }
    }

    // Test 5: Supabase Auth User Check
    console.log('\n📋 Test 5: Supabase Auth User Check');
    try {
      // Try to sign in with the email and password
      const { data: authData, error: authError } = await supabaseClient.auth.signInWithPassword({
        email: linkedUser.email,
        password: testPassword
      });

      if (authError) {
        console.log('❌ Supabase Auth login FAILED:', authError.message);
        
        // Check if auth user exists
        console.log('   🔍 Checking if auth user exists...');
        const { data: { users }, error: listError } = await supabaseAdmin.auth.admin.listUsers();
        
        if (!listError) {
          const authUser = users.find(u => u.email === linkedUser.email);
          if (authUser) {
            console.log('   ✅ Auth user exists in Supabase Auth');
            console.log(`   Auth User ID: ${authUser.id}`);
          } else {
            console.log('   ❌ Auth user does NOT exist in Supabase Auth');
            console.log('   🔧 This might be the root cause - auth user missing');
          }
        }
      } else {
        console.log('✅ Supabase Auth login SUCCESS');
        // Sign out immediately
        await supabaseClient.auth.signOut();
      }
    } catch (err) {
      console.log('❌ Auth login test ERROR:', err.message);
    }

    // Test 6: Network Connectivity to External APIs
    console.log('\n📋 Test 6: Network Connectivity Check');
    try {
      const response = await fetch('https://backenster.com/api/app/config', {
        method: 'GET',
        timeout: 5000
      });
      console.log(`   backenster.com API: ${response.ok ? '✅ OK' : '❌ FAILED'} (${response.status})`);
    } catch (err) {
      console.log('   ❌ backenster.com API: CONNECTION FAILED -', err.message);
    }

    // Test 7: Site Content Table Check
    console.log('\n📋 Test 7: Site Content Table Check');
    try {
      const { data: siteContent, error: siteError } = await supabaseAdmin
        .from('site_content')
        .select('*')
        .limit(1);
      
      if (siteError) {
        console.log('❌ Site content table ERROR:', siteError.message);
      } else {
        console.log('✅ Site content table accessible');
      }
    } catch (err) {
      console.log('❌ Site content table ERROR:', err.message);
    }

    console.log('\n' + '='.repeat(60));
    console.log('🔍 DIAGNOSTIC SUMMARY:');
    console.log('1. Check database connectivity results above');
    console.log('2. Verify Supabase Auth user exists');
    console.log('3. Confirm password verification status');
    console.log('4. Review network connectivity issues');
    console.log('='.repeat(60));

  } catch (error) {
    console.error('❌ Emergency diagnostics failed:', error);
  }
};

emergencyDiagnostics();
