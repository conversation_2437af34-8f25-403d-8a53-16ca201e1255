-- =====================================================
-- EMAIL VERIFICATION SYSTEM DATABASE SCHEMA
-- =====================================================
-- 
-- This script creates the required database tables for the
-- comprehensive email verification and account management system
-- using Resend email service integration.
--
-- Tables created:
-- 1. email_verification_codes - PIN verification codes
-- 2. account_change_logs - Audit trail for account changes
-- 3. newsletter_subscriptions - Newsletter management
-- 4. email_delivery_logs - Email delivery tracking
--
-- =====================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. EMAIL VERIFICATION CODES TABLE
-- =====================================================
-- Stores 6-digit PIN verification codes for email verification
-- with secure hashing, expiration, and attempt tracking

CREATE TABLE IF NOT EXISTS email_verification_codes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    code_hash VARCHAR(255) NOT NULL, -- bcrypt hashed 6-digit code
    purpose VARCHAR(50) NOT NULL CHECK (purpose IN ('registration', 'account_update', 'withdrawal', 'password_reset')),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER DEFAULT 0 CHECK (attempts >= 0 AND attempts <= 10),
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for email_verification_codes
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_user_id ON email_verification_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_email ON email_verification_codes(email);
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_purpose ON email_verification_codes(purpose);
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_expires_at ON email_verification_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_verified_at ON email_verification_codes(verified_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_email_verification_codes_user_email_purpose 
ON email_verification_codes(user_id, email, purpose);

CREATE INDEX IF NOT EXISTS idx_email_verification_codes_active 
ON email_verification_codes(user_id, email, purpose, expires_at) 
WHERE verified_at IS NULL;

-- =====================================================
-- 2. ACCOUNT CHANGE LOGS TABLE
-- =====================================================
-- Comprehensive audit trail for all account changes
-- with email verification tracking and security monitoring

CREATE TABLE IF NOT EXISTS account_change_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    field_changed VARCHAR(100) NOT NULL,
    old_value_hash VARCHAR(255), -- bcrypt hashed old value for security
    new_value_hash VARCHAR(255), -- bcrypt hashed new value for security
    verification_method VARCHAR(50) NOT NULL CHECK (verification_method IN ('email_code', 'password', 'admin_override', 'system')),
    email_verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    admin_user_id INTEGER REFERENCES users(id), -- If changed by admin
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for account_change_logs
CREATE INDEX IF NOT EXISTS idx_account_change_logs_user_id ON account_change_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_account_change_logs_field_changed ON account_change_logs(field_changed);
CREATE INDEX IF NOT EXISTS idx_account_change_logs_verification_method ON account_change_logs(verification_method);
CREATE INDEX IF NOT EXISTS idx_account_change_logs_email_verified ON account_change_logs(email_verified);
CREATE INDEX IF NOT EXISTS idx_account_change_logs_created_at ON account_change_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_account_change_logs_ip_address ON account_change_logs(ip_address);

-- Composite index for security monitoring
CREATE INDEX IF NOT EXISTS idx_account_change_logs_security 
ON account_change_logs(user_id, field_changed, email_verified, created_at);

-- =====================================================
-- 3. NEWSLETTER SUBSCRIPTIONS TABLE - COMPATIBILITY UPDATE
-- =====================================================
-- NOTE: email_preferences table already exists with comprehensive settings
-- Creating newsletter_subscriptions table to complement existing email_preferences

CREATE TABLE IF NOT EXISTS newsletter_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    subscription_categories JSONB DEFAULT '[]'::jsonb, -- Array of category strings
    subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    unsubscribed_at TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}'::jsonb, -- User preferences (frequency, format, etc.)
    unsubscribe_token VARCHAR(255) UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'),
    source VARCHAR(50) DEFAULT 'website', -- 'website', 'bot', 'admin', 'import'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for newsletter_subscriptions
CREATE INDEX IF NOT EXISTS idx_newsletter_subscriptions_user_id ON newsletter_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscriptions_email ON newsletter_subscriptions(email);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscriptions_unsubscribe_token ON newsletter_subscriptions(unsubscribe_token);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscriptions_subscribed_at ON newsletter_subscriptions(subscribed_at);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscriptions_unsubscribed_at ON newsletter_subscriptions(unsubscribed_at);

-- Index for active subscriptions
CREATE INDEX IF NOT EXISTS idx_newsletter_subscriptions_active
ON newsletter_subscriptions(email, subscription_categories)
WHERE unsubscribed_at IS NULL;

-- GIN index for JSONB category searches
CREATE INDEX IF NOT EXISTS idx_newsletter_subscriptions_categories
ON newsletter_subscriptions USING GIN (subscription_categories);

-- =====================================================
-- 4. EMAIL DELIVERY LOGS TABLE - COMPATIBILITY UPDATE
-- =====================================================
-- NOTE: email_delivery_log table already exists in database
-- Adding missing columns and indexes for email verification system

-- Add missing columns to existing email_delivery_log table
ALTER TABLE email_delivery_log
ADD COLUMN IF NOT EXISTS template_used VARCHAR(100),
ADD COLUMN IF NOT EXISTS error_message TEXT,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing columns to match our requirements
ALTER TABLE email_delivery_log
ALTER COLUMN delivery_status TYPE VARCHAR(50),
ALTER COLUMN delivery_status SET DEFAULT 'sent';

-- Add check constraint for delivery status
ALTER TABLE email_delivery_log
ADD CONSTRAINT IF NOT EXISTS chk_delivery_status
CHECK (delivery_status IN ('sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed', 'spam'));

-- Create missing indexes for email_delivery_log
CREATE INDEX IF NOT EXISTS idx_email_delivery_log_user_id ON email_delivery_log(user_id);
CREATE INDEX IF NOT EXISTS idx_email_delivery_log_email_address ON email_delivery_log(email_address);
CREATE INDEX IF NOT EXISTS idx_email_delivery_log_email_type ON email_delivery_log(email_type);
CREATE INDEX IF NOT EXISTS idx_email_delivery_log_resend_message_id ON email_delivery_log(resend_message_id);
CREATE INDEX IF NOT EXISTS idx_email_delivery_log_delivery_status ON email_delivery_log(delivery_status);
CREATE INDEX IF NOT EXISTS idx_email_delivery_log_created_at ON email_delivery_log(created_at);

-- Composite indexes for analytics
CREATE INDEX IF NOT EXISTS idx_email_delivery_log_analytics
ON email_delivery_log(email_type, delivery_status, created_at);

CREATE INDEX IF NOT EXISTS idx_email_delivery_log_user_analytics
ON email_delivery_log(user_id, email_type, delivery_status, created_at);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================
-- Secure access to email verification data

-- Enable RLS on new tables (existing tables already have RLS enabled)
ALTER TABLE email_verification_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE account_change_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE newsletter_subscriptions ENABLE ROW LEVEL SECURITY;
-- Note: email_delivery_log already has RLS enabled

-- Email verification codes - users can only access their own codes
CREATE POLICY "Users can access their own verification codes" ON email_verification_codes
    FOR ALL USING (auth.uid()::text = user_id::text);

-- Account change logs - users can view their own logs, admins can view all
CREATE POLICY "Users can view their own account change logs" ON account_change_logs
    FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Admins can view all account change logs" ON account_change_logs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid()::integer 
            AND role = 'admin'
        )
    );

-- Newsletter subscriptions - users can manage their own subscriptions
CREATE POLICY "Users can manage their own newsletter subscriptions" ON newsletter_subscriptions
    FOR ALL USING (auth.uid()::text = user_id::text);

-- Email delivery logs - admins only for privacy (using existing table name)
CREATE POLICY IF NOT EXISTS "Admins can access email delivery logs" ON email_delivery_log
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE auth_user_id = auth.uid()
            AND is_admin = true
        )
    );

-- =====================================================
-- CLEANUP FUNCTIONS
-- =====================================================
-- Automated cleanup of expired verification codes

CREATE OR REPLACE FUNCTION cleanup_expired_verification_codes()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM email_verification_codes 
    WHERE expires_at < NOW() - INTERVAL '24 hours';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run cleanup daily (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-verification-codes', '0 2 * * *', 'SELECT cleanup_expired_verification_codes();');

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get active verification code for user
CREATE OR REPLACE FUNCTION get_active_verification_code(
    p_user_id INTEGER,
    p_email VARCHAR(255),
    p_purpose VARCHAR(50)
)
RETURNS TABLE (
    id UUID,
    code_hash VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        evc.id,
        evc.code_hash,
        evc.expires_at,
        evc.attempts,
        evc.created_at
    FROM email_verification_codes evc
    WHERE evc.user_id = p_user_id
        AND evc.email = p_email
        AND evc.purpose = p_purpose
        AND evc.verified_at IS NULL
        AND evc.expires_at > NOW()
    ORDER BY evc.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to check if email is verified for purpose
CREATE OR REPLACE FUNCTION is_email_verified(
    p_user_id INTEGER,
    p_email VARCHAR(255),
    p_purpose VARCHAR(50),
    p_max_age_hours INTEGER DEFAULT 24
)
RETURNS BOOLEAN AS $$
DECLARE
    verification_time TIMESTAMP WITH TIME ZONE;
BEGIN
    SELECT verified_at INTO verification_time
    FROM email_verification_codes
    WHERE user_id = p_user_id
        AND email = p_email
        AND purpose = p_purpose
        AND verified_at IS NOT NULL
        AND verified_at > NOW() - (p_max_age_hours || ' hours')::INTERVAL
    ORDER BY verified_at DESC
    LIMIT 1;
    
    RETURN verification_time IS NOT NULL;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIAL DATA
-- =====================================================

-- Insert default newsletter categories
INSERT INTO newsletter_subscriptions (user_id, email, subscription_categories, preferences, source)
SELECT 
    id,
    email,
    '["announcements", "updates"]'::jsonb,
    '{"frequency": "weekly", "format": "html"}'::jsonb,
    'migration'
FROM users 
WHERE email IS NOT NULL 
    AND email != ''
    AND NOT EXISTS (
        SELECT 1 FROM newsletter_subscriptions ns 
        WHERE ns.user_id = users.id
    )
ON CONFLICT DO NOTHING;

-- =====================================================
-- GRANTS AND PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON email_verification_codes TO authenticated;
GRANT SELECT, INSERT ON account_change_logs TO authenticated;
GRANT ALL ON newsletter_subscriptions TO authenticated;
GRANT SELECT ON email_delivery_logs TO authenticated;

-- Grant admin permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Email verification system database schema created successfully!';
    RAISE NOTICE '📧 Tables created: email_verification_codes, account_change_logs, newsletter_subscriptions, email_delivery_logs';
    RAISE NOTICE '🔒 Row Level Security policies applied';
    RAISE NOTICE '🔧 Helper functions and cleanup procedures installed';
    RAISE NOTICE '📊 Indexes optimized for performance';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Ready for Resend email service integration!';
END $$;
