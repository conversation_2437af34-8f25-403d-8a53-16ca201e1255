import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkPasswordHash() {
  try {
    const { data } = await supabase
      .from('users')
      .select('password_hash')
      .eq('email', '<EMAIL>')
      .single();
    
    console.log('Stored hash:', data.password_hash);
    
    // Test our hash function
    const encoder = new TextEncoder();
    const testData = encoder.encode('TestPassword123' + 'aureus_salt_2024');
    const hashBuffer = await crypto.subtle.digest('SHA-256', testData);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const computedHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    
    console.log('Computed hash:', computedHash);
    console.log('Match:', data.password_hash === computedHash);
    
    // Let's also check the length
    console.log('Stored hash length:', data.password_hash.length);
    console.log('Computed hash length:', computedHash.length);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkPasswordHash();
