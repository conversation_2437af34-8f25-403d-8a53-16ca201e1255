#!/usr/bin/env node

/**
 * CREATE USER SESSIONS TABLE
 * 
 * This script creates the user_sessions table with security features
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createSessionTable() {
  console.log('🔐 Creating user_sessions table...');
  
  try {
    // Read and execute the SQL file
    const sqlContent = fs.readFileSync('create-session-table.sql', 'utf8');
    
    // Split SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Executing ${statements.length} SQL statements...`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.includes('CREATE TABLE') || 
          statement.includes('CREATE INDEX') || 
          statement.includes('ALTER TABLE') ||
          statement.includes('CREATE POLICY') ||
          statement.includes('CREATE FUNCTION') ||
          statement.includes('CREATE TRIGGER') ||
          statement.includes('GRANT') ||
          statement.includes('INSERT INTO') ||
          statement.includes('CREATE VIEW') ||
          statement.includes('COMMENT ON')) {
        
        console.log(`   ${i + 1}. Executing: ${statement.substring(0, 50)}...`);
        
        const { error } = await supabase.rpc('exec_sql', { 
          sql_query: statement + ';' 
        });

        if (error) {
          console.log(`   ⚠️ Statement ${i + 1} result: ${error.message}`);
          // Continue with other statements
        } else {
          console.log(`   ✅ Statement ${i + 1} executed successfully`);
        }
      }
    }

    // Test table creation by checking if it exists
    console.log('\n🧪 Testing table creation...');
    
    const { data: tableCheck, error: tableError } = await supabase
      .from('user_sessions')
      .select('count')
      .limit(0);

    if (tableError) {
      console.log('❌ Table creation failed:', tableError.message);
      return false;
    } else {
      console.log('✅ user_sessions table created successfully');
    }

    // Test RLS policies
    console.log('🛡️ Testing Row Level Security...');
    
    const { data: rlsTest, error: rlsError } = await supabase
      .from('user_sessions')
      .select('*')
      .limit(1);

    if (rlsError && rlsError.message.includes('row-level security')) {
      console.log('✅ Row Level Security is active');
    } else {
      console.log('⚠️ RLS may not be properly configured');
    }

    console.log('\n🎉 Session table setup completed!');
    console.log('📋 Features enabled:');
    console.log('   ✅ Secure session storage');
    console.log('   ✅ Automatic expiration');
    console.log('   ✅ Activity tracking');
    console.log('   ✅ Row Level Security');
    console.log('   ✅ Performance indexes');

    return true;

  } catch (error) {
    console.error('❌ Session table creation failed:', error);
    return false;
  }
}

// Alternative: Create table with direct SQL if RPC doesn't work
async function createSessionTableDirect() {
  console.log('🔐 Creating user_sessions table (direct method)...');
  
  try {
    // Create the basic table structure
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS public.user_sessions (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(64) UNIQUE NOT NULL,
          user_id INTEGER NOT NULL,
          user_email VARCHAR(255) NOT NULL,
          ip_address INET,
          user_agent TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
          is_active BOOLEAN DEFAULT true,
          invalidated_at TIMESTAMP WITH TIME ZONE
      );
    `;

    console.log('📝 Creating table structure...');
    
    // Use a simple approach - just try to insert a test record to see if table exists
    const { error: testError } = await supabase
      .from('user_sessions')
      .select('id')
      .limit(1);

    if (testError && testError.message.includes('does not exist')) {
      console.log('❌ Table does not exist. Manual creation required.');
      console.log('\n📋 MANUAL SETUP REQUIRED:');
      console.log('1. Go to Supabase Dashboard > SQL Editor');
      console.log('2. Copy and paste the contents of create-session-table.sql');
      console.log('3. Execute the SQL script');
      console.log('4. Re-run this script to test');
      return false;
    } else {
      console.log('✅ user_sessions table already exists or is accessible');
      return true;
    }

  } catch (error) {
    console.error('❌ Direct table creation failed:', error);
    return false;
  }
}

// Run the table creation
console.log('🚀 Starting session table setup...\n');

createSessionTable()
  .then(success => {
    if (!success) {
      console.log('\n🔄 Trying alternative method...');
      return createSessionTableDirect();
    }
    return success;
  })
  .then(success => {
    if (success) {
      console.log('\n✅ Session management system ready!');
      process.exit(0);
    } else {
      console.log('\n❌ Session table setup failed. Manual intervention required.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
