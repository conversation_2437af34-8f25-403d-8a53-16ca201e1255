/**
 * EMAIL PREFERENCES SERVICE
 * 
 * Integrates with existing email_preferences table to manage user email settings
 * and determine if emails should be sent based on user preferences.
 * 
 * Features:
 * - Integration with existing email_preferences table
 * - Granular email type preferences
 * - Newsletter subscription management
 * - Unsubscribe functionality
 * - Preference validation before sending emails
 */

import { supabase } from './supabase';

export interface EmailPreferences {
  id: string;
  user_id: number;
  welcome_emails: boolean;
  commission_notifications: boolean;
  share_purchase_confirmations: boolean;
  password_reset_emails: boolean;
  admin_newsletters: boolean;
  upline_communications: boolean;
  withdrawal_confirmations: boolean;
  conversion_confirmations: boolean;
  investment_confirmations: boolean;
  dividend_notifications: boolean;
  security_alerts: boolean;
  system_announcements: boolean;
  marketing_emails: boolean;
  educational_content: boolean;
  frequency: 'immediate' | 'daily' | 'weekly' | 'monthly';
  created_at: string;
  updated_at: string;
}

export interface EmailTypeMapping {
  verification_registration: 'security_alerts';
  verification_account_update: 'security_alerts';
  verification_withdrawal: 'withdrawal_confirmations';
  verification_password_reset: 'password_reset_emails';
  password_reset: 'password_reset_emails';
  account_change: 'security_alerts';
  bulk_email: 'admin_newsletters';
  newsletter: 'admin_newsletters';
  commission_update: 'commission_notifications';
  share_purchase: 'share_purchase_confirmations';
  withdrawal_confirmation: 'withdrawal_confirmations';
  conversion_confirmation: 'conversion_confirmations';
  investment_confirmation: 'investment_confirmations';
  dividend_notification: 'dividend_notifications';
  system_announcement: 'system_announcements';
  marketing: 'marketing_emails';
  educational: 'educational_content';
  welcome: 'welcome_emails';
}

class EmailPreferencesService {
  private emailTypeMapping: EmailTypeMapping = {
    verification_registration: 'security_alerts',
    verification_account_update: 'security_alerts',
    verification_withdrawal: 'withdrawal_confirmations',
    verification_password_reset: 'password_reset_emails',
    password_reset: 'password_reset_emails',
    account_change: 'security_alerts',
    bulk_email: 'admin_newsletters',
    newsletter: 'admin_newsletters',
    commission_update: 'commission_notifications',
    share_purchase: 'share_purchase_confirmations',
    withdrawal_confirmation: 'withdrawal_confirmations',
    conversion_confirmation: 'conversion_confirmations',
    investment_confirmation: 'investment_confirmations',
    dividend_notification: 'dividend_notifications',
    system_announcement: 'system_announcements',
    marketing: 'marketing_emails',
    educational: 'educational_content',
    welcome: 'welcome_emails'
  };

  /**
   * Get user's email preferences
   */
  async getUserPreferences(userId: number): Promise<EmailPreferences | null> {
    try {
      const { data, error } = await supabase
        .from('email_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No preferences found, create default preferences
          return await this.createDefaultPreferences(userId);
        }
        console.error('❌ Error fetching email preferences:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('❌ Error in getUserPreferences:', error);
      return null;
    }
  }

  /**
   * Create default email preferences for a user
   */
  async createDefaultPreferences(userId: number): Promise<EmailPreferences | null> {
    try {
      const defaultPreferences = {
        user_id: userId,
        welcome_emails: true,
        commission_notifications: true,
        share_purchase_confirmations: true,
        password_reset_emails: true,
        admin_newsletters: true,
        upline_communications: true,
        withdrawal_confirmations: true,
        conversion_confirmations: true,
        investment_confirmations: true,
        dividend_notifications: true,
        security_alerts: true,
        system_announcements: true,
        marketing_emails: true,
        educational_content: true,
        frequency: 'immediate' as const
      };

      const { data, error } = await supabase
        .from('email_preferences')
        .insert(defaultPreferences)
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating default preferences:', error);
        return null;
      }

      console.log(`✅ Created default email preferences for user ${userId}`);
      return data;
    } catch (error) {
      console.error('❌ Error in createDefaultPreferences:', error);
      return null;
    }
  }

  /**
   * Check if user allows a specific email type
   */
  async canSendEmail(userId: number, emailType: string): Promise<boolean> {
    try {
      // Security-critical emails (verification codes) are always allowed
      const criticalTypes = [
        'verification_registration',
        'verification_account_update', 
        'verification_withdrawal',
        'verification_password_reset',
        'password_reset',
        'security_alerts'
      ];

      if (criticalTypes.includes(emailType)) {
        return true;
      }

      const preferences = await this.getUserPreferences(userId);
      if (!preferences) {
        // If no preferences found, allow critical emails only
        return criticalTypes.includes(emailType);
      }

      // Map email type to preference field
      const preferenceField = this.emailTypeMapping[emailType as keyof EmailTypeMapping];
      if (!preferenceField) {
        console.warn(`⚠️ Unknown email type: ${emailType}`);
        return false;
      }

      return preferences[preferenceField] === true;
    } catch (error) {
      console.error('❌ Error checking email permissions:', error);
      // Default to allowing critical emails only
      return [
        'verification_registration',
        'verification_account_update',
        'verification_withdrawal', 
        'verification_password_reset',
        'password_reset',
        'security_alerts'
      ].includes(emailType);
    }
  }

  /**
   * Update user's email preferences
   */
  async updatePreferences(userId: number, updates: Partial<EmailPreferences>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('email_preferences')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Error updating email preferences:', error);
        return false;
      }

      console.log(`✅ Updated email preferences for user ${userId}`);
      return true;
    } catch (error) {
      console.error('❌ Error in updatePreferences:', error);
      return false;
    }
  }

  /**
   * Unsubscribe user from all non-critical emails
   */
  async unsubscribeFromAll(userId: number): Promise<boolean> {
    try {
      const updates = {
        welcome_emails: false,
        commission_notifications: false,
        share_purchase_confirmations: false,
        admin_newsletters: false,
        upline_communications: false,
        withdrawal_confirmations: false,
        conversion_confirmations: false,
        investment_confirmations: false,
        dividend_notifications: false,
        system_announcements: false,
        marketing_emails: false,
        educational_content: false,
        // Keep security-critical emails enabled
        password_reset_emails: true,
        security_alerts: true
      };

      return await this.updatePreferences(userId, updates);
    } catch (error) {
      console.error('❌ Error in unsubscribeFromAll:', error);
      return false;
    }
  }

  /**
   * Get users subscribed to a specific email type
   */
  async getSubscribedUsers(emailType: string): Promise<number[]> {
    try {
      const preferenceField = this.emailTypeMapping[emailType as keyof EmailTypeMapping];
      if (!preferenceField) {
        console.warn(`⚠️ Unknown email type: ${emailType}`);
        return [];
      }

      const { data, error } = await supabase
        .from('email_preferences')
        .select('user_id')
        .eq(preferenceField, true);

      if (error) {
        console.error('❌ Error fetching subscribed users:', error);
        return [];
      }

      return data.map(row => row.user_id);
    } catch (error) {
      console.error('❌ Error in getSubscribedUsers:', error);
      return [];
    }
  }

  /**
   * Get email preferences summary for admin dashboard
   */
  async getPreferencesSummary(): Promise<{
    totalUsers: number;
    subscribedByType: Record<string, number>;
    unsubscribedUsers: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('email_preferences')
        .select('*');

      if (error) {
        console.error('❌ Error fetching preferences summary:', error);
        return { totalUsers: 0, subscribedByType: {}, unsubscribedUsers: 0 };
      }

      const totalUsers = data.length;
      const subscribedByType: Record<string, number> = {};
      let unsubscribedUsers = 0;

      // Count subscriptions by type
      Object.entries(this.emailTypeMapping).forEach(([emailType, preferenceField]) => {
        subscribedByType[emailType] = data.filter(pref => pref[preferenceField] === true).length;
      });

      // Count users who have unsubscribed from all non-critical emails
      unsubscribedUsers = data.filter(pref => 
        !pref.admin_newsletters && 
        !pref.marketing_emails && 
        !pref.system_announcements
      ).length;

      return { totalUsers, subscribedByType, unsubscribedUsers };
    } catch (error) {
      console.error('❌ Error in getPreferencesSummary:', error);
      return { totalUsers: 0, subscribedByType: {}, unsubscribedUsers: 0 };
    }
  }

  /**
   * Migrate users to newsletter_subscriptions table
   */
  async migrateToNewsletterSubscriptions(): Promise<void> {
    try {
      console.log('🔄 Starting newsletter subscription migration...');

      // Get all users with email preferences
      const { data: preferences, error: prefError } = await supabase
        .from('email_preferences')
        .select('user_id, admin_newsletters, marketing_emails, system_announcements, educational_content');

      if (prefError) {
        console.error('❌ Error fetching preferences for migration:', prefError);
        return;
      }

      // Get user emails
      const userIds = preferences.map(p => p.user_id);
      const { data: users, error: userError } = await supabase
        .from('users')
        .select('id, email')
        .in('id', userIds)
        .not('email', 'is', null);

      if (userError) {
        console.error('❌ Error fetching users for migration:', userError);
        return;
      }

      // Create newsletter subscriptions
      const subscriptions = users.map(user => {
        const userPrefs = preferences.find(p => p.user_id === user.id);
        const categories = [];

        if (userPrefs?.admin_newsletters) categories.push('newsletters');
        if (userPrefs?.marketing_emails) categories.push('marketing');
        if (userPrefs?.system_announcements) categories.push('announcements');
        if (userPrefs?.educational_content) categories.push('educational');

        return {
          user_id: user.id,
          email: user.email,
          subscription_categories: categories,
          preferences: { frequency: 'weekly', format: 'html' },
          source: 'migration'
        };
      }).filter(sub => sub.subscription_categories.length > 0);

      if (subscriptions.length > 0) {
        const { error: insertError } = await supabase
          .from('newsletter_subscriptions')
          .insert(subscriptions);

        if (insertError) {
          console.error('❌ Error inserting newsletter subscriptions:', insertError);
        } else {
          console.log(`✅ Migrated ${subscriptions.length} newsletter subscriptions`);
        }
      }

    } catch (error) {
      console.error('❌ Error in newsletter migration:', error);
    }
  }
}

// Export singleton instance
export const emailPreferencesService = new EmailPreferencesService();
export default emailPreferencesService;
