-- Clean up the duplicate columns that were mistakenly added
-- The system should use the existing telegram_users table instead

-- Remove the duplicate telegram_username column from users table
ALTER TABLE users DROP COLUMN IF EXISTS telegram_username;

-- Remove the duplicate phone_number column from users table  
-- (Keep this one if you want phone numbers, or remove if not needed)
-- ALTER TABLE users DROP COLUMN IF EXISTS phone_number;

-- Drop the index that was created for the duplicate column
DROP INDEX IF EXISTS idx_users_telegram_username;

-- Verify the cleanup
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
AND column_name IN ('phone_number', 'telegram_username')
ORDER BY column_name;
