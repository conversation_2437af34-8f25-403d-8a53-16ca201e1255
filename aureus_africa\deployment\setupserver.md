# 🚀 Aureus Alliance Website - cPanel Ubuntu Server Setup Guide

## ⚠️ **IMPORTANT: About public_html folder**

**NO, do NOT remove everything in public_html!**

- If you have other websites/files in public_html, keep them
- We'll create a subfolder for your Aureus Alliance website
- Your existing websites will continue to work

---

## 📋 **Step-by-Step Setup Instructions**

### **Step 1: Check Your Current Setup**

In your cPanel Terminal, run these commands:

```bash
# Check where you are
pwd

# Check what's in your home directory
ls -la

# Check if public_html exists and what's in it
ls -la public_html/

# Check if Node.js and Git are installed
node --version
npm --version
git --version
```

### **Step 2: Install Required Software (if not installed)**

```bash
# Install Node.js 20 LTS
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
sudo apt-get install -y nodejs

# Install Git (if not installed)
sudo apt-get install -y git

# Verify installation
node --version
npm --version
git --version

# Install PM2 for managing your app
npm install -g pm2
```

### **Step 3: Clone Your Repository**

```bash
# Go to your public_html folder
cd public_html

# Clone your repository
git clone https://github.com/yourusername/aureus_bot.git aureus-alliance

# Navigate to the website directory
cd aureus-alliance/aureus_africa

# Create logs directory
mkdir -p logs
```

### **Step 4: Build Your Website**

```bash
# Make sure you're in the aureus_africa directory
cd ~/public_html/aureus-alliance/aureus_africa

# Install dependencies
npm install

# Build the production version
npm run build
```

### **Step 5: Create Environment Configuration**

```bash
# Make sure you're in the aureus_africa directory
cd ~/public_html/aureus-alliance/aureus_africa

# Create your environment file
nano .env
```

**Copy and paste this into the .env file:**
```
NODE_ENV=production
PORT=8002

# Replace these with your actual Supabase values
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Application settings
VITE_APP_NAME=Aureus Alliance Holdings
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production

# Replace with your actual Resend API key
RESEND_API_KEY=your_resend_api_key_here
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Aureus Alliance Holdings

VITE_DEBUG=false
```

**Save the file:** Press `Ctrl+X`, then `Y`, then `Enter`

### **Step 6: Install Dependencies and Start Your Website**

```bash
# Install all required packages
npm install

# Create PM2 configuration file
nano ecosystem.config.js
```

**Copy and paste this:**
```javascript
module.exports = {
  apps: [{
    name: 'aureus-alliance',
    script: 'server.js',
    instances: 1,
    env: {
      NODE_ENV: 'production',
      PORT: 8002
    },
    error_file: './logs/error.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

**Save the file:** Press `Ctrl+X`, then `Y`, then `Enter`

### **Step 7: Start Your Website**

```bash
# Start your website with PM2
pm2 start ecosystem.config.js

# Check if it's running
pm2 status

# Save PM2 configuration
pm2 save
```

### **Step 8: Set Up Domain/Subdomain in cPanel**

1. **Go to cPanel main dashboard**
2. **Find "Subdomains" section**
3. **Create a new subdomain:**
   - Subdomain: `app` (or whatever you want)
   - Domain: your main domain
   - Document Root: `public_html/aureus-alliance/aureus_africa/dist`
4. **Click "Create"**

### **Step 9: Create .htaccess File for React Router**

```bash
# Go to your dist folder
cd ~/public_html/aureus-alliance/aureus_africa/dist

# Create .htaccess file
nano .htaccess
```

**Copy and paste this:**
```apache
RewriteEngine On

# Handle API routes - proxy to your Node.js server
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^api/(.*)$ http://localhost:8002/api/$1 [P,L]

# Handle React Router - serve index.html for all other routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

**Save the file:** Press `Ctrl+X`, then `Y`, then `Enter`

---

## ✅ **Testing Your Website**

```bash
# Check if your app is running
pm2 status

# View logs if there are issues
pm2 logs aureus-alliance

# Test if server responds
curl http://localhost:8002
```

**Visit your website:** `http://app.yourdomain.com` (or whatever subdomain you created)

---

## 🔧 **Common Commands**

```bash
# Restart your website
pm2 restart aureus-alliance

# Stop your website
pm2 stop aureus-alliance

# View real-time logs
pm2 logs aureus-alliance

# Check server status
pm2 status
```

---

## 🆘 **If Something Goes Wrong**

1. **Check PM2 status:** `pm2 status`
2. **Check logs:** `pm2 logs aureus-alliance`
3. **Restart app:** `pm2 restart aureus-alliance`
4. **Check if port 8002 is free:** `netstat -tlnp | grep :8002`

---

## 🔄 **Future Updates (Using Git)**

To update your website when you make changes:

```bash
# Go to your repository directory
cd ~/public_html/aureus-alliance

# Pull latest changes
git pull origin main

# Go to the website directory
cd aureus_africa

# Install any new dependencies
npm install

# Rebuild the website
npm run build

# Restart the server
pm2 restart aureus-alliance
```

## 📁 **Final Folder Structure**

Your `public_html` should look like this:
```
public_html/
├── (your existing files - keep them!)
└── aureus-alliance/           (git repository)
    ├── aureus-bot-new.js     (telegram bot - don't touch)
    ├── package.json          (bot dependencies)
    └── aureus_africa/        (website directory)
        ├── dist/             (built React app)
        ├── api/              (API files)
        ├── server.js         (Express server)
        ├── package.json      (website dependencies)
        ├── .env             (configuration)
        ├── ecosystem.config.js (PM2 config)
        └── logs/            (log files)
```

**That's it! Your website should now be running!** 🎉
