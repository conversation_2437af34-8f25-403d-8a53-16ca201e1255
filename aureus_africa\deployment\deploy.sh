#!/bin/bash

# Aureus Alliance Website - Auto-Deploy Script for cPanel Git
# This script runs automatically when you push to GitHub

set -e

echo "🚀 Auto-Deploy: Starting Aureus Alliance Website Update..."

# Get current directory (should be the repository root)
REPO_PATH=$(pwd)
echo "📁 Repository path: $REPO_PATH"

# Navigate to the website directory
cd "$REPO_PATH"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Are we in the right directory?"
    exit 1
fi

# Install/update dependencies
echo "📦 Installing dependencies..."
npm install

# Build the production version
echo "🏗️ Building production version..."
npm run build

# Check if PM2 is available and restart application
if command -v pm2 &> /dev/null; then
    if pm2 list | grep -q "aureus-alliance"; then
        echo "🔄 Restarting application..."
        pm2 restart aureus-alliance
    else
        echo "🚀 Starting application for first time..."
        pm2 start server.js --name aureus-alliance
        pm2 save
    fi
else
    echo "⚠️  PM2 not found. Please install PM2 to manage the application."
fi

echo "✅ Auto-Deploy Complete!"
echo "🌐 Website updated successfully!"

# Log the deployment
echo "$(date): Auto-deployment completed from Git push" >> deployment.log
