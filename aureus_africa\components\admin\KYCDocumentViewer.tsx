import React, { useState } from 'react';
import { supabase } from '../../lib/supabase';

interface KYCDocumentViewerProps {
  kycData: any;
  onClose: () => void;
}

export const KYCDocumentViewer: React.FC<KYCDocumentViewerProps> = ({
  kycData,
  onClose
}) => {
  const [selectedDocument, setSelectedDocument] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const documents = kycData?.certificate_data?.documents || {};

  const getDocumentUrl = async (documentPath: string) => {
    try {
      setLoading(true);
      
      // Extract filename from URL if it's a full URL
      const filename = documentPath.includes('/') 
        ? documentPath.split('/').pop() 
        : documentPath;

      const { data, error } = await supabase.storage
        .from('proof')
        .createSignedUrl(filename, 3600); // 1 hour expiry

      if (error) throw error;
      
      return data.signedUrl;
    } catch (error) {
      console.error('Error getting document URL:', error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const openDocument = async (documentPath: string) => {
    const signedUrl = await getDocumentUrl(documentPath);
    if (signedUrl) {
      window.open(signedUrl, '_blank');
    } else {
      alert('Unable to load document');
    }
  };

  const getDocumentType = (type: string) => {
    switch (type) {
      case 'id_document':
        return kycData.id_type === 'passport' ? 'Passport' : 
               kycData.id_type === 'drivers_license' ? 'Driver\'s License' : 'National ID';
      case 'proof_of_residence':
        return 'Proof of Residence';
      default:
        return type;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-white">
            📄 KYC Documents - {kycData.full_legal_name}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ✕
          </button>
        </div>

        {/* User Information */}
        <div className="bg-gray-700 rounded-lg p-4 mb-6">
          <h4 className="text-lg font-medium text-white mb-3">User Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div>
              <span className="text-gray-400">Full Name:</span>
              <span className="text-white ml-2">{kycData.full_legal_name}</span>
            </div>
            <div>
              <span className="text-gray-400">Email:</span>
              <span className="text-white ml-2">{kycData.email_address}</span>
            </div>
            <div>
              <span className="text-gray-400">Phone:</span>
              <span className="text-white ml-2">{kycData.phone_number}</span>
            </div>
            <div>
              <span className="text-gray-400">Country:</span>
              <span className="text-white ml-2">{kycData.country_name}</span>
            </div>
            <div>
              <span className="text-gray-400">ID Type:</span>
              <span className="text-white ml-2">
                {kycData.id_type === 'national_id' ? 'National ID' : 
                 kycData.id_type === 'passport' ? 'Passport' : 
                 kycData.id_type === 'drivers_license' ? 'Driver\'s License' : kycData.id_type}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Status:</span>
              <span className={`ml-2 font-semibold ${
                kycData.kyc_status === 'completed' ? 'text-green-400' : 'text-yellow-400'
              }`}>
                {kycData.kyc_status}
              </span>
            </div>
          </div>
        </div>

        {/* Documents Section */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-white">Uploaded Documents</h4>
          
          {Object.keys(documents).length === 0 ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-2">📄</div>
              <p className="text-gray-400">No documents uploaded</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(documents).map(([type, url]) => (
                <div key={type} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">
                        {type === 'id_document' ? '🆔' : '🏠'}
                      </span>
                      <div>
                        <h5 className="text-white font-medium">
                          {getDocumentType(type)}
                        </h5>
                        <p className="text-gray-400 text-xs">
                          Uploaded: {new Date(kycData.certificate_data?.uploaded_at || kycData.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <button
                      onClick={() => openDocument(url as string)}
                      disabled={loading}
                      className="w-full px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 text-sm"
                    >
                      {loading ? 'Loading...' : '👁️ View Document'}
                    </button>
                    
                    <button
                      onClick={async () => {
                        const signedUrl = await getDocumentUrl(url as string);
                        if (signedUrl) {
                          const link = document.createElement('a');
                          link.href = signedUrl;
                          link.download = `${kycData.full_legal_name}_${type}`;
                          link.click();
                        }
                      }}
                      disabled={loading}
                      className="w-full px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 text-sm"
                    >
                      📥 Download
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Address Information */}
        <div className="bg-gray-700 rounded-lg p-4 mt-6">
          <h4 className="text-lg font-medium text-white mb-3">Address Information</h4>
          <div className="text-sm">
            <p className="text-white">{kycData.street_address}</p>
            <p className="text-white">
              {kycData.city}, {kycData.postal_code}
            </p>
            <p className="text-white">{kycData.country_name}</p>
          </div>
        </div>

        {/* Compliance Information */}
        <div className="bg-gray-700 rounded-lg p-4 mt-4">
          <h4 className="text-lg font-medium text-white mb-3">Compliance</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <span className={kycData.data_consent_given ? 'text-green-400' : 'text-red-400'}>
                {kycData.data_consent_given ? '✅' : '❌'}
              </span>
              <span className="text-white">Data Consent Given</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={kycData.privacy_policy_accepted ? 'text-green-400' : 'text-red-400'}>
                {kycData.privacy_policy_accepted ? '✅' : '❌'}
              </span>
              <span className="text-white">Privacy Policy Accepted</span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 mt-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Close
          </button>
          
          {kycData.kyc_status === 'completed' && (
            <button
              onClick={() => {
                // This could trigger certificate generation or other admin actions
                alert('Certificate generation feature can be implemented here');
              }}
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              📜 Generate Certificate
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default KYCDocumentViewer;
