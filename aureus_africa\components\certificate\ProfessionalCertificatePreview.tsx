import React from 'react';
import { CertificateData } from '../../lib/certificateTemplate';

interface ProfessionalCertificatePreviewProps {
  data: CertificateData;
  onClose: () => void;
  onDownload?: () => void;
}

export const ProfessionalCertificatePreview: React.FC<ProfessionalCertificatePreviewProps> = ({
  data,
  onClose,
  onDownload
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[95vh] overflow-y-auto">
        {/* Header with controls */}
        <div className="flex justify-between items-center p-4 bg-gray-800 text-white rounded-t-lg">
          <h3 className="text-xl font-bold">Certificate Preview</h3>
          <div className="flex gap-2">
            {onDownload && (
              <button
                onClick={onDownload}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-sm font-medium"
              >
                📥 Download PDF
              </button>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm font-medium"
            >
              ✕ Close
            </button>
          </div>
        </div>

        {/* Certificate Content */}
        <div className="p-8">
          <div
            className="certificate-container mx-auto bg-white"
            style={{
              width: '297mm',
              height: '210mm',
              border: '8px solid #D4AF37',
              position: 'relative',
              fontFamily: 'Arial, sans-serif',
              backgroundImage: `
                linear-gradient(45deg, transparent 24%, rgba(212, 175, 55, 0.1) 25%, rgba(212, 175, 55, 0.1) 26%, transparent 27%, transparent 74%, rgba(212, 175, 55, 0.1) 75%, rgba(212, 175, 55, 0.1) 76%, transparent 77%, transparent),
                linear-gradient(-45deg, transparent 24%, rgba(212, 175, 55, 0.1) 25%, rgba(212, 175, 55, 0.1) 26%, transparent 27%, transparent 74%, rgba(212, 175, 55, 0.1) 75%, rgba(212, 175, 55, 0.1) 76%, transparent 77%, transparent)
              `,
              backgroundSize: '20px 20px'
            }}
          >
            {/* Inner border */}
            <div 
              className="absolute inset-4 border-2"
              style={{ borderColor: '#B8860B' }}
            />

            {/* Header Section */}
            <div className="flex justify-between items-start p-6">
              <div className="text-left">
                <div className="text-xs font-bold mb-1">CERTIFICATE NO</div>
                <div className="text-sm font-bold">{data.certificateNumber}</div>
              </div>
              <div className="text-right">
                <div className="text-xs font-bold mb-1">NO OF SHARES</div>
                <div className="text-sm font-bold">{data.sharesQuantity.toLocaleString()}</div>
              </div>
            </div>

            {/* Title Section */}
            <div className="text-center mt-8 mb-6">
              <h1 className="text-4xl font-bold mb-4" style={{ fontFamily: 'serif' }}>
                SHARE CERTIFICATE
              </h1>
              <h2 className="text-xl font-bold mb-2">AUREUS ALLIANCE HOLDINGS</h2>
              <p className="text-sm mb-1">(Incorporated in the Republic of South Africa)</p>
              <p className="text-sm font-bold">REG NO. 2025 / 368711 / 07</p>
            </div>

            {/* Address Section */}
            <div className="flex justify-between px-12 mb-8">
              <div>
                <div className="text-xs font-bold mb-2">Registered Office:</div>
                <div className="text-xs leading-relaxed">
                  1848 Mees Avenue<br/>
                  Randpark Ridge<br/>
                  Randburg<br/>
                  Gauteng, 2194<br/>
                  South Africa
                </div>
              </div>
              <div>
                <div className="text-xs font-bold mb-2">Postal Address:</div>
                <div className="text-xs leading-relaxed">
                  1848 Mees Avenue<br/>
                  Randpark Ridge<br/>
                  Randburg<br/>
                  Gauteng, 2194<br/>
                  South Africa
                </div>
              </div>
            </div>

            {/* Main Certificate Text */}
            <div className="text-center px-12 mb-8">
              <p className="text-sm leading-relaxed">
                This is to certify that the undermentioned is the registered proprietor of fully paid-up shares as shown below in the capital of the above Company, subject to the memorandum of incorporation
              </p>
            </div>

            {/* Certificate Table */}
            <div className="mx-12 mb-8">
              {/* Table Header */}
              <div className="grid grid-cols-6 bg-yellow-600 text-white text-xs font-bold text-center py-2">
                <div>NAME AND ADDRESS</div>
                <div>CLASS OF SHARE</div>
                <div>REF NO.</div>
                <div>DATE</div>
                <div>CERT NO</div>
                <div>NO OF SHARES</div>
              </div>
              
              {/* Table Content */}
              <div className="grid grid-cols-6 border border-gray-300 text-xs py-4 px-2">
                <div className="pr-2">
                  <div className="font-bold mb-1">{data.userFullName}</div>
                  <div className="text-xs">ID: {data.userIdNumber || 'N/A'}</div>
                  <div className="text-xs mt-2">{data.userAddress}</div>
                  <div className="text-xs mt-2">SUN ID: {data.sunNumber || 'N/A'}</div>
                </div>
                <div className="text-center">NO PAR VALUE<br/>SHARES</div>
                <div className="text-center">1-700</div>
                <div className="text-center">{formatDate(data.issueDate)}</div>
                <div className="text-center">{data.certificateNumber}</div>
                <div className="text-center font-bold">{data.sharesQuantity.toLocaleString()}</div>
              </div>
            </div>

            {/* Company Logo/Seal Section */}
            <div className="text-center mb-8">
              <div 
                className="inline-block text-2xl font-bold"
                style={{ 
                  color: '#D4AF37',
                  fontFamily: 'serif',
                  textShadow: '1px 1px 2px rgba(0,0,0,0.1)'
                }}
              >
                AUREUS<br/>
                ALLIANCE HOLDINGS
              </div>
            </div>

            {/* Signatures Section */}
            <div className="flex justify-between items-end px-12 pb-8">
              <div className="text-center">
                <div className="border-b border-gray-400 w-32 mb-2 mx-auto"></div>
                <div className="text-xs font-bold">Mr DC James</div>
                <div className="text-xs">C.E.O AUREUS ALLIANCE HOLDINGS</div>
              </div>
              
              <div className="text-center">
                <div className="border-b border-gray-400 w-32 mb-2 mx-auto"></div>
                <div className="text-xs font-bold">Mr JP Rademeyer</div>
                <div className="text-xs">C.E.O SMART UNITED NETWORK</div>
              </div>
            </div>

            {/* QR Code placeholder */}
            <div className="absolute bottom-8 right-8">
              <div className="w-16 h-16 border-2 border-gray-300 flex items-center justify-center text-xs text-gray-500">
                QR Code
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute top-4 left-4 w-8 h-8 opacity-20">
              <svg viewBox="0 0 24 24" fill="currentColor" className="text-yellow-600">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div className="absolute top-4 right-4 w-8 h-8 opacity-20">
              <svg viewBox="0 0 24 24" fill="currentColor" className="text-yellow-600">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div className="absolute bottom-4 left-4 w-8 h-8 opacity-20">
              <svg viewBox="0 0 24 24" fill="currentColor" className="text-yellow-600">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div className="absolute bottom-4 right-20 w-8 h-8 opacity-20">
              <svg viewBox="0 0 24 24" fill="currentColor" className="text-yellow-600">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfessionalCertificatePreview;
