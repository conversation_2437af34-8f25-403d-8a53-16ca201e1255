-- Marketing Materials Table Creation
-- Execute this SQL in your Supabase SQL editor

-- Create marketing_materials table
CREATE TABLE IF NOT EXISTS public.marketing_materials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_url TEXT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    language VARCHAR(10) NOT NULL DEFAULT 'en',
    category VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_marketing_materials_language ON public.marketing_materials(language);
CREATE INDEX IF NOT EXISTS idx_marketing_materials_category ON public.marketing_materials(category);
CREATE INDEX IF NOT EXISTS idx_marketing_materials_is_active ON public.marketing_materials(is_active);
CREATE INDEX IF NOT EXISTS idx_marketing_materials_created_at ON public.marketing_materials(created_at DESC);

-- Add comments to the table and columns
COMMENT ON TABLE public.marketing_materials IS 'Storage for marketing materials uploaded by admins';
COMMENT ON COLUMN public.marketing_materials.id IS 'Unique identifier for the marketing material';
COMMENT ON COLUMN public.marketing_materials.title IS 'Title of the marketing material';
COMMENT ON COLUMN public.marketing_materials.description IS 'Description of the marketing material';
COMMENT ON COLUMN public.marketing_materials.file_url IS 'URL to the uploaded file in Supabase Storage';
COMMENT ON COLUMN public.marketing_materials.file_type IS 'MIME type of the uploaded file';
COMMENT ON COLUMN public.marketing_materials.file_size IS 'Size of the file in bytes';
COMMENT ON COLUMN public.marketing_materials.language IS 'Language code (en, af, zu, xh, fr, pt, es)';
COMMENT ON COLUMN public.marketing_materials.category IS 'Category of the material (Presentations, Brochures, etc.)';
COMMENT ON COLUMN public.marketing_materials.is_active IS 'Whether the material is active and visible to users';
COMMENT ON COLUMN public.marketing_materials.created_at IS 'Timestamp when the material was created';
COMMENT ON COLUMN public.marketing_materials.updated_at IS 'Timestamp when the material was last updated';

-- Enable Row Level Security (RLS)
ALTER TABLE public.marketing_materials ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Policy for admins to have full access
CREATE POLICY "Admins can manage marketing materials" ON public.marketing_materials
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.admin_users 
            WHERE email = auth.jwt() ->> 'email'
        )
    );

-- Policy for authenticated users to read active materials
CREATE POLICY "Users can view active marketing materials" ON public.marketing_materials
    FOR SELECT USING (
        is_active = true AND 
        auth.role() = 'authenticated'
    );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_marketing_materials_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_marketing_materials_updated_at
    BEFORE UPDATE ON public.marketing_materials
    FOR EACH ROW
    EXECUTE FUNCTION public.update_marketing_materials_updated_at();

-- Grant necessary permissions
GRANT SELECT ON public.marketing_materials TO authenticated;
GRANT ALL ON public.marketing_materials TO service_role;

-- Create storage bucket for marketing materials (if not exists)
-- Note: This needs to be done via the Supabase dashboard or storage API
-- INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
-- VALUES (
--     'marketing-materials',
--     'marketing-materials',
--     true,
--     52428800, -- 50MB
--     ARRAY[
--         'application/pdf',
--         'application/vnd.ms-powerpoint',
--         'application/vnd.openxmlformats-officedocument.presentationml.presentation',
--         'application/msword',
--         'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
--         'image/jpeg',
--         'image/png',
--         'image/webp',
--         'video/mp4',
--         'video/webm'
--     ]
-- ) ON CONFLICT (id) DO NOTHING;

-- Create storage policies for marketing-materials bucket
-- Policy for admins to upload files
-- CREATE POLICY "Admins can upload marketing materials" ON storage.objects
--     FOR INSERT WITH CHECK (
--         bucket_id = 'marketing-materials' AND
--         EXISTS (
--             SELECT 1 FROM public.admin_users 
--             WHERE email = auth.jwt() ->> 'email'
--         )
--     );

-- Policy for admins to update files
-- CREATE POLICY "Admins can update marketing materials" ON storage.objects
--     FOR UPDATE USING (
--         bucket_id = 'marketing-materials' AND
--         EXISTS (
--             SELECT 1 FROM public.admin_users 
--             WHERE email = auth.jwt() ->> 'email'
--         )
--     );

-- Policy for admins to delete files
-- CREATE POLICY "Admins can delete marketing materials" ON storage.objects
--     FOR DELETE USING (
--         bucket_id = 'marketing-materials' AND
--         EXISTS (
--             SELECT 1 FROM public.admin_users 
--             WHERE email = auth.jwt() ->> 'email'
--         )
--     );

-- Policy for authenticated users to view files
-- CREATE POLICY "Users can view marketing materials" ON storage.objects
--     FOR SELECT USING (
--         bucket_id = 'marketing-materials' AND
--         auth.role() = 'authenticated'
--     );

-- Insert some sample data (optional)
INSERT INTO public.marketing_materials (
    title,
    description,
    file_url,
    file_type,
    file_size,
    language,
    category,
    is_active
) VALUES 
(
    'Aureus Alliance Company Overview',
    'Comprehensive presentation about Aureus Alliance Holdings and our gold mining operations in South Africa',
    'https://example.com/sample-presentation.pdf',
    'application/pdf',
    2048000,
    'en',
    'Presentations',
    true
),
(
    'Investment Opportunity Brochure',
    'Detailed brochure outlining the investment opportunity and expected returns',
    'https://example.com/sample-brochure.pdf',
    'application/pdf',
    1536000,
    'en',
    'Brochures',
    true
),
(
    'Aureus Alliance Oorsig (Afrikaans)',
    'Maatskappy oorsig in Afrikaans vir plaaslike beleggers',
    'https://example.com/sample-afrikaans.pdf',
    'application/pdf',
    2048000,
    'af',
    'Presentations',
    true
)
ON CONFLICT DO NOTHING;

-- Create view for active materials with language names
CREATE OR REPLACE VIEW public.active_marketing_materials AS
SELECT 
    m.*,
    CASE m.language
        WHEN 'en' THEN 'English'
        WHEN 'af' THEN 'Afrikaans'
        WHEN 'zu' THEN 'Zulu'
        WHEN 'xh' THEN 'Xhosa'
        WHEN 'fr' THEN 'French'
        WHEN 'pt' THEN 'Portuguese'
        WHEN 'es' THEN 'Spanish'
        ELSE 'Unknown'
    END as language_name
FROM public.marketing_materials m
WHERE m.is_active = true
ORDER BY m.category, m.language, m.created_at DESC;

-- Grant access to the view
GRANT SELECT ON public.active_marketing_materials TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Marketing materials table and related objects created successfully!';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Create the marketing-materials storage bucket in Supabase Dashboard';
    RAISE NOTICE '2. Configure storage policies for the bucket';
    RAISE NOTICE '3. Update sample data URLs with actual file URLs';
END $$;
