-- ========================================
-- ADVANCED MARKETING TOOLKIT DATABASE SCHEMA
-- ========================================
-- This schema supports the advanced marketing toolkit features
-- including content generation, campaign tracking, and analytics

-- 1. Marketing Content Generation Tracking
CREATE TABLE IF NOT EXISTS public.marketing_content_generated (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  platform VARCHAR(50) NOT NULL, -- facebook, instagram, twitter, etc.
  content_type VARCHAR(50) NOT NULL, -- post, story, reel, tweet, etc.
  template_id VARCHAR(100) NOT NULL,
  content_text TEXT NOT NULL,
  hashtags TEXT,
  referral_link TEXT NOT NULL,
  customizations JSONB DEFAULT '{}',
  character_count INTEGER DEFAULT 0,
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_marketing_content_user_id (user_id),
  INDEX idx_marketing_content_platform (platform),
  INDEX idx_marketing_content_generated_at (generated_at)
);

-- 2. Generated Marketing Materials
CREATE TABLE IF NOT EXISTS public.generated_marketing_materials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  material_type VARCHAR(50) NOT NULL, -- banner, video, presentation, infographic, qr_code
  template_id VARCHAR(100) NOT NULL,
  material_name VARCHAR(255) NOT NULL,
  file_url TEXT NOT NULL,
  download_url TEXT NOT NULL,
  customizations JSONB DEFAULT '{}',
  file_size INTEGER DEFAULT 0,
  file_format VARCHAR(20),
  dimensions VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  downloaded_count INTEGER DEFAULT 0,
  last_downloaded_at TIMESTAMP WITH TIME ZONE,
  
  -- Indexes
  INDEX idx_generated_materials_user_id (user_id),
  INDEX idx_generated_materials_type (material_type),
  INDEX idx_generated_materials_created_at (created_at)
);

-- 3. Enhanced Referral Analytics (extends existing referral_analytics)
-- Add columns to existing referral_analytics table if they don't exist
DO $$ 
BEGIN
  -- Add platform-specific tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'referral_analytics' AND column_name = 'platform_data') THEN
    ALTER TABLE public.referral_analytics ADD COLUMN platform_data JSONB DEFAULT '{}';
  END IF;
  
  -- Add content performance tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'referral_analytics' AND column_name = 'content_performance') THEN
    ALTER TABLE public.referral_analytics ADD COLUMN content_performance JSONB DEFAULT '{}';
  END IF;
  
  -- Add geographic data
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'referral_analytics' AND column_name = 'geographic_data') THEN
    ALTER TABLE public.referral_analytics ADD COLUMN geographic_data JSONB DEFAULT '{}';
  END IF;
  
  -- Add device/browser tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'referral_analytics' AND column_name = 'device_data') THEN
    ALTER TABLE public.referral_analytics ADD COLUMN device_data JSONB DEFAULT '{}';
  END IF;
END $$;

-- 4. Campaign Performance Tracking
CREATE TABLE IF NOT EXISTS public.campaign_performance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  campaign_id VARCHAR(100) NOT NULL,
  campaign_name VARCHAR(255) NOT NULL,
  platform VARCHAR(50) NOT NULL,
  content_type VARCHAR(50),
  
  -- Performance Metrics
  impressions INTEGER DEFAULT 0,
  clicks INTEGER DEFAULT 0,
  registrations INTEGER DEFAULT 0,
  conversions INTEGER DEFAULT 0,
  revenue DECIMAL(10,2) DEFAULT 0.00,
  
  -- Calculated Metrics
  click_through_rate DECIMAL(5,2) DEFAULT 0.00,
  conversion_rate DECIMAL(5,2) DEFAULT 0.00,
  cost_per_click DECIMAL(8,2) DEFAULT 0.00,
  return_on_ad_spend DECIMAL(8,2) DEFAULT 0.00,
  
  -- Time Tracking
  campaign_start_date DATE,
  campaign_end_date DATE,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Additional Data
  metadata JSONB DEFAULT '{}',
  
  -- Indexes
  INDEX idx_campaign_performance_user_id (user_id),
  INDEX idx_campaign_performance_campaign_id (campaign_id),
  INDEX idx_campaign_performance_platform (platform),
  INDEX idx_campaign_performance_dates (campaign_start_date, campaign_end_date)
);

-- 5. Social Media Platform Configurations
CREATE TABLE IF NOT EXISTS public.social_media_platforms (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  icon VARCHAR(10),
  color VARCHAR(7), -- Hex color code
  character_limits JSONB DEFAULT '{}', -- Different limits for different content types
  supported_content_types TEXT[] DEFAULT '{}',
  api_endpoints JSONB DEFAULT '{}',
  sharing_templates JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default platform configurations
INSERT INTO public.social_media_platforms (id, name, icon, color, character_limits, supported_content_types) VALUES
('facebook', 'Facebook', '📘', '#1877f2', '{"post": 2200, "story": 2200}', '{"post", "story", "reel"}'),
('instagram', 'Instagram', '📷', '#e4405f', '{"post": 2200, "story": 2200, "reel": 2200}', '{"post", "story", "reel", "igtv"}'),
('twitter', 'Twitter/X', '🐦', '#1da1f2', '{"tweet": 280, "thread": 280}', '{"tweet", "thread"}'),
('linkedin', 'LinkedIn', '💼', '#0077b5', '{"post": 3000, "article": 125000}', '{"post", "article"}'),
('tiktok', 'TikTok', '🎵', '#000000', '{"video": 2200}', '{"video"}'),
('whatsapp', 'WhatsApp', '💬', '#25d366', '{"message": 4096, "status": 700}', '{"message", "status"}'),
('telegram', 'Telegram', '✈️', '#0088cc', '{"message": 4096, "channel_post": 4096}', '{"message", "channel_post"}'),
('youtube', 'YouTube', '📺', '#ff0000', '{"short": 100, "video_description": 5000}', '{"short", "video_description"}')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  icon = EXCLUDED.icon,
  color = EXCLUDED.color,
  character_limits = EXCLUDED.character_limits,
  supported_content_types = EXCLUDED.supported_content_types,
  updated_at = NOW();

-- 6. Content Templates
CREATE TABLE IF NOT EXISTS public.content_templates (
  id VARCHAR(100) PRIMARY KEY,
  platform VARCHAR(50) NOT NULL REFERENCES public.social_media_platforms(id),
  content_type VARCHAR(50) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  template_text TEXT NOT NULL,
  default_hashtags TEXT[] DEFAULT '{}',
  customizable_fields TEXT[] DEFAULT '{}',
  category VARCHAR(100),
  is_premium BOOLEAN DEFAULT FALSE,
  usage_count INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0.00,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_content_templates_platform (platform),
  INDEX idx_content_templates_category (category),
  INDEX idx_content_templates_rating (rating DESC)
);

-- 7. User Marketing Preferences
CREATE TABLE IF NOT EXISTS public.user_marketing_preferences (
  user_id INTEGER PRIMARY KEY REFERENCES public.users(id) ON DELETE CASCADE,
  preferred_platforms TEXT[] DEFAULT '{}',
  content_style VARCHAR(50) DEFAULT 'professional', -- professional, casual, energetic, etc.
  auto_generate_hashtags BOOLEAN DEFAULT TRUE,
  include_referral_tracking BOOLEAN DEFAULT TRUE,
  default_campaign_prefix VARCHAR(50),
  notification_preferences JSONB DEFAULT '{}',
  ai_content_settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Marketing Training Progress
CREATE TABLE IF NOT EXISTS public.marketing_training_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  module_id VARCHAR(100) NOT NULL,
  module_name VARCHAR(255) NOT NULL,
  completion_percentage INTEGER DEFAULT 0,
  completed_at TIMESTAMP WITH TIME ZONE,
  quiz_score INTEGER,
  time_spent_minutes INTEGER DEFAULT 0,
  last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_training_progress_user_id (user_id),
  INDEX idx_training_progress_module_id (module_id),
  UNIQUE(user_id, module_id)
);

-- 9. A/B Testing for Marketing Content
CREATE TABLE IF NOT EXISTS public.marketing_ab_tests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  test_name VARCHAR(255) NOT NULL,
  platform VARCHAR(50) NOT NULL,
  content_type VARCHAR(50) NOT NULL,
  
  -- Test Variants
  variant_a_content TEXT NOT NULL,
  variant_b_content TEXT NOT NULL,
  variant_a_performance JSONB DEFAULT '{}',
  variant_b_performance JSONB DEFAULT '{}',
  
  -- Test Configuration
  test_duration_days INTEGER DEFAULT 7,
  traffic_split INTEGER DEFAULT 50, -- Percentage for variant A
  success_metric VARCHAR(50) DEFAULT 'conversions', -- clicks, conversions, revenue
  
  -- Test Status
  status VARCHAR(20) DEFAULT 'draft', -- draft, running, completed, paused
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  winner VARCHAR(10), -- 'A', 'B', or 'tie'
  confidence_level DECIMAL(5,2),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_ab_tests_user_id (user_id),
  INDEX idx_ab_tests_status (status),
  INDEX idx_ab_tests_platform (platform)
);

-- 10. Marketing Automation Rules
CREATE TABLE IF NOT EXISTS public.marketing_automation_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  rule_name VARCHAR(255) NOT NULL,
  trigger_type VARCHAR(50) NOT NULL, -- time_based, performance_based, event_based
  trigger_conditions JSONB NOT NULL,
  actions JSONB NOT NULL, -- What to do when triggered
  is_active BOOLEAN DEFAULT TRUE,
  last_triggered TIMESTAMP WITH TIME ZONE,
  trigger_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_automation_rules_user_id (user_id),
  INDEX idx_automation_rules_active (is_active),
  INDEX idx_automation_rules_trigger_type (trigger_type)
);

-- ========================================
-- FUNCTIONS AND PROCEDURES
-- ========================================

-- Function to calculate campaign ROI
CREATE OR REPLACE FUNCTION calculate_campaign_roi(
  p_campaign_id VARCHAR(100),
  p_user_id INTEGER
) RETURNS DECIMAL(8,2) AS $$
DECLARE
  total_revenue DECIMAL(10,2);
  total_cost DECIMAL(10,2);
  roi DECIMAL(8,2);
BEGIN
  -- Get total revenue for the campaign
  SELECT COALESCE(SUM(revenue), 0) INTO total_revenue
  FROM campaign_performance
  WHERE campaign_id = p_campaign_id AND user_id = p_user_id;
  
  -- Calculate total cost (simplified - clicks * average cost per click)
  SELECT COALESCE(SUM(clicks * cost_per_click), 0) INTO total_cost
  FROM campaign_performance
  WHERE campaign_id = p_campaign_id AND user_id = p_user_id;
  
  -- Calculate ROI
  IF total_cost > 0 THEN
    roi := ((total_revenue - total_cost) / total_cost) * 100;
  ELSE
    roi := 0;
  END IF;
  
  RETURN roi;
END;
$$ LANGUAGE plpgsql;

-- Function to get top performing content templates
CREATE OR REPLACE FUNCTION get_top_content_templates(
  p_platform VARCHAR(50) DEFAULT NULL,
  p_limit INTEGER DEFAULT 10
) RETURNS TABLE (
  template_id VARCHAR(100),
  template_name VARCHAR(255),
  platform VARCHAR(50),
  usage_count INTEGER,
  rating DECIMAL(3,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ct.id,
    ct.name,
    ct.platform,
    ct.usage_count,
    ct.rating
  FROM content_templates ct
  WHERE (p_platform IS NULL OR ct.platform = p_platform)
  ORDER BY ct.rating DESC, ct.usage_count DESC
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to update campaign performance metrics
CREATE OR REPLACE FUNCTION update_campaign_metrics(
  p_campaign_id VARCHAR(100),
  p_user_id INTEGER
) RETURNS VOID AS $$
BEGIN
  UPDATE campaign_performance
  SET 
    click_through_rate = CASE 
      WHEN impressions > 0 THEN (clicks::DECIMAL / impressions) * 100
      ELSE 0
    END,
    conversion_rate = CASE 
      WHEN clicks > 0 THEN (conversions::DECIMAL / clicks) * 100
      ELSE 0
    END,
    return_on_ad_spend = CASE 
      WHEN (clicks * cost_per_click) > 0 THEN revenue / (clicks * cost_per_click)
      ELSE 0
    END,
    last_updated = NOW()
  WHERE campaign_id = p_campaign_id AND user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- PERMISSIONS AND SECURITY
-- ========================================

-- Enable Row Level Security
ALTER TABLE public.marketing_content_generated ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.generated_marketing_materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_marketing_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_training_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_ab_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_automation_rules ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can manage their own marketing content" ON public.marketing_content_generated
  FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage their own marketing materials" ON public.generated_marketing_materials
  FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view their own campaign performance" ON public.campaign_performance
  FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage their own marketing preferences" ON public.user_marketing_preferences
  FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view their own training progress" ON public.marketing_training_progress
  FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage their own A/B tests" ON public.marketing_ab_tests
  FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage their own automation rules" ON public.marketing_automation_rules
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.marketing_content_generated TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.generated_marketing_materials TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.campaign_performance TO authenticated;
GRANT SELECT ON public.social_media_platforms TO authenticated;
GRANT SELECT ON public.content_templates TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_marketing_preferences TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.marketing_training_progress TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.marketing_ab_tests TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.marketing_automation_rules TO authenticated;

-- Grant function permissions
GRANT EXECUTE ON FUNCTION calculate_campaign_roi(VARCHAR(100), INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_top_content_templates(VARCHAR(50), INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION update_campaign_metrics(VARCHAR(100), INTEGER) TO authenticated;

-- ========================================
-- COMMENTS AND DOCUMENTATION
-- ========================================

COMMENT ON TABLE public.marketing_content_generated IS 'Tracks AI-generated social media content';
COMMENT ON TABLE public.generated_marketing_materials IS 'Stores generated marketing materials like banners, videos, etc.';
COMMENT ON TABLE public.campaign_performance IS 'Detailed campaign performance tracking and analytics';
COMMENT ON TABLE public.social_media_platforms IS 'Configuration for supported social media platforms';
COMMENT ON TABLE public.content_templates IS 'Reusable content templates for different platforms';
COMMENT ON TABLE public.user_marketing_preferences IS 'User preferences for marketing tools and content generation';
COMMENT ON TABLE public.marketing_training_progress IS 'Tracks user progress through marketing training modules';
COMMENT ON TABLE public.marketing_ab_tests IS 'A/B testing framework for marketing content optimization';
COMMENT ON TABLE public.marketing_automation_rules IS 'Automated marketing rules and triggers';

SELECT '🎉 ADVANCED MARKETING TOOLKIT SCHEMA CREATED SUCCESSFULLY! 🎉' as status;
