import React from 'react';

interface TelegramRedirectMessageProps {
  title?: string;
  showChannelLink?: boolean;
  className?: string;
  isRegistration?: boolean;
  buttonText?: string;
}

export const TelegramRedirectMessage: React.FC<TelegramRedirectMessageProps> = ({
  title = "Account Operations via Telegram Bot",
  showChannelLink = true,
  className = "",
  isRegistration = false,
  buttonText
}) => {
  return (
    <div className={`bg-blue-900/20 border border-blue-500/30 rounded-xl p-8 text-center ${className}`}>
      <div className="mb-6">
        <div className="text-6xl mb-4">🤖</div>
        <h3 className="text-2xl font-bold text-blue-300 mb-4">
          {title}
        </h3>
      </div>

      <div className="space-y-4 text-gray-300">
        <p className="text-lg">
          All registration and login processes are currently handled exclusively through our{' '}
          <span className="text-blue-300 font-semibold">Telegram Bot</span>.
        </p>

        {/* Registration-specific sponsor guidance */}
        {isRegistration && (
          <div className="bg-amber-900/20 border border-amber-500/30 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-3">
              <div className="text-amber-400 text-xl">⚠️</div>
              <div>
                <h4 className="text-amber-300 font-semibold mb-2">Important for New Registrations</h4>
                <p className="text-amber-100 text-sm">
                  <strong>If you are registering, please have your sponsor's username ready</strong> as this will be required during the registration process.
                  If you don't have a sponsor, one will be assigned to you automatically.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-600/30">
          <p className="text-sm text-gray-400 mb-3">
            To get started with your account:
          </p>
          <ol className="text-left text-sm space-y-2 max-w-md mx-auto">
            <li className="flex items-start">
              <span className="text-blue-400 font-bold mr-2">1.</span>
              <span>
                Click the "{buttonText || (isRegistration ? "Continue to Registration" : "Continue to Login")}" button below to access our Telegram Bot
              </span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-400 font-bold mr-2">2.</span>
              <span>
                Follow the bot's instructions to {isRegistration ? "create your account and register" : "log into your existing account"}
              </span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-400 font-bold mr-2">3.</span>
              <span>
                Complete your {isRegistration ? "registration and share purchase" : "login and access your dashboard"}
              </span>
            </li>
          </ol>
        </div>

        <div className="pt-4">
          <a
            href="https://t.me/AureusAllianceBot"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-200 shadow-lg hover:shadow-blue-500/25"
          >
            <span className="mr-2">🚀</span>
            {buttonText || (isRegistration ? "Continue to Registration" : "Access Telegram Bot")}
          </a>
        </div>

        {showChannelLink && (
          <div className="pt-6 border-t border-gray-600/30">
            <p className="text-sm text-gray-400 mb-3">
              Stay updated with the latest news and announcements:
            </p>
            <a
              href="https://t.me/AureusAllianceHoldings"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-yellow-400 hover:text-yellow-300 font-medium transition-colors duration-200"
            >
              <span className="mr-2">📢</span>
              Join Official Telegram Channel
            </a>
          </div>
        )}

        <div className="pt-4">
          <p className="text-xs text-gray-500">
            Website-based registration and login will be available in the future.
          </p>
        </div>
      </div>
    </div>
  );
};
