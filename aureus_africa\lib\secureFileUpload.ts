/**
 * SECURE FILE UPLOAD SYSTEM
 * 
 * This module provides comprehensive file upload security including
 * type validation, size limits, malware scanning, and sandboxing.
 */

import { supabase } from './supabase';

interface FileUploadConfig {
  maxFileSize: number; // in bytes
  allowedMimeTypes: string[];
  allowedExtensions: string[];
  scanForMalware: boolean;
  quarantineUnsafe: boolean;
  generateThumbnails: boolean;
  logUploads: boolean;
}

interface FileUploadResult {
  success: boolean;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  securityScore?: number;
  warnings?: string[];
  error?: string;
}

interface FileSecurityScan {
  safe: boolean;
  score: number; // 0-100, higher is safer
  threats: string[];
  warnings: string[];
  metadata: any;
}

class SecureFileUploadManager {
  private readonly defaultConfig: FileUploadConfig = {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedMimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'text/csv'
    ],
    allowedExtensions: [
      '.jpg', '.jpeg', '.png', '.gif', '.webp',
      '.pdf', '.txt', '.csv'
    ],
    scanForMalware: true,
    quarantineUnsafe: true,
    generateThumbnails: true,
    logUploads: true
  };

  private readonly dangerousExtensions = [
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
    '.app', '.deb', '.pkg', '.dmg', '.rpm', '.msi', '.run', '.bin',
    '.sh', '.bash', '.zsh', '.fish', '.ps1', '.psm1', '.psd1',
    '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl', '.cgi'
  ];

  private readonly suspiciousMimeTypes = [
    'application/x-executable',
    'application/x-msdownload',
    'application/x-msdos-program',
    'application/x-winexe',
    'application/x-javascript',
    'text/javascript',
    'application/javascript'
  ];

  /**
   * Validate and upload file securely
   */
  async uploadFile(
    file: File,
    userId: number,
    uploadPath: string,
    config: Partial<FileUploadConfig> = {}
  ): Promise<FileUploadResult> {
    try {
      console.log(`🔐 Secure file upload initiated for user ${userId}`);
      
      const finalConfig = { ...this.defaultConfig, ...config };
      const warnings: string[] = [];

      // 1. Basic file validation
      const basicValidation = this.validateFileBasics(file, finalConfig);
      if (!basicValidation.valid) {
        return { success: false, error: basicValidation.error };
      }

      // 2. Security scan
      const securityScan = await this.scanFileForThreats(file);
      if (!securityScan.safe && finalConfig.quarantineUnsafe) {
        await this.quarantineFile(file, userId, securityScan);
        return {
          success: false,
          error: 'File failed security scan',
          securityScore: securityScan.score,
          warnings: securityScan.threats
        };
      }

      warnings.push(...securityScan.warnings);

      // 3. Generate secure filename
      const secureFileName = await this.generateSecureFileName(file.name, uploadPath);

      // 4. Upload to secure storage
      const uploadResult = await this.uploadToSecureStorage(
        file,
        secureFileName,
        userId,
        finalConfig
      );

      if (!uploadResult.success) {
        return { success: false, error: uploadResult.error };
      }

      // 5. Log upload activity
      if (finalConfig.logUploads) {
        await this.logFileUpload(userId, file, secureFileName, securityScan);
      }

      console.log(`✅ File uploaded securely: ${secureFileName}`);
      return {
        success: true,
        fileUrl: uploadResult.fileUrl,
        fileName: secureFileName,
        fileSize: file.size,
        mimeType: file.type,
        securityScore: securityScan.score,
        warnings: warnings.length > 0 ? warnings : undefined
      };

    } catch (error) {
      console.error('❌ Secure file upload error:', error);
      return { success: false, error: 'File upload failed' };
    }
  }

  /**
   * Validate basic file properties
   */
  private validateFileBasics(
    file: File,
    config: FileUploadConfig
  ): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > config.maxFileSize) {
      return {
        valid: false,
        error: `File too large. Maximum size: ${(config.maxFileSize / 1024 / 1024).toFixed(1)}MB`
      };
    }

    if (file.size === 0) {
      return { valid: false, error: 'Empty file not allowed' };
    }

    // Check MIME type
    if (!config.allowedMimeTypes.includes(file.type)) {
      return { valid: false, error: 'File type not allowed' };
    }

    // Check file extension
    const extension = this.getFileExtension(file.name).toLowerCase();
    if (!config.allowedExtensions.includes(extension)) {
      return { valid: false, error: 'File extension not allowed' };
    }

    // Check for dangerous extensions
    if (this.dangerousExtensions.includes(extension)) {
      return { valid: false, error: 'Potentially dangerous file type' };
    }

    // Check for suspicious MIME types
    if (this.suspiciousMimeTypes.includes(file.type)) {
      return { valid: false, error: 'Suspicious file type detected' };
    }

    // Check filename for suspicious patterns
    if (this.hasSuspiciousFilename(file.name)) {
      return { valid: false, error: 'Suspicious filename detected' };
    }

    return { valid: true };
  }

  /**
   * Scan file for security threats
   */
  private async scanFileForThreats(file: File): Promise<FileSecurityScan> {
    try {
      console.log('🔍 Scanning file for security threats...');

      let score = 100;
      const threats: string[] = [];
      const warnings: string[] = [];
      const metadata: any = {};

      // 1. Check file header/magic bytes
      const headerCheck = await this.validateFileHeader(file);
      if (!headerCheck.valid) {
        score -= 30;
        threats.push('Invalid file header - possible file type spoofing');
      }

      // 2. Check for embedded scripts
      const scriptCheck = await this.scanForEmbeddedScripts(file);
      if (scriptCheck.hasScripts) {
        score -= 40;
        threats.push('Embedded scripts detected');
      }

      // 3. Check file size anomalies
      if (file.size > 50 * 1024 * 1024) { // 50MB
        score -= 10;
        warnings.push('Large file size - potential zip bomb');
      }

      // 4. Check filename for suspicious patterns
      if (this.hasSuspiciousFilename(file.name)) {
        score -= 20;
        warnings.push('Suspicious filename pattern');
      }

      // 5. Basic malware signature check (simplified)
      const signatureCheck = await this.basicMalwareSignatureCheck(file);
      if (signatureCheck.suspicious) {
        score -= 50;
        threats.push('Suspicious file signature detected');
      }

      metadata.scanTime = new Date().toISOString();
      metadata.fileSize = file.size;
      metadata.mimeType = file.type;

      const safe = score >= 70 && threats.length === 0;

      console.log(`🔍 Security scan complete: Score ${score}/100, Safe: ${safe}`);
      return { safe, score, threats, warnings, metadata };

    } catch (error) {
      console.error('❌ File security scan error:', error);
      return {
        safe: false,
        score: 0,
        threats: ['Security scan failed'],
        warnings: [],
        metadata: { error: error.message }
      };
    }
  }

  /**
   * Validate file header matches declared MIME type
   */
  private async validateFileHeader(file: File): Promise<{ valid: boolean; detectedType?: string }> {
    try {
      const buffer = await file.slice(0, 16).arrayBuffer();
      const bytes = new Uint8Array(buffer);
      
      // Common file signatures
      const signatures = {
        'image/jpeg': [[0xFF, 0xD8, 0xFF]],
        'image/png': [[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]],
        'image/gif': [[0x47, 0x49, 0x46, 0x38, 0x37, 0x61], [0x47, 0x49, 0x46, 0x38, 0x39, 0x61]],
        'application/pdf': [[0x25, 0x50, 0x44, 0x46]],
        'image/webp': [[0x52, 0x49, 0x46, 0x46]]
      };

      const declaredType = file.type;
      const expectedSignatures = signatures[declaredType as keyof typeof signatures];

      if (!expectedSignatures) {
        // No signature check available for this type
        return { valid: true };
      }

      const matches = expectedSignatures.some(signature =>
        signature.every((byte, index) => bytes[index] === byte)
      );

      return { valid: matches, detectedType: declaredType };

    } catch (error) {
      console.error('❌ File header validation error:', error);
      return { valid: false };
    }
  }

  /**
   * Scan for embedded scripts in files
   */
  private async scanForEmbeddedScripts(file: File): Promise<{ hasScripts: boolean; scripts: string[] }> {
    try {
      // Only scan text-based files and PDFs
      if (!file.type.startsWith('text/') && file.type !== 'application/pdf') {
        return { hasScripts: false, scripts: [] };
      }

      const text = await file.text();
      const scripts: string[] = [];

      // Common script patterns
      const scriptPatterns = [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /vbscript:/gi,
        /on\w+\s*=/gi,
        /eval\s*\(/gi,
        /document\.write/gi,
        /innerHTML/gi
      ];

      for (const pattern of scriptPatterns) {
        const matches = text.match(pattern);
        if (matches) {
          scripts.push(...matches.map(match => match.substring(0, 50)));
        }
      }

      return { hasScripts: scripts.length > 0, scripts };

    } catch (error) {
      console.error('❌ Script scanning error:', error);
      return { hasScripts: false, scripts: [] };
    }
  }

  /**
   * Basic malware signature check
   */
  private async basicMalwareSignatureCheck(file: File): Promise<{ suspicious: boolean; signatures: string[] }> {
    try {
      // Read first 1KB for signature check
      const buffer = await file.slice(0, 1024).arrayBuffer();
      const bytes = new Uint8Array(buffer);
      const signatures: string[] = [];

      // Convert to hex string for pattern matching
      const hexString = Array.from(bytes)
        .map(byte => byte.toString(16).padStart(2, '0'))
        .join('');

      // Common malware signatures (simplified examples)
      const malwarePatterns = [
        '4d5a', // PE executable header
        '7f454c46', // ELF executable header
        'cafebabe', // Java class file
        'feedface', // Mach-O executable
      ];

      for (const pattern of malwarePatterns) {
        if (hexString.includes(pattern)) {
          signatures.push(pattern);
        }
      }

      return { suspicious: signatures.length > 0, signatures };

    } catch (error) {
      console.error('❌ Malware signature check error:', error);
      return { suspicious: false, signatures: [] };
    }
  }

  /**
   * Check for suspicious filename patterns
   */
  private hasSuspiciousFilename(filename: string): boolean {
    const suspiciousPatterns = [
      /\.(exe|bat|cmd|com|pif|scr|vbs|js)$/i,
      /\.(php|asp|aspx|jsp)$/i,
      /\.(sh|bash|zsh|ps1)$/i,
      /[<>:"|?*]/,
      /^\./,
      /\s+$/,
      /\.(jpg|png|gif|pdf)\.exe$/i, // Double extension
      /script/i,
      /malware/i,
      /virus/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(filename));
  }

  /**
   * Generate secure filename
   */
  private async generateSecureFileName(originalName: string, uploadPath: string): Promise<string> {
    // Sanitize filename
    const sanitized = originalName
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .toLowerCase();

    // Add timestamp and random suffix
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const extension = this.getFileExtension(sanitized);
    const baseName = sanitized.replace(extension, '');

    return `${uploadPath}/${timestamp}_${randomSuffix}_${baseName}${extension}`;
  }

  /**
   * Upload to secure storage
   */
  private async uploadToSecureStorage(
    file: File,
    fileName: string,
    userId: number,
    config: FileUploadConfig
  ): Promise<{ success: boolean; fileUrl?: string; error?: string }> {
    try {
      console.log(`📤 Uploading to secure storage: ${fileName}`);

      const { data, error } = await supabase.storage
        .from('secure-uploads')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false,
          metadata: {
            uploadedBy: userId.toString(),
            uploadTime: new Date().toISOString(),
            originalName: file.name,
            securityScanned: 'true'
          }
        });

      if (error) {
        console.error('❌ Storage upload error:', error);
        return { success: false, error: 'Storage upload failed' };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('secure-uploads')
        .getPublicUrl(fileName);

      return { success: true, fileUrl: urlData.publicUrl };

    } catch (error) {
      console.error('❌ Secure storage upload error:', error);
      return { success: false, error: 'Upload failed' };
    }
  }

  /**
   * Quarantine unsafe file
   */
  private async quarantineFile(
    file: File,
    userId: number,
    securityScan: FileSecurityScan
  ): Promise<void> {
    try {
      console.log('🚨 Quarantining unsafe file...');

      // Store in quarantine bucket with restricted access
      const quarantineName = `quarantine/${Date.now()}_${userId}_${file.name}`;
      
      await supabase.storage
        .from('quarantine')
        .upload(quarantineName, file, {
          metadata: {
            quarantineReason: securityScan.threats.join(', '),
            securityScore: securityScan.score.toString(),
            uploadedBy: userId.toString(),
            quarantineTime: new Date().toISOString()
          }
        });

      // Log quarantine event
      await this.logSecurityEvent('FILE_QUARANTINED', userId, {
        fileName: file.name,
        threats: securityScan.threats,
        securityScore: securityScan.score
      });

    } catch (error) {
      console.error('❌ File quarantine error:', error);
    }
  }

  /**
   * Get file extension
   */
  private getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot === -1 ? '' : filename.substring(lastDot);
  }

  /**
   * Log file upload activity
   */
  private async logFileUpload(
    userId: number,
    file: File,
    secureFileName: string,
    securityScan: FileSecurityScan
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'file_upload_system',
          action: 'FILE_UPLOADED',
          target_type: 'file_upload',
          target_id: userId.toString(),
          metadata: {
            originalFileName: file.name,
            secureFileName,
            fileSize: file.size,
            mimeType: file.type,
            securityScore: securityScan.score,
            threats: securityScan.threats,
            warnings: securityScan.warnings,
            uploadTime: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log file upload:', error);
    }
  }

  /**
   * Log security events
   */
  private async logSecurityEvent(
    eventType: string,
    userId: number,
    metadata: any
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'file_security_system',
          action: `FILE_SECURITY_${eventType}`,
          target_type: 'file_security',
          target_id: userId.toString(),
          metadata: {
            ...metadata,
            timestamp: new Date().toISOString(),
            severity: 'HIGH'
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log security event:', error);
    }
  }
}

// Create singleton instance
export const secureFileUpload = new SecureFileUploadManager();

export default secureFileUpload;
