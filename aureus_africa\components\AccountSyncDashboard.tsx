import React, { useState, useEffect } from 'react';
import { AccountSyncService, SyncNotification, AccountMatch } from '../lib/accountSyncService';

interface AccountSyncDashboardProps {
  user: any;
  onSyncComplete?: () => void;
}

export const AccountSyncDashboard: React.FC<AccountSyncDashboardProps> = ({ user, onSyncComplete }) => {
  const [notifications, setNotifications] = useState<SyncNotification[]>([]);
  const [matches, setMatches] = useState<AccountMatch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<AccountMatch | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);

  useEffect(() => {
    loadSyncData();
  }, [user]);

  const loadSyncData = async () => {
    if (!user?.database_user?.id) return;

    setIsLoading(true);
    try {
      // Load notifications
      const notifs = await AccountSyncService.getSyncNotifications(user.database_user.id);
      setNotifications(notifs);

      // Auto-detect matches
      await AccountSyncService.autoDetectAndNotify(user.database_user.id);
      
      // Load fresh notifications after auto-detection
      const freshNotifs = await AccountSyncService.getSyncNotifications(user.database_user.id);
      setNotifications(freshNotifs);

    } catch (error) {
      console.error('Error loading sync data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNotificationClick = async (notification: SyncNotification) => {
    // Mark as read
    await AccountSyncService.markNotificationRead(notification.id);
    
    // Load matches if it's an account detection notification
    if (notification.notification_type === 'account_detected' && notification.metadata?.details) {
      const match: AccountMatch = {
        match_type: notification.metadata.match_type,
        matched_user_id: notification.metadata.details.matched_user_id,
        confidence_score: notification.metadata.confidence,
        match_details: notification.metadata.details
      };
      setSelectedMatch(match);
      setShowPreview(true);
      
      // Load preview data
      const summary = await AccountSyncService.getUserDataSummary(match.matched_user_id);
      setPreviewData(summary);
    }

    // Refresh notifications
    loadSyncData();
  };

  const handleLinkAccounts = async () => {
    if (!selectedMatch || !user?.database_user?.id) return;

    setIsLoading(true);
    try {
      // Create account link
      const linkId = await AccountSyncService.createAccountLink(
        user.id, // Supabase auth user ID
        user.database_user.id, // Database user ID
        user.telegram_id,
        'auto_detected'
      );

      if (linkId) {
        // Confirm and merge
        await AccountSyncService.confirmAccountLink(linkId, user.database_user.id);
        
        // Create success notification
        await AccountSyncService.createSyncNotification(
          user.database_user.id,
          'sync_completed',
          'Accounts Successfully Linked!',
          'Your accounts have been merged. All your data is now accessible from both platforms.',
          '/dashboard',
          'View Dashboard'
        );

        setShowPreview(false);
        setSelectedMatch(null);
        
        if (onSyncComplete) {
          onSyncComplete();
        }
      }
    } catch (error) {
      console.error('Account linking failed:', error);
      
      // Create error notification
      await AccountSyncService.createSyncNotification(
        user.database_user.id,
        'sync_failed',
        'Account Linking Failed',
        'There was an issue linking your accounts. Please try again or contact support.',
        '/support',
        'Contact Support'
      );
    } finally {
      setIsLoading(false);
      loadSyncData();
    }
  };

  if (notifications.length === 0 && !isLoading) {
    return null; // No sync notifications to show
  }

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
          <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          </svg>
          Account Synchronization
        </h3>
        {isLoading && (
          <div className="animate-spin w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full"></div>
        )}
      </div>

      {/* Notifications List */}
      <div className="space-y-3">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:border-blue-500/50 ${
              notification.priority === 'high' 
                ? 'bg-blue-500/10 border-blue-500/30' 
                : 'bg-gray-700/50 border-gray-600/30'
            }`}
            onClick={() => handleNotificationClick(notification)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-white mb-1">{notification.title}</h4>
                <p className="text-sm text-gray-300 mb-2">{notification.message}</p>
                {notification.action_text && (
                  <span className="inline-flex items-center text-sm text-blue-400 hover:text-blue-300">
                    {notification.action_text}
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                {notification.priority === 'high' && (
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                )}
                <span className="text-xs text-gray-400">
                  {new Date(notification.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Account Preview Modal */}
      {showPreview && selectedMatch && previewData && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-600 p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">Account Sync Preview</h3>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-400 hover:text-white"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-6">
              {/* Match Details */}
              <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                <h4 className="font-semibold text-blue-400 mb-2">Match Found</h4>
                <div className="text-sm text-gray-300 space-y-1">
                  <p><strong>Match Type:</strong> {selectedMatch.match_type.replace('_', ' ')}</p>
                  <p><strong>Confidence:</strong> {selectedMatch.confidence_score}%</p>
                  {selectedMatch.match_details.email && (
                    <p><strong>Email:</strong> {selectedMatch.match_details.email}</p>
                  )}
                  {selectedMatch.match_details.full_name && (
                    <p><strong>Name:</strong> {selectedMatch.match_details.full_name}</p>
                  )}
                </div>
              </div>

              {/* Data Preview */}
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <h4 className="font-semibold text-white mb-3">Current Account</h4>
                  <div className="text-sm text-gray-300 space-y-2">
                    <div className="flex justify-between">
                      <span>Purchases:</span>
                      <span className="text-white">0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Invested:</span>
                      <span className="text-white">$0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Referrals:</span>
                      <span className="text-white">0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Commissions:</span>
                      <span className="text-white">$0</span>
                    </div>
                  </div>
                </div>

                <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                  <h4 className="font-semibold text-green-400 mb-3">Matched Account</h4>
                  <div className="text-sm text-gray-300 space-y-2">
                    <div className="flex justify-between">
                      <span>Purchases:</span>
                      <span className="text-green-400 font-semibold">{previewData.purchases}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Invested:</span>
                      <span className="text-green-400 font-semibold">${previewData.total_invested}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Referrals:</span>
                      <span className="text-green-400 font-semibold">{previewData.referrals}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Commissions:</span>
                      <span className="text-green-400 font-semibold">${previewData.commission_balance}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Warning */}
              <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                <h4 className="font-semibold text-yellow-400 mb-2">⚠️ Important</h4>
                <p className="text-sm text-gray-300">
                  Linking accounts will merge all data from the matched account into your current account. 
                  This action cannot be undone. Please review the data carefully before proceeding.
                </p>
              </div>

              {/* Actions */}
              <div className="flex gap-3">
                <button
                  onClick={() => setShowPreview(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleLinkAccounts}
                  disabled={isLoading}
                  className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  {isLoading ? (
                    <span className="flex items-center justify-center">
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                      Linking...
                    </span>
                  ) : (
                    'Link Accounts'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
