/**
 * MULTI-FACTOR AUTHENTICATION (MFA) SYSTEM
 * 
 * This module provides TOTP-based MFA with backup codes,
 * QR code generation, and comprehensive security features.
 */

import { supabase } from './supabase';
import * as crypto from 'crypto';

interface MFASetupResult {
  success: boolean;
  secret?: string;
  qrCodeUrl?: string;
  backupCodes?: string[];
  error?: string;
}

interface MFAVerificationResult {
  success: boolean;
  verified: boolean;
  usedBackupCode?: boolean;
  remainingBackupCodes?: number;
  error?: string;
}

interface MFAStatus {
  enabled: boolean;
  setupComplete: boolean;
  backupCodesRemaining: number;
  lastUsed?: Date;
  deviceTrusted?: boolean;
}

interface TrustedDevice {
  id: string;
  userId: number;
  deviceFingerprint: string;
  deviceName: string;
  ipAddress: string;
  userAgent: string;
  trustedAt: Date;
  lastUsed: Date;
  isActive: boolean;
}

class MultiFactorAuthSystem {
  private readonly TOTP_WINDOW = 1; // Allow 1 step before/after current time
  private readonly TOTP_STEP = 30; // 30 seconds per step
  private readonly BACKUP_CODE_COUNT = 10;
  private readonly TRUSTED_DEVICE_DURATION = 30 * 24 * 60 * 60 * 1000; // 30 days

  /**
   * Setup MFA for a user
   */
  async setupMFA(userId: number): Promise<MFASetupResult> {
    try {
      console.log(`🔐 Setting up MFA for user ${userId}`);

      // Check if MFA is already enabled
      const existingMFA = await this.getMFAStatus(userId);
      if (existingMFA.enabled) {
        return { success: false, error: 'MFA is already enabled for this user' };
      }

      // Generate secret key
      const secret = this.generateSecret();
      
      // Get user email for QR code
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('email, username')
        .eq('id', userId)
        .single();

      if (userError || !user) {
        return { success: false, error: 'User not found' };
      }

      // Generate QR code URL
      const qrCodeUrl = this.generateQRCodeUrl(secret, user.email);

      // Generate backup codes
      const backupCodes = this.generateBackupCodes();

      // Store MFA configuration (not enabled yet)
      const { error: mfaError } = await supabase
        .from('user_mfa')
        .upsert({
          user_id: userId,
          secret_key: secret,
          backup_codes: backupCodes,
          is_enabled: false,
          setup_complete: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (mfaError) {
        console.error('❌ Failed to store MFA configuration:', mfaError);
        return { success: false, error: 'Failed to setup MFA' };
      }

      // Log MFA setup
      await this.logMFAEvent(userId, 'MFA_SETUP_INITIATED', {
        hasBackupCodes: true,
        backupCodeCount: backupCodes.length
      });

      console.log(`✅ MFA setup initiated for user ${userId}`);
      return {
        success: true,
        secret,
        qrCodeUrl,
        backupCodes
      };

    } catch (error) {
      console.error('❌ MFA setup error:', error);
      return { success: false, error: 'MFA setup failed' };
    }
  }

  /**
   * Verify MFA setup and enable MFA
   */
  async verifyMFASetup(userId: number, token: string): Promise<MFAVerificationResult> {
    try {
      console.log(`🔐 Verifying MFA setup for user ${userId}`);

      // Get MFA configuration
      const { data: mfaConfig, error: mfaError } = await supabase
        .from('user_mfa')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (mfaError || !mfaConfig) {
        return { success: false, verified: false, error: 'MFA not configured' };
      }

      if (mfaConfig.is_enabled) {
        return { success: false, verified: false, error: 'MFA is already enabled' };
      }

      // Verify TOTP token
      const isValid = this.verifyTOTP(mfaConfig.secret_key, token);

      if (!isValid) {
        await this.logMFAEvent(userId, 'MFA_SETUP_VERIFICATION_FAILED', {
          token: token.substring(0, 2) + '****'
        });
        return { success: false, verified: false, error: 'Invalid verification code' };
      }

      // Enable MFA
      const { error: enableError } = await supabase
        .from('user_mfa')
        .update({
          is_enabled: true,
          setup_complete: true,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (enableError) {
        console.error('❌ Failed to enable MFA:', enableError);
        return { success: false, verified: false, error: 'Failed to enable MFA' };
      }

      // Log successful MFA enablement
      await this.logMFAEvent(userId, 'MFA_ENABLED', {
        setupVerified: true
      });

      console.log(`✅ MFA enabled for user ${userId}`);
      return {
        success: true,
        verified: true,
        remainingBackupCodes: mfaConfig.backup_codes?.length || 0
      };

    } catch (error) {
      console.error('❌ MFA verification error:', error);
      return { success: false, verified: false, error: 'MFA verification failed' };
    }
  }

  /**
   * Verify MFA token during login
   */
  async verifyMFA(
    userId: number, 
    token: string, 
    deviceFingerprint?: string,
    trustDevice: boolean = false
  ): Promise<MFAVerificationResult> {
    try {
      console.log(`🔐 Verifying MFA for user ${userId}`);

      // Get MFA configuration
      const { data: mfaConfig, error: mfaError } = await supabase
        .from('user_mfa')
        .select('*')
        .eq('user_id', userId)
        .eq('is_enabled', true)
        .single();

      if (mfaError || !mfaConfig) {
        return { success: false, verified: false, error: 'MFA not enabled' };
      }

      let verified = false;
      let usedBackupCode = false;
      let remainingBackupCodes = mfaConfig.backup_codes?.length || 0;

      // First try TOTP verification
      if (this.verifyTOTP(mfaConfig.secret_key, token)) {
        verified = true;
        console.log('✅ TOTP verification successful');
      } else {
        // Try backup code verification
        const backupResult = await this.verifyBackupCode(userId, token);
        if (backupResult.verified) {
          verified = true;
          usedBackupCode = true;
          remainingBackupCodes = backupResult.remainingCodes || 0;
          console.log('✅ Backup code verification successful');
        }
      }

      if (!verified) {
        await this.logMFAEvent(userId, 'MFA_VERIFICATION_FAILED', {
          token: token.substring(0, 2) + '****',
          attemptType: 'login'
        });
        return { success: false, verified: false, error: 'Invalid MFA code' };
      }

      // Update last used timestamp
      await supabase
        .from('user_mfa')
        .update({
          last_used: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      // Trust device if requested
      if (trustDevice && deviceFingerprint) {
        await this.trustDevice(userId, deviceFingerprint);
      }

      // Log successful verification
      await this.logMFAEvent(userId, 'MFA_VERIFICATION_SUCCESS', {
        usedBackupCode,
        remainingBackupCodes,
        deviceTrusted: trustDevice
      });

      console.log(`✅ MFA verification successful for user ${userId}`);
      return {
        success: true,
        verified: true,
        usedBackupCode,
        remainingBackupCodes
      };

    } catch (error) {
      console.error('❌ MFA verification error:', error);
      return { success: false, verified: false, error: 'MFA verification failed' };
    }
  }

  /**
   * Check if device is trusted
   */
  async isDeviceTrusted(userId: number, deviceFingerprint: string): Promise<boolean> {
    try {
      const { data: trustedDevice, error } = await supabase
        .from('trusted_devices')
        .select('*')
        .eq('user_id', userId)
        .eq('device_fingerprint', deviceFingerprint)
        .eq('is_active', true)
        .single();

      if (error || !trustedDevice) {
        return false;
      }

      // Check if device trust has expired
      const trustExpiry = new Date(trustedDevice.trusted_at).getTime() + this.TRUSTED_DEVICE_DURATION;
      if (Date.now() > trustExpiry) {
        // Expire the trusted device
        await supabase
          .from('trusted_devices')
          .update({ is_active: false })
          .eq('id', trustedDevice.id);
        
        return false;
      }

      // Update last used
      await supabase
        .from('trusted_devices')
        .update({ last_used: new Date().toISOString() })
        .eq('id', trustedDevice.id);

      return true;

    } catch (error) {
      console.error('❌ Device trust check error:', error);
      return false;
    }
  }

  /**
   * Trust a device
   */
  private async trustDevice(userId: number, deviceFingerprint: string): Promise<void> {
    try {
      const deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      await supabase
        .from('trusted_devices')
        .insert({
          id: deviceId,
          user_id: userId,
          device_fingerprint: deviceFingerprint,
          device_name: 'Trusted Device',
          trusted_at: new Date().toISOString(),
          last_used: new Date().toISOString(),
          is_active: true
        });

      await this.logMFAEvent(userId, 'DEVICE_TRUSTED', {
        deviceFingerprint: deviceFingerprint.substring(0, 8) + '...'
      });

    } catch (error) {
      console.error('❌ Device trust error:', error);
    }
  }

  /**
   * Generate backup codes
   */
  async generateNewBackupCodes(userId: number): Promise<{ success: boolean; backupCodes?: string[]; error?: string }> {
    try {
      console.log(`🔐 Generating new backup codes for user ${userId}`);

      // Verify MFA is enabled
      const mfaStatus = await this.getMFAStatus(userId);
      if (!mfaStatus.enabled) {
        return { success: false, error: 'MFA is not enabled' };
      }

      // Generate new backup codes
      const backupCodes = this.generateBackupCodes();

      // Update database
      const { error } = await supabase
        .from('user_mfa')
        .update({
          backup_codes: backupCodes,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Failed to update backup codes:', error);
        return { success: false, error: 'Failed to generate backup codes' };
      }

      // Log backup code generation
      await this.logMFAEvent(userId, 'BACKUP_CODES_REGENERATED', {
        codeCount: backupCodes.length
      });

      console.log(`✅ New backup codes generated for user ${userId}`);
      return { success: true, backupCodes };

    } catch (error) {
      console.error('❌ Backup code generation error:', error);
      return { success: false, error: 'Backup code generation failed' };
    }
  }

  /**
   * Disable MFA
   */
  async disableMFA(userId: number, verificationToken: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`🔐 Disabling MFA for user ${userId}`);

      // Verify current MFA token before disabling
      const verification = await this.verifyMFA(userId, verificationToken);
      if (!verification.verified) {
        return { success: false, error: 'Invalid MFA code' };
      }

      // Disable MFA
      const { error } = await supabase
        .from('user_mfa')
        .update({
          is_enabled: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Failed to disable MFA:', error);
        return { success: false, error: 'Failed to disable MFA' };
      }

      // Remove all trusted devices
      await supabase
        .from('trusted_devices')
        .update({ is_active: false })
        .eq('user_id', userId);

      // Log MFA disabling
      await this.logMFAEvent(userId, 'MFA_DISABLED', {
        verificationRequired: true
      });

      console.log(`✅ MFA disabled for user ${userId}`);
      return { success: true };

    } catch (error) {
      console.error('❌ MFA disable error:', error);
      return { success: false, error: 'MFA disable failed' };
    }
  }

  /**
   * Get MFA status for user
   */
  async getMFAStatus(userId: number): Promise<MFAStatus> {
    try {
      const { data: mfaConfig, error } = await supabase
        .from('user_mfa')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error || !mfaConfig) {
        return {
          enabled: false,
          setupComplete: false,
          backupCodesRemaining: 0
        };
      }

      return {
        enabled: mfaConfig.is_enabled,
        setupComplete: mfaConfig.setup_complete,
        backupCodesRemaining: mfaConfig.backup_codes?.length || 0,
        lastUsed: mfaConfig.last_used ? new Date(mfaConfig.last_used) : undefined
      };

    } catch (error) {
      console.error('❌ MFA status check error:', error);
      return {
        enabled: false,
        setupComplete: false,
        backupCodesRemaining: 0
      };
    }
  }

  /**
   * Generate secret key for TOTP
   */
  private generateSecret(): string {
    return crypto.randomBytes(20).toString('base32');
  }

  /**
   * Generate QR code URL for TOTP setup
   */
  private generateQRCodeUrl(secret: string, userEmail: string): string {
    const issuer = 'Aureus Africa';
    const label = `${issuer}:${userEmail}`;
    
    const params = new URLSearchParams({
      secret,
      issuer,
      algorithm: 'SHA1',
      digits: '6',
      period: '30'
    });

    return `otpauth://totp/${encodeURIComponent(label)}?${params.toString()}`;
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    
    for (let i = 0; i < this.BACKUP_CODE_COUNT; i++) {
      // Generate 8-digit backup code
      const code = Math.random().toString().substr(2, 8);
      codes.push(code);
    }
    
    return codes;
  }

  /**
   * Verify TOTP token
   */
  private verifyTOTP(secret: string, token: string): boolean {
    try {
      const currentTime = Math.floor(Date.now() / 1000 / this.TOTP_STEP);
      
      // Check current time window and adjacent windows
      for (let i = -this.TOTP_WINDOW; i <= this.TOTP_WINDOW; i++) {
        const timeStep = currentTime + i;
        const expectedToken = this.generateTOTP(secret, timeStep);
        
        if (expectedToken === token) {
          return true;
        }
      }
      
      return false;

    } catch (error) {
      console.error('❌ TOTP verification error:', error);
      return false;
    }
  }

  /**
   * Generate TOTP token
   */
  private generateTOTP(secret: string, timeStep: number): string {
    try {
      const buffer = Buffer.alloc(8);
      buffer.writeUInt32BE(0, 0);
      buffer.writeUInt32BE(timeStep, 4);

      const hmac = crypto.createHmac('sha1', Buffer.from(secret, 'base32'));
      hmac.update(buffer);
      const digest = hmac.digest();

      const offset = digest[digest.length - 1] & 0x0f;
      const code = ((digest[offset] & 0x7f) << 24) |
                   ((digest[offset + 1] & 0xff) << 16) |
                   ((digest[offset + 2] & 0xff) << 8) |
                   (digest[offset + 3] & 0xff);

      return (code % 1000000).toString().padStart(6, '0');

    } catch (error) {
      console.error('❌ TOTP generation error:', error);
      return '000000';
    }
  }

  /**
   * Verify backup code
   */
  private async verifyBackupCode(userId: number, code: string): Promise<{
    verified: boolean;
    remainingCodes?: number;
  }> {
    try {
      const { data: mfaConfig, error } = await supabase
        .from('user_mfa')
        .select('backup_codes')
        .eq('user_id', userId)
        .single();

      if (error || !mfaConfig || !mfaConfig.backup_codes) {
        return { verified: false };
      }

      const backupCodes = mfaConfig.backup_codes;
      const codeIndex = backupCodes.indexOf(code);

      if (codeIndex === -1) {
        return { verified: false };
      }

      // Remove used backup code
      backupCodes.splice(codeIndex, 1);

      // Update database
      await supabase
        .from('user_mfa')
        .update({
          backup_codes: backupCodes,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      await this.logMFAEvent(userId, 'BACKUP_CODE_USED', {
        remainingCodes: backupCodes.length
      });

      return {
        verified: true,
        remainingCodes: backupCodes.length
      };

    } catch (error) {
      console.error('❌ Backup code verification error:', error);
      return { verified: false };
    }
  }

  /**
   * Log MFA events
   */
  private async logMFAEvent(userId: number, eventType: string, metadata: any): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'mfa_system',
          action: `MFA_${eventType}`,
          target_type: 'mfa_security',
          target_id: userId.toString(),
          metadata: {
            ...metadata,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log MFA event:', error);
    }
  }
}

// Create singleton instance
export const multiFactorAuth = new MultiFactorAuthSystem();

export default multiFactorAuth;
