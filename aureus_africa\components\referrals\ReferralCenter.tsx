import React, { useState } from 'react';
import { SponsorContact } from './SponsorContact';
import { DownlineTreeView } from './DownlineTreeView';

interface ReferralCenterProps {
  userId: number;
  className?: string;
}

export const ReferralCenter: React.FC<ReferralCenterProps> = ({ userId, className = '' }) => {
  const [activeTab, setActiveTab] = useState<'sponsor' | 'downline' | 'overview'>('overview');

  const tabs = [
    {
      id: 'overview',
      label: '📊 Overview',
      description: 'Referral summary and quick stats',
      icon: '📊'
    },
    {
      id: 'sponsor',
      label: '👥 My Sponsor',
      description: 'Contact and view sponsor information',
      icon: '👥'
    },
    {
      id: 'downline',
      label: '🌳 My Team',
      description: 'View and manage your referral network',
      icon: '🌳'
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
          <div className="text-3xl mb-2">👥</div>
          <h3 className="text-white text-lg font-semibold mb-2">Your Sponsor</h3>
          <p className="text-gray-400 text-sm mb-4">
            Get support and guidance from your sponsor
          </p>
          <button
            onClick={() => setActiveTab('sponsor')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors w-full"
          >
            Contact Sponsor
          </button>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
          <div className="text-3xl mb-2">🌳</div>
          <h3 className="text-white text-lg font-semibold mb-2">Your Team</h3>
          <p className="text-gray-400 text-sm mb-4">
            View and manage your referral network
          </p>
          <button
            onClick={() => setActiveTab('downline')}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors w-full"
          >
            View Team
          </button>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 text-center">
          <div className="text-3xl mb-2">🔗</div>
          <h3 className="text-white text-lg font-semibold mb-2">Referral Links</h3>
          <p className="text-gray-400 text-sm mb-4">
            Share your links to grow your network
          </p>
          <button
            onClick={() => {
              // Scroll to marketing toolkit or navigate to it
              const marketingSection = document.querySelector('[data-section="marketing"]');
              if (marketingSection) {
                marketingSection.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors w-full"
          >
            Get Links
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-white text-xl font-semibold mb-4">🚀 Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('sponsor')}
            className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center transition-colors"
          >
            <div className="text-2xl mb-2">💬</div>
            <div className="text-sm font-medium">Ask Sponsor</div>
          </button>
          
          <button
            onClick={() => setActiveTab('downline')}
            className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center transition-colors"
          >
            <div className="text-2xl mb-2">👥</div>
            <div className="text-sm font-medium">Contact Team</div>
          </button>
          
          <button
            onClick={() => {
              const marketingSection = document.querySelector('[data-section="marketing"]');
              if (marketingSection) {
                marketingSection.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center transition-colors"
          >
            <div className="text-2xl mb-2">📱</div>
            <div className="text-sm font-medium">Share Links</div>
          </button>
          
          <button
            onClick={() => {
              const marketingSection = document.querySelector('[data-section="marketing"]');
              if (marketingSection) {
                marketingSection.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center transition-colors"
          >
            <div className="text-2xl mb-2">✨</div>
            <div className="text-sm font-medium">AI Content</div>
          </button>
        </div>
      </div>

      {/* Tips and Best Practices */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-white text-xl font-semibold mb-4">💡 Referral Success Tips</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            {
              icon: '🎯',
              title: 'Target the Right Audience',
              description: 'Focus on people interested in gold mining and passive income opportunities.'
            },
            {
              icon: '🤝',
              title: 'Build Genuine Relationships',
              description: 'Focus on helping others succeed rather than just making sales.'
            },
            {
              icon: '📚',
              title: 'Educate Your Prospects',
              description: 'Share knowledge about gold mining and the benefits of share ownership.'
            },
            {
              icon: '📊',
              title: 'Track Your Performance',
              description: 'Use analytics to understand what works and optimize your approach.'
            },
            {
              icon: '💬',
              title: 'Stay Connected',
              description: 'Maintain regular contact with your team and provide ongoing support.'
            },
            {
              icon: '🎓',
              title: 'Keep Learning',
              description: 'Use the training resources to improve your marketing skills continuously.'
            }
          ].map((tip, index) => (
            <div key={index} className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <div className="text-2xl">{tip.icon}</div>
                <div>
                  <h4 className="text-white font-semibold mb-2">{tip.title}</h4>
                  <p className="text-gray-400 text-sm">{tip.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Support Resources */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-white text-xl font-semibold mb-4">🆘 Need Help?</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-3xl mb-2">👥</div>
            <h4 className="text-white font-medium mb-2">Contact Your Sponsor</h4>
            <p className="text-gray-400 text-sm mb-3">
              Your sponsor is here to help you succeed
            </p>
            <button
              onClick={() => setActiveTab('sponsor')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
            >
              Contact Now
            </button>
          </div>
          
          <div className="text-center">
            <div className="text-3xl mb-2">✈️</div>
            <h4 className="text-white font-medium mb-2">Telegram Support</h4>
            <p className="text-gray-400 text-sm mb-3">
              Join our community for instant help
            </p>
            <button
              onClick={() => window.open('https://t.me/AureusAllianceBot')}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
            >
              Join Telegram
            </button>
          </div>
          
          <div className="text-center">
            <div className="text-3xl mb-2">📧</div>
            <h4 className="text-white font-medium mb-2">Email Support</h4>
            <p className="text-gray-400 text-sm mb-3">
              Get help from our support team
            </p>
            <button
              onClick={() => window.open('mailto:<EMAIL>')}
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
            >
              Send Email
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className={className}>
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-white text-3xl font-bold mb-2">🤝 Referral Center</h1>
        <p className="text-gray-400">
          Manage your referral network, contact your sponsor, and grow your team
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-700 pb-4">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white'
            }`}
          >
            <span>{tab.icon}</span>
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'sponsor' && (
          <SponsorContact userId={userId} className="bg-gray-900 rounded-lg p-6" />
        )}
        {activeTab === 'downline' && (
          <DownlineTreeView userId={userId} className="bg-gray-900 rounded-lg p-6" />
        )}
      </div>
    </div>
  );
};
