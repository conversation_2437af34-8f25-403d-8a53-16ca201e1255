import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://fgubaqoftdeefcakejwu.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'
);

async function fixTelegramIdMapping() {
  try {
    console.log('🔧 Fixing Telegram ID mapping...');
    
    const telegramId = 1393852532;
    
    // Step 1: Remove Telegram ID from admin user in users table
    console.log('📝 Removing Telegram ID from admin user in users table...');
    const { data: removeData, error: removeError } = await supabase
      .from('users')
      .update({ 
        telegram_id: null,
        updated_at: new Date().toISOString()
      })
      .eq('username', 'admin')
      .eq('telegram_id', telegramId)
      .select();
      
    if (removeError) {
      console.error('❌ Error removing Telegram ID from admin user:', removeError);
      return;
    }
    
    console.log('✅ Removed Telegram ID from admin user:', removeData);
    
    // Step 2: Verify the telegram_users table has the correct mapping
    console.log('🔍 Verifying telegram_users table...');
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId);
      
    if (telegramError) {
      console.error('❌ Error checking telegram_users:', telegramError);
      return;
    }
    
    console.log('📋 Telegram user data:', telegramUser);
    
    if (!telegramUser || telegramUser.length === 0) {
      console.error('❌ No telegram user found with ID:', telegramId);
      return;
    }
    
    const tttfounderTelegramUser = telegramUser.find(u => u.username === 'TTTFOUNDER');
    if (!tttfounderTelegramUser) {
      console.error('❌ TTTFOUNDER not found in telegram_users table');
      return;
    }
    
    console.log('✅ TTTFOUNDER correctly mapped in telegram_users table:', tttfounderTelegramUser);
    
    // Step 3: Final verification
    console.log('🔍 Final verification...');
    const { data: finalCheck, error: finalError } = await supabase
      .from('users')
      .select('id, username, email, telegram_id')
      .eq('telegram_id', telegramId);
      
    if (finalError) {
      console.error('❌ Error in final verification:', finalError);
      return;
    }
    
    if (finalCheck && finalCheck.length === 0) {
      console.log('✅ SUCCESS! No users in users table have Telegram ID', telegramId);
      console.log('✅ The system will now correctly find TTTFOUNDER in telegram_users table');
      console.log('🎉 Telegram ID mapping fixed!');
    } else {
      console.error('❌ Still have users with Telegram ID in users table:', finalCheck);
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

fixTelegramIdMapping();
