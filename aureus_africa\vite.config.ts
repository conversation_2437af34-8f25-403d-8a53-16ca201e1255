import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      plugins: [react()],
      server: {
        host: '0.0.0.0',
        port: parseInt(process.env.PORT || '8000'),
        proxy: {
          '/api': {
            target: 'http://localhost:8002',
            changeOrigin: true,
            secure: false
          }
        }
      },
      preview: {
        host: '0.0.0.0',
        port: parseInt(process.env.PORT || '8000'),
        strictPort: true,
        allowedHosts: ['aureusafrica-production.up.railway.app', 'localhost']
      },
      build: {
        outDir: 'dist',
        sourcemap: true,
        rollupOptions: {
          output: {
            manualChunks: {
              vendor: ['react', 'react-dom'],
              supabase: ['@supabase/supabase-js']
            }
          }
        }
      },
      define: {
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || mode),
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY || ''),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY || ''),
        'process.env.VITE_SUPABASE_URL': JSON.stringify(env.VITE_SUPABASE_URL || ''),
        'process.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(env.VITE_SUPABASE_ANON_KEY || ''),
        'process.env.VITE_SUPABASE_SERVICE_ROLE_KEY': JSON.stringify(env.VITE_SUPABASE_SERVICE_ROLE_KEY || ''),
        'process.env.VITE_RESEND_API_KEY': JSON.stringify(env.VITE_RESEND_API_KEY || ''),
        'process.env.VITE_RESEND_FROM_EMAIL': JSON.stringify(env.VITE_RESEND_FROM_EMAIL || ''),
        'process.env.VITE_RESEND_FROM_NAME': JSON.stringify(env.VITE_RESEND_FROM_NAME || '')
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      }
    };
});
