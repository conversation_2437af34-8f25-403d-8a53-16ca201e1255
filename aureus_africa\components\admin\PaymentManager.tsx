import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { notificationService } from '../../lib/notificationService'

interface Payment {
  id: string
  user_id: number
  amount: number
  shares_to_purchase: number
  network: string
  currency: string
  sender_wallet: string
  receiver_wallet: string
  transaction_hash: string
  screenshot_url: string
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  approved_at?: string
  rejected_at?: string
  approved_by_admin_id?: number
  rejected_by_admin_id?: number
  rejection_reason?: string
  admin_notes?: string
  verification_status?: string
  users?: {
    id: number
    username: string
    email: string
    full_name?: string
  }
}

interface PaymentManagerProps {
  currentUser: any
}

export const PaymentManager: React.FC<PaymentManagerProps> = ({ currentUser }) => {
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)
  const [activeTab, setActiveTab] = useState<'pending' | 'approved' | 'rejected'>('pending')
  const [rejectionReason, setRejectionReason] = useState('')
  const [showRejectionModal, setShowRejectionModal] = useState(false)
  const [processingPayment, setProcessingPayment] = useState<string | null>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info', text: string } | null>(null)
  const [showScreenshotModal, setShowScreenshotModal] = useState(false)
  const [currentScreenshot, setCurrentScreenshot] = useState<string | null>(null)

  useEffect(() => {
    loadPayments()
  }, [activeTab])

  const loadPayments = async () => {
    setLoading(true)
    setMessage(null)

    try {
      const { data, error } = await supabase
        .from('crypto_payment_transactions')
        .select(`
          *,
          users!inner(
            id,
            username,
            email,
            full_name,
            phone_number,
            telegram_users(
              username,
              first_name,
              last_name
            )
          )
        `)
        .eq('status', activeTab)
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) {
        console.error('Supabase error:', error)
        throw new Error(`Database error: ${error.message}`)
      }

      // Process payments and add calculated shares if missing
      const processedPayments = (data || []).map(payment => {
        // Ensure all required fields have fallback values
        return {
          ...payment,
          amount: payment.amount || 0,
          shares_to_purchase: payment.shares_to_purchase || Math.floor((payment.amount || 0) / 5.0),
          network: payment.network || 'Unknown',
          currency: payment.currency || 'USDT',
          transaction_hash: payment.transaction_hash || '',
          users: payment.users || { username: 'Unknown', email: 'No email', full_name: 'Unknown User' }
        }
      })

      setPayments(processedPayments)

      if (processedPayments.length === 0) {
        setMessage({ type: 'info', text: `No ${activeTab} payments found` })
      }

    } catch (error: any) {
      console.error('Error loading payments:', error)
      setMessage({
        type: 'error',
        text: error.message || 'Failed to load payments. Please check your connection and try again.'
      })
      setPayments([]) // Clear payments on error
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Less than 1 hour ago'
    if (diffInHours < 24) return `${diffInHours} hours ago`
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} days ago`
  }

  const getNetworkIcon = (network: string) => {
    switch (network.toLowerCase()) {
      case 'bsc': return '🟡'
      case 'polygon': return '🟣'
      case 'tron': return '🔴'
      case 'bank_transfer': return '🏦'
      default: return '💎'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-500/20'
      case 'approved': return 'text-green-400 bg-green-500/20'
      case 'rejected': return 'text-red-400 bg-red-500/20'
      default: return 'text-gray-400 bg-gray-500/20'
    }
  }

  const getScreenshotUrl = (screenshotPath: string) => {
    if (!screenshotPath) return null

    console.log('Processing screenshot path:', screenshotPath)

    // If it's already a full URL, return as is
    if (screenshotPath.startsWith('http')) {
      return screenshotPath
    }

    // Handle different path formats
    let cleanPath = screenshotPath

    // Remove leading slash if present
    if (cleanPath.startsWith('/')) {
      cleanPath = cleanPath.substring(1)
    }

    // Remove 'proof/' prefix if present (in case it's duplicated)
    if (cleanPath.startsWith('proof/')) {
      cleanPath = cleanPath.substring(6)
    }

    console.log('Clean path for storage:', cleanPath)

    // Convert Supabase storage path to public URL
    const { data } = supabase.storage
      .from('proof')
      .getPublicUrl(cleanPath)

    console.log('Generated public URL:', data.publicUrl)
    return data.publicUrl
  }

  const handleViewScreenshot = async (payment: Payment) => {
    if (!payment.screenshot_url) {
      setMessage({ type: 'error', text: 'No screenshot available for this payment' })
      return
    }

    console.log('Attempting to view screenshot for payment:', payment.id)
    console.log('Screenshot URL from database:', payment.screenshot_url)

    try {
      // First try to get the public URL
      let screenshotUrl = getScreenshotUrl(payment.screenshot_url)

      if (!screenshotUrl) {
        setMessage({ type: 'error', text: 'Unable to generate screenshot URL' })
        return
      }

      console.log('Testing URL accessibility:', screenshotUrl)

      // Test if the URL is accessible
      try {
        const response = await fetch(screenshotUrl, { method: 'HEAD' })
        if (response.ok) {
          console.log('Public URL is accessible')
          setCurrentScreenshot(screenshotUrl)
          setShowScreenshotModal(true)
          return
        }
      } catch (fetchError) {
        console.log('Public URL fetch failed:', fetchError)
      }

      // If public URL fails, try downloading from storage
      console.log('Public URL failed, attempting storage download...')

      // Try different path variations
      const pathVariations = [
        payment.screenshot_url,
        payment.screenshot_url.replace(/^\/+/, ''), // Remove leading slashes
        payment.screenshot_url.replace(/^proof\//, ''), // Remove proof/ prefix
        `payment_${payment.user_id}_${payment.screenshot_url}` // Try with user prefix
      ]

      let fileData = null
      let downloadError = null

      for (const pathVariation of pathVariations) {
        console.log('Trying storage path:', pathVariation)
        const result = await supabase.storage
          .from('proof')
          .download(pathVariation)

        if (!result.error) {
          fileData = result.data
          console.log('Successfully downloaded from path:', pathVariation)
          break
        } else {
          console.log('Failed to download from path:', pathVariation, result.error)
          downloadError = result.error
        }
      }

      if (!fileData) {
        throw new Error(`Storage download failed: ${downloadError?.message || 'File not found in any path variation'}`)
      }

      // Create blob URL for viewing
      const blob = new Blob([fileData], { type: 'image/jpeg' })
      screenshotUrl = URL.createObjectURL(blob)

      console.log('Created blob URL for viewing')
      setCurrentScreenshot(screenshotUrl)
      setShowScreenshotModal(true)

    } catch (error: any) {
      console.error('Error loading screenshot:', error)
      setMessage({
        type: 'error',
        text: `Failed to load screenshot: ${error.message}. Please check the browser console for more details.`
      })
    }
  }

  const approvePayment = async (payment: Payment) => {
    if (!currentUser?.database_user?.id) {
      setMessage({ type: 'error', text: 'Admin user not found' })
      return
    }

    setProcessingPayment(payment.id)
    setMessage(null)

    try {
      // Get current phase for share calculation
      const { data: currentPhase, error: phaseError } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single()

      if (phaseError || !currentPhase) {
        throw new Error('No active investment phase found')
      }

      // Calculate shares based on current phase price
      const phasePrice = parseFloat(currentPhase.price_per_share) || 5.0 // Fallback to $5 if price is invalid
      const sharesAmount = Math.floor(payment.amount / phasePrice)

      if (sharesAmount <= 0) {
        throw new Error('Invalid share calculation - amount too small or phase price invalid')
      }

      // Start transaction
      const { error: updateError } = await supabase
        .from('crypto_payment_transactions')
        .update({
          status: 'approved',
          approved_at: new Date().toISOString(),
          approved_by_admin_id: currentUser.database_user.id,
          verification_status: 'verified'
        })
        .eq('id', payment.id)

      if (updateError) {
        throw updateError
      }

      // Create share purchase record
      const { error: shareError } = await supabase
        .from('aureus_share_purchases')
        .insert({
          user_id: payment.user_id,
          shares_purchased: sharesAmount,
          total_amount: payment.amount,
          price_per_share: parseFloat(currentPhase.price_per_share),
          phase_id: currentPhase.id,
          payment_method: `${payment.network} (${payment.currency})`,
          transaction_reference: payment.transaction_hash,
          status: 'active',
          purchase_date: new Date().toISOString()
        })

      if (shareError) {
        throw shareError
      }

      // Update phase sold count
      const { error: phaseUpdateError } = await supabase
        .from('investment_phases')
        .update({
          shares_sold: (currentPhase.shares_sold || 0) + sharesAmount,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentPhase.id)

      if (phaseUpdateError) {
        throw phaseUpdateError
      }

      // Process referral commissions (if applicable)
      const commissionData = await processReferralCommissions(payment.user_id, payment.amount, sharesAmount)

      // Send payment approval notification to user
      await notificationService.sendPaymentApprovalNotification(
        payment.user_id,
        {
          payment_id: payment.id,
          amount: payment.amount,
          shares: sharesAmount,
          price_per_share: phasePrice,
          network: payment.network,
          processed_date: new Date().toISOString()
        }
      )

      // Send commission notification to referrer if commission was earned
      if (commissionData && commissionData.referrerId) {
        await notificationService.sendCommissionEarnedNotification(
          commissionData.referrerId,
          {
            commission_id: commissionData.commissionId,
            referred_username: payment.users?.username || 'Unknown User',
            investment_amount: payment.amount,
            shares_purchased: sharesAmount,
            usdt_commission: commissionData.usdtCommission,
            share_commission: commissionData.shareCommission,
            total_usdt_balance: commissionData.totalUsdtBalance,
            total_share_balance: commissionData.totalShareBalance,
            earned_date: new Date().toISOString()
          }
        )
      }

      setMessage({
        type: 'success',
        text: `Payment approved successfully! ${sharesAmount} shares allocated to ${payment.users?.username}. Notifications sent.`
      })

      // Reload payments
      await loadPayments()
      setSelectedPayment(null)

    } catch (error: any) {
      console.error('Error approving payment:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to approve payment' })
    } finally {
      setProcessingPayment(null)
    }
  }

  const processReferralCommissions = async (userId: number, paymentAmount: number, sharesAmount: number) => {
    try {
      // Get user's referrer
      const { data: referralData, error: referralError } = await supabase
        .from('referrals')
        .select('referrer_id, referred_id, commission_rate')
        .eq('referred_id', userId)
        .eq('status', 'active')
        .single()

      if (referralError || !referralData) {
        console.log('No referrer found for user:', userId)
        return null
      }

      // Calculate commissions (15% USDT + 15% shares)
      const usdtCommission = paymentAmount * 0.15
      const shareCommission = sharesAmount * 0.15

      // Create commission transaction
      const { data: commissionTransaction, error: commissionError } = await supabase
        .from('commission_transactions')
        .insert({
          referrer_id: referralData.referrer_id,
          referred_id: referralData.referred_id,
          share_purchase_id: null, // Will be updated if needed
          commission_rate: 15.00,
          share_purchase_amount: paymentAmount,
          usdt_commission: usdtCommission,
          share_commission: shareCommission,
          status: 'approved',
          payment_date: new Date().toISOString()
        })
        .select()
        .single()

      if (commissionError) {
        console.error('Error creating commission:', commissionError)
        return null
      }

      // Update commission balance
      const { data: existingBalance, error: balanceError } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', referralData.referrer_id)
        .single()

      const currentUSDT = existingBalance ? parseFloat(existingBalance.usdt_balance || '0') : 0
      const currentShares = existingBalance ? parseFloat(existingBalance.share_balance || '0') : 0
      const totalEarnedUSDT = existingBalance ? parseFloat(existingBalance.total_earned_usdt || '0') : 0
      const totalEarnedShares = existingBalance ? parseFloat(existingBalance.total_earned_shares || '0') : 0

      const newUsdtBalance = currentUSDT + usdtCommission
      const newShareBalance = currentShares + shareCommission
      const newTotalEarnedUSDT = totalEarnedUSDT + usdtCommission
      const newTotalEarnedShares = totalEarnedShares + shareCommission

      const balanceUpdateData = {
        user_id: referralData.referrer_id,
        usdt_balance: newUsdtBalance,
        share_balance: newShareBalance,
        total_earned_usdt: newTotalEarnedUSDT,
        total_earned_shares: newTotalEarnedShares,
        last_updated: new Date().toISOString()
      }

      const { error: updateBalanceError } = await supabase
        .from('commission_balances')
        .upsert(balanceUpdateData, { onConflict: 'user_id' })

      if (updateBalanceError) {
        console.error('Error updating commission balance:', updateBalanceError)
        return null
      }

      // Return commission data for notification
      return {
        referrerId: referralData.referrer_id,
        commissionId: commissionTransaction.id,
        usdtCommission,
        shareCommission,
        totalUsdtBalance: newUsdtBalance,
        totalShareBalance: newShareBalance
      }

    } catch (error) {
      console.error('Error processing referral commissions:', error)
      return null
    }
  }

  const rejectPayment = async (payment: Payment, reason: string) => {
    if (!currentUser?.database_user?.id) {
      setMessage({ type: 'error', text: 'Admin user not found' })
      return
    }

    setProcessingPayment(payment.id)
    setMessage(null)

    try {
      const { error: updateError } = await supabase
        .from('crypto_payment_transactions')
        .update({
          status: 'rejected',
          rejected_at: new Date().toISOString(),
          rejected_by_admin_id: currentUser.database_user.id,
          rejection_reason: reason.trim(),
          verification_status: 'rejected'
        })
        .eq('id', payment.id)

      if (updateError) {
        throw updateError
      }

      // Send payment rejection notification to user
      await notificationService.sendPaymentRejectionNotification(
        payment.user_id,
        {
          payment_id: payment.id,
          amount: payment.amount,
          network: payment.network,
          tx_hash: payment.transaction_hash || '',
          rejection_reason: reason.trim(),
          rejected_date: new Date().toISOString()
        }
      )

      setMessage({
        type: 'success',
        text: `Payment rejected successfully. User ${payment.users?.username} has been notified.`
      })

      // Reload payments
      await loadPayments()
      setSelectedPayment(null)
      setShowRejectionModal(false)
      setRejectionReason('')

    } catch (error: any) {
      console.error('Error rejecting payment:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to reject payment' })
    } finally {
      setProcessingPayment(null)
    }
  }

  const handleRejectClick = (payment: Payment) => {
    setSelectedPayment(payment)
    setShowRejectionModal(true)
    setRejectionReason('')
  }

  const handleRejectSubmit = () => {
    if (!selectedPayment || !rejectionReason.trim()) {
      setMessage({ type: 'error', text: 'Please provide a rejection reason' })
      return
    }

    if (rejectionReason.trim().length < 5) {
      setMessage({ type: 'error', text: 'Rejection reason must be at least 5 characters long' })
      return
    }

    rejectPayment(selectedPayment, rejectionReason)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Payment Management</h2>
        <button
          onClick={loadPayments}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Refresh'}
        </button>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg border ${
          message.type === 'success' ? 'bg-green-900/20 border-green-500/30 text-green-300' :
          message.type === 'error' ? 'bg-red-900/20 border-red-500/30 text-red-300' :
          'bg-blue-900/20 border-blue-500/30 text-blue-300'
        }`}>
          {message.text}
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        {(['pending', 'approved', 'rejected'] as const).map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab
                ? 'bg-yellow-500 text-black'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            }`}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)} Payments
          </button>
        ))}
      </div>

      {/* Payments List */}
      <div className="bg-gray-800 rounded-lg border border-gray-700">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading payments...</p>
          </div>
        ) : payments.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-400">No {activeTab} payments found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {payments.map((payment) => (
              <div key={payment.id} className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
                {/* Header with amount and status */}
                <div className="bg-gray-750 px-6 py-4 border-b border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getNetworkIcon(payment.network)}</span>
                      <div>
                        <h3 className="text-xl font-bold text-white">
                          {formatCurrency(payment.amount)}
                        </h3>
                        <p className="text-sm text-gray-400">
                          {(payment.shares_to_purchase || 0).toLocaleString()} shares
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(payment.status)}`}>
                        {payment.status.toUpperCase()}
                      </span>
                      <span className="text-sm text-gray-400">
                        {getTimeAgo(payment.created_at)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Main content */}
                <div className="p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* User Information */}
                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-white mb-3">👤 User Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Name:</span>
                          <span className="text-white font-medium">
                            {payment.users?.full_name || payment.users?.username || 'Unknown User'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Email:</span>
                          <span className="text-white font-mono text-xs">
                            {payment.users?.email || 'No email'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">User ID:</span>
                          <span className="text-white font-mono">
                            {payment.user_id}
                          </span>
                        </div>

                        {/* Telegram Username */}
                        {payment.users?.telegram_users?.[0]?.username && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Telegram:</span>
                            <a
                              href={`https://t.me/${payment.users.telegram_users[0].username}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-400 hover:text-blue-300 font-mono text-xs underline transition-colors"
                              title="Contact user on Telegram"
                            >
                              @{payment.users.telegram_users[0].username}
                            </a>
                          </div>
                        )}

                        {/* Phone Number */}
                        {payment.users?.phone_number && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Phone:</span>
                            <a
                              href={`tel:${payment.users.phone_number}`}
                              className="text-green-400 hover:text-green-300 font-mono text-xs underline transition-colors"
                              title="Call user"
                            >
                              {payment.users.phone_number}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Payment Details */}
                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-white mb-3">💳 Payment Details</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Network:</span>
                          <span className="text-white font-medium">
                            {payment.network || 'Unknown'} ({payment.currency || 'Unknown'})
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-400">Payment ID:</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-white font-mono text-xs break-all max-w-xs">
                              {payment.id}
                            </span>
                            <button
                              onClick={() => navigator.clipboard.writeText(payment.id)}
                              className="p-1 text-gray-400 hover:text-white transition-colors"
                              title="Copy Payment ID"
                            >
                              📋
                            </button>
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Created:</span>
                          <span className="text-white">
                            {formatDate(payment.created_at)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Wallet Information */}
                  <div className="mt-6 p-4 bg-gray-900 rounded-lg border border-gray-600">
                    <h4 className="text-lg font-semibold text-white mb-3">🔗 Wallet & Transaction Details</h4>
                    <div className="space-y-3 text-sm">
                      <div>
                        <span className="text-gray-400 block mb-1">Sender Wallet:</span>
                        <div className="bg-gray-800 p-2 rounded border border-gray-600">
                          <span className="text-green-400 font-mono text-xs break-all">
                            {payment.sender_wallet || 'Not provided'}
                          </span>
                          {payment.sender_wallet && (
                            <button
                              onClick={() => navigator.clipboard.writeText(payment.sender_wallet)}
                              className="ml-2 text-blue-400 hover:text-blue-300 text-xs"
                              title="Copy to clipboard"
                            >
                              📋
                            </button>
                          )}
                        </div>
                      </div>

                      <div>
                        <span className="text-gray-400 block mb-1">Receiver Wallet:</span>
                        <div className="bg-gray-800 p-2 rounded border border-gray-600">
                          <span className="text-blue-400 font-mono text-xs break-all">
                            {payment.receiver_wallet || 'Not provided'}
                          </span>
                          {payment.receiver_wallet && (
                            <button
                              onClick={() => navigator.clipboard.writeText(payment.receiver_wallet)}
                              className="ml-2 text-blue-400 hover:text-blue-300 text-xs"
                              title="Copy to clipboard"
                            >
                              📋
                            </button>
                          )}
                        </div>
                      </div>

                      {payment.transaction_hash && payment.network !== 'BANK_TRANSFER' ? (
                        <div>
                          <span className="text-gray-400 block mb-1">Transaction Hash:</span>
                          <div className="bg-gray-800 p-2 rounded border border-gray-600">
                            <span className="text-yellow-400 font-mono text-xs break-all">
                              {payment.transaction_hash}
                            </span>
                            <button
                              onClick={() => navigator.clipboard.writeText(payment.transaction_hash)}
                              className="ml-2 text-blue-400 hover:text-blue-300 text-xs"
                              title="Copy to clipboard"
                            >
                              📋
                            </button>
                          </div>
                        </div>
                      ) : payment.network === 'BANK_TRANSFER' ? (
                        <div className="text-center py-2">
                          <span className="text-orange-400 text-sm">🏦 Bank Transfer - No transaction hash</span>
                        </div>
                      ) : (
                        <div className="text-center py-2">
                          <span className="text-red-400 text-sm">⚠️ No transaction hash provided</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Rejection Reason */}
                  {payment.rejection_reason && (
                    <div className="mt-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                      <h4 className="text-red-300 font-semibold mb-2">❌ Rejection Reason:</h4>
                      <p className="text-red-300 text-sm">{payment.rejection_reason}</p>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="mt-6 flex flex-wrap gap-3">
                    {payment.status === 'pending' && (
                      <>
                        <button
                          onClick={() => approvePayment(payment)}
                          disabled={processingPayment === payment.id}
                          className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50 font-medium"
                        >
                          {processingPayment === payment.id ? (
                            <span className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                              Processing...
                            </span>
                          ) : (
                            '✅ Approve Payment'
                          )}
                        </button>
                        <button
                          onClick={() => handleRejectClick(payment)}
                          disabled={processingPayment === payment.id}
                          className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 font-medium"
                        >
                          ❌ Reject Payment
                        </button>
                      </>
                    )}

                    {payment.screenshot_url && payment.network !== 'BANK_TRANSFER' ? (
                      <button
                        onClick={() => handleViewScreenshot(payment)}
                        className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                      >
                        📷 View Screenshot
                      </button>
                    ) : payment.network === 'BANK_TRANSFER' ? (
                      <div className="px-6 py-3 bg-gray-600 text-gray-300 rounded-lg font-medium">
                        🏦 Bank Transfer - No screenshot required
                      </div>
                    ) : (
                      <div className="px-6 py-3 bg-orange-600 text-white rounded-lg font-medium">
                        ⚠️ No screenshot provided
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Rejection Modal */}
      {showRejectionModal && selectedPayment && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-bold text-white mb-4">Reject Payment</h3>
            
            <div className="mb-4">
              <p className="text-gray-300 mb-2">
                <strong>Payment:</strong> {formatCurrency(selectedPayment.amount || 0)}
              </p>
              <p className="text-gray-300 mb-4">
                <strong>User:</strong> {selectedPayment.users?.full_name || selectedPayment.users?.username || 'Unknown User'}
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Rejection Reason *
              </label>
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Please provide a detailed reason for rejecting this payment..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
                rows={4}
              />
              <p className="text-xs text-gray-400 mt-1">
                This message will be sent to the user. Minimum 5 characters.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleRejectSubmit}
                disabled={processingPayment === selectedPayment.id || !rejectionReason.trim()}
                className="flex-1 py-2 px-4 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {processingPayment === selectedPayment.id ? 'Processing...' : 'Confirm Rejection'}
              </button>
              <button
                onClick={() => {
                  setShowRejectionModal(false)
                  setSelectedPayment(null)
                  setRejectionReason('')
                }}
                disabled={processingPayment === selectedPayment.id}
                className="flex-1 py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Screenshot Modal */}
      {showScreenshotModal && currentScreenshot && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
          <div className="relative max-w-4xl max-h-[90vh] mx-4">
            <button
              onClick={() => {
                setShowScreenshotModal(false)
                setCurrentScreenshot(null)
                // Clean up blob URL if it was created
                if (currentScreenshot.startsWith('blob:')) {
                  URL.revokeObjectURL(currentScreenshot)
                }
              }}
              className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors"
            >
              ✕
            </button>
            <img
              src={currentScreenshot}
              alt="Payment Screenshot"
              className="max-w-full max-h-full object-contain rounded-lg"
              onError={() => {
                setMessage({ type: 'error', text: 'Failed to load screenshot image' })
                setShowScreenshotModal(false)
                setCurrentScreenshot(null)
              }}
            />
            <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-2 rounded-lg text-sm">
              Payment Proof Screenshot
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
