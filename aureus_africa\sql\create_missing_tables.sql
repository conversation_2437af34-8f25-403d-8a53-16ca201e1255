-- Create Missing Tables for Web App Integration
-- Execute this SQL in your Supabase SQL editor

-- =====================================================
-- 1. CREATE USER_NOTIFICATION_SUMMARY TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.user_notification_summary (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    unread_count INTEGER DEFAULT 0,
    last_notification_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_user_notification_summary_user_id ON public.user_notification_summary(user_id);

-- Enable RLS
ALTER TABLE public.user_notification_summary ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can only see their own notification summary
CREATE POLICY "Users can view their own notification summary" ON public.user_notification_summary
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = user_notification_summary.user_id 
            AND users.auth_user_id = auth.uid()
        )
    );

-- =====================================================
-- 2. CREATE USER_PAYMENT_METHODS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.user_payment_methods (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    method_type VARCHAR(50) NOT NULL, -- 'bank_transfer', 'crypto', etc.
    method_name VARCHAR(100) NOT NULL,
    method_details JSONB NOT NULL, -- Store account details, wallet addresses, etc.
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_user_payment_methods_user_id ON public.user_payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_user_payment_methods_active ON public.user_payment_methods(user_id, is_active);

-- Enable RLS
ALTER TABLE public.user_payment_methods ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can manage their own payment methods
CREATE POLICY "Users can manage their own payment methods" ON public.user_payment_methods
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE users.id = user_payment_methods.user_id 
            AND users.auth_user_id = auth.uid()
        )
    );

-- =====================================================
-- 3. FIX COMMISSION_BALANCES TABLE ACCESS
-- =====================================================

-- Check if commission_balances table exists and has proper RLS
DO $$
BEGIN
    -- Enable RLS if not already enabled
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'commission_balances') THEN
        ALTER TABLE public.commission_balances ENABLE ROW LEVEL SECURITY;
        
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view their own commission balance" ON public.commission_balances;
        
        -- Create policy for users to view their own commission balance
        CREATE POLICY "Users can view their own commission balance" ON public.commission_balances
            FOR SELECT USING (
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE users.id = commission_balances.user_id 
                    AND users.auth_user_id = auth.uid()
                )
            );
            
        RAISE NOTICE '✅ Commission balances table RLS policies updated';
    ELSE
        RAISE NOTICE '⚠️  Commission balances table does not exist - please create it first';
    END IF;
END $$;

-- =====================================================
-- 4. CREATE INITIAL DATA FOR EXISTING USERS
-- =====================================================

-- Create notification summaries for existing users
INSERT INTO public.user_notification_summary (user_id, unread_count, last_notification_at)
SELECT 
    id as user_id,
    0 as unread_count,
    NOW() as last_notification_at
FROM public.users
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_notification_summary 
    WHERE user_notification_summary.user_id = users.id
);

-- =====================================================
-- 5. SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Missing tables created successfully!';
    RAISE NOTICE '📊 user_notification_summary: Created with RLS policies';
    RAISE NOTICE '💳 user_payment_methods: Created with RLS policies';
    RAISE NOTICE '💰 commission_balances: RLS policies updated';
    RAISE NOTICE '🔒 All tables configured with proper security';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 404 errors should now be resolved!';
END $$;
