/**
 * SAFE SVG ICON COMPONENT
 * 
 * A React component that safely handles SVG path data with validation
 * and error recovery to prevent SVG path attribute errors.
 */

'use client';

import React, { useState, useEffect } from 'react';

interface SafeSVGIconProps {
  pathData: string;
  className?: string;
  width?: number | string;
  height?: number | string;
  viewBox?: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number | string;
  strokeLinecap?: 'butt' | 'round' | 'square';
  strokeLinejoin?: 'miter' | 'round' | 'bevel';
  [key: string]: any;
}

/**
 * Validates and fixes SVG path data
 */
function validateAndFixSVGPath(pathData: string): string {
  try {
    if (!pathData || typeof pathData !== 'string') {
      return 'M 0 0 L 10 10'; // Simple fallback path
    }

    // Clean the path data
    let cleanPath = pathData
      .replace(/[^\d\.\-\s,MmLlHhVvCcSsQqTtAaZz]/g, '') // Remove invalid characters
      .replace(/(\d)([MmLlHhVvCcSsQqTtAaZz])/g, '$1 $2') // Add space between numbers and commands
      .replace(/([MmLlHhVvCcSsQqTtAaZz])(\d)/g, '$1 $2') // Add space between commands and numbers
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // Basic validation - ensure path starts with a move command
    if (!cleanPath.match(/^[Mm]/)) {
      cleanPath = 'M 0 0 ' + cleanPath;
    }

    // Test the path by creating a temporary SVG element
    if (typeof document !== 'undefined') {
      const testSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      const testPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      testPath.setAttribute('d', cleanPath);
      testSvg.appendChild(testPath);
      
      // If we get here without error, the path is valid
      return cleanPath;
    }

    return cleanPath;

  } catch (error) {
    console.error('❌ SVG path validation failed:', error);
    console.error('   Original path:', pathData);
    
    // Return a safe fallback path
    return 'M 0 0 L 10 10';
  }
}

/**
 * Common SVG icon paths for fallbacks
 */
const FALLBACK_ICONS = {
  default: 'M 0 0 L 10 10',
  check: 'M 9 12 L 2 2 4 4 L 19 7',
  x: 'M 18 6 L 6 18 M 6 6 L 18 18',
  arrow: 'M 5 12 H 19 M 12 5 L 19 12 L 12 19',
  user: 'M 20 21 V 19 A 4 4 0 0 0 16 15 H 8 A 4 4 0 0 0 4 19 V 21 M 16 7 A 4 4 0 1 1 8 7 A 4 4 0 0 1 16 7 Z',
  settings: 'M 12 15 A 3 3 0 1 0 12 9 A 3 3 0 0 0 12 15 Z M 19.4 15 A 1.65 1.65 0 0 0 18.82 13.37 L 19.9 12.5 A 1.65 1.65 0 0 0 19.4 9 L 18.32 8.13 A 1.65 1.65 0 0 0 19.4 9 Z'
};

const SafeSVGIcon: React.FC<SafeSVGIconProps> = ({
  pathData,
  className = '',
  width = 24,
  height = 24,
  viewBox = '0 0 24 24',
  fill = 'none',
  stroke = 'currentColor',
  strokeWidth = 2,
  strokeLinecap = 'round',
  strokeLinejoin = 'round',
  fallbackIcon = 'default',
  ...props
}) => {
  const [validPath, setValidPath] = useState<string>('');
  const [hasError, setHasError] = useState<boolean>(false);

  useEffect(() => {
    try {
      setHasError(false);
      
      if (!pathData) {
        setValidPath(FALLBACK_ICONS[fallbackIcon as keyof typeof FALLBACK_ICONS] || FALLBACK_ICONS.default);
        return;
      }

      const fixedPath = validateAndFixSVGPath(pathData);
      setValidPath(fixedPath);

    } catch (error) {
      console.error('❌ SVG path processing error:', error);
      setHasError(true);
      setValidPath(FALLBACK_ICONS[fallbackIcon as keyof typeof FALLBACK_ICONS] || FALLBACK_ICONS.default);
    }
  }, [pathData, fallbackIcon]);

  // Error boundary for SVG rendering
  const handleSVGError = (error: any) => {
    console.error('❌ SVG rendering error:', error);
    setHasError(true);
    setValidPath(FALLBACK_ICONS.default);
  };

  return (
    <svg
      className={`safe-svg-icon ${className} ${hasError ? 'svg-error' : ''}`}
      width={width}
      height={height}
      viewBox={viewBox}
      fill={fill}
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeLinecap={strokeLinecap}
      strokeLinejoin={strokeLinejoin}
      onError={handleSVGError}
      {...props}
    >
      <path d={validPath} />
      {hasError && (
        <title>SVG Icon (fallback due to error)</title>
      )}
    </svg>
  );
};

/**
 * Pre-built safe icons with validated paths
 */
export const SafeIcons = {
  Check: (props: Omit<SafeSVGIconProps, 'pathData'>) => (
    <SafeSVGIcon pathData={FALLBACK_ICONS.check} {...props} />
  ),
  
  X: (props: Omit<SafeSVGIconProps, 'pathData'>) => (
    <SafeSVGIcon pathData={FALLBACK_ICONS.x} {...props} />
  ),
  
  Arrow: (props: Omit<SafeSVGIconProps, 'pathData'>) => (
    <SafeSVGIcon pathData={FALLBACK_ICONS.arrow} {...props} />
  ),
  
  User: (props: Omit<SafeSVGIconProps, 'pathData'>) => (
    <SafeSVGIcon pathData={FALLBACK_ICONS.user} {...props} />
  ),
  
  Settings: (props: Omit<SafeSVGIconProps, 'pathData'>) => (
    <SafeSVGIcon pathData={FALLBACK_ICONS.settings} {...props} />
  ),

  // Loading spinner with safe animation
  Loading: (props: Omit<SafeSVGIconProps, 'pathData'>) => (
    <SafeSVGIcon 
      pathData="M 12 2 V 6 M 12 18 V 22 M 4.93 4.93 L 7.76 7.76 M 16.24 16.24 L 19.07 19.07 M 2 12 H 6 M 18 12 H 22 M 4.93 19.07 L 7.76 16.24 M 16.24 7.76 L 19.07 4.93"
      className={`animate-spin ${props.className || ''}`}
      {...props} 
    />
  )
};

/**
 * Hook for safe SVG path validation
 */
export function useSafeSVGPath(pathData: string) {
  const [validPath, setValidPath] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(true);

  useEffect(() => {
    try {
      const fixed = validateAndFixSVGPath(pathData);
      setValidPath(fixed);
      setIsValid(fixed === pathData); // Check if path needed fixing
    } catch (error) {
      setValidPath(FALLBACK_ICONS.default);
      setIsValid(false);
    }
  }, [pathData]);

  return { validPath, isValid };
}

/**
 * Utility function to validate SVG paths in bulk
 */
export function validateSVGPaths(paths: Record<string, string>): Record<string, string> {
  const validatedPaths: Record<string, string> = {};
  
  for (const [key, path] of Object.entries(paths)) {
    try {
      validatedPaths[key] = validateAndFixSVGPath(path);
    } catch (error) {
      console.error(`❌ Failed to validate SVG path for ${key}:`, error);
      validatedPaths[key] = FALLBACK_ICONS.default;
    }
  }
  
  return validatedPaths;
}

export default SafeSVGIcon;
