#!/usr/bin/env node

/**
 * TEST PASSWORD RESET FUNCTIONALITY
 * 
 * This script tests the complete password reset flow:
 * 1. Create password reset request
 * 2. Verify email is sent
 * 3. Test token validation
 * 4. Test password reset with token
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testPasswordReset() {
  console.log('🧪 Testing password reset functionality...\n');

  try {
    // Test with a known user email (you can change this to a test user)
    const testEmail = '<EMAIL>';
    
    console.log(`1️⃣ Testing password reset request for: ${testEmail}`);
    
    // Import the password reset function
    const { createPasswordResetRequest, validateResetToken, resetPasswordWithToken } = await import('./lib/passwordReset.ts');
    
    // Step 1: Create password reset request
    const resetResult = await createPasswordResetRequest(testEmail, 'test_client');
    
    console.log('📧 Password reset request result:', {
      success: resetResult.success,
      message: resetResult.message,
      error: resetResult.error
    });
    
    if (!resetResult.success && resetResult.error !== 'User not found') {
      console.error('❌ Password reset request failed');
      return false;
    }
    
    // Step 2: Check if user exists and has reset token
    console.log('\n2️⃣ Checking if reset token was created...');
    
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, reset_token, reset_token_expires')
      .eq('email', testEmail)
      .single();
    
    if (userError || !user) {
      console.log('⚠️ User not found - this is expected for non-existent emails');
      console.log('✅ Password reset system correctly handles non-existent emails');
      return true;
    }
    
    console.log('👤 User found:', {
      id: user.id,
      email: user.email,
      hasResetToken: !!user.reset_token,
      tokenExpires: user.reset_token_expires
    });
    
    if (!user.reset_token) {
      console.error('❌ Reset token was not created');
      return false;
    }
    
    // Step 3: Test token validation
    console.log('\n3️⃣ Testing token validation...');
    
    const tokenValidation = await validateResetToken(user.reset_token);
    console.log('🔍 Token validation result:', {
      valid: tokenValidation.valid,
      error: tokenValidation.error,
      userEmail: tokenValidation.user?.email
    });
    
    if (!tokenValidation.valid) {
      console.error('❌ Token validation failed');
      return false;
    }
    
    // Step 4: Test password reset with token
    console.log('\n4️⃣ Testing password reset with token...');
    
    const newPassword = 'NewSecurePassword123!';
    const passwordResetResult = await resetPasswordWithToken(user.reset_token, newPassword);
    
    console.log('🔐 Password reset result:', {
      success: passwordResetResult.success,
      message: passwordResetResult.message,
      error: passwordResetResult.error
    });
    
    if (!passwordResetResult.success) {
      console.error('❌ Password reset failed');
      return false;
    }
    
    // Step 5: Verify token was cleared
    console.log('\n5️⃣ Verifying reset token was cleared...');
    
    const { data: updatedUser, error: updatedUserError } = await supabase
      .from('users')
      .select('reset_token, reset_token_expires')
      .eq('id', user.id)
      .single();
    
    if (updatedUserError) {
      console.error('❌ Failed to check updated user:', updatedUserError);
      return false;
    }
    
    console.log('🧹 Token cleanup result:', {
      resetToken: updatedUser.reset_token,
      resetTokenExpires: updatedUser.reset_token_expires
    });
    
    if (updatedUser.reset_token !== null) {
      console.error('❌ Reset token was not cleared after password reset');
      return false;
    }
    
    console.log('\n✅ Password reset functionality test completed successfully!');
    console.log('📋 Test Summary:');
    console.log('   ✅ Password reset request created');
    console.log('   ✅ Reset token generated and stored');
    console.log('   ✅ Token validation working');
    console.log('   ✅ Password reset with token working');
    console.log('   ✅ Reset token properly cleared after use');
    
    return true;
    
  } catch (error) {
    console.error('❌ Password reset test failed:', error);
    return false;
  }
}

// Run the test
testPasswordReset().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
