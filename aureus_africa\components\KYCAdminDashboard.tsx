/**
 * KYC ADMIN DASHBOARD
 * 
 * Administrative interface for reviewing and managing KYC submissions
 * with detailed verification information and approval workflow.
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { kycManager } from '../lib/kycManager';

interface KYCSubmission {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  id_number: string;
  verification_status: string;
  overall_verification_score: number;
  face_match_confidence: number;
  liveness_check_passed: boolean;
  document_valid: boolean;
  submitted_at: string;
  users: {
    email: string;
    username: string;
    created_at: string;
  };
}

interface KYCStatistics {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  expired: number;
  averageProcessingTime: number;
  approvalRate: number;
}

const KYCAdminDashboard: React.FC = () => {
  const [submissions, setSubmissions] = useState<KYCSubmission[]>([]);
  const [statistics, setStatistics] = useState<KYCStatistics>({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    expired: 0,
    averageProcessingTime: 0,
    approvalRate: 0
  });
  const [selectedSubmission, setSelectedSubmission] = useState<KYCSubmission | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isReviewing, setIsReviewing] = useState(false);
  const [reviewComments, setReviewComments] = useState('');
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');

  // Fetch KYC data
  const fetchKYCData = useCallback(async () => {
    try {
      console.log('📋 Fetching KYC data...');

      // Fetch pending submissions
      const pendingSubmissions = await kycManager.getPendingKYCSubmissions(100);
      setSubmissions(pendingSubmissions);

      // Fetch statistics
      const stats = await kycManager.getKYCStatistics();
      setStatistics(stats);

      console.log('✅ KYC data loaded');

    } catch (error) {
      console.error('❌ Error fetching KYC data:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    fetchKYCData();
    const interval = setInterval(fetchKYCData, 30000);
    return () => clearInterval(interval);
  }, [fetchKYCData]);

  // Handle KYC review
  const handleReview = async (decision: 'approve' | 'reject') => {
    if (!selectedSubmission) return;

    try {
      setIsReviewing(true);
      console.log(`👨‍💼 Reviewing KYC: ${decision}`);

      const result = await kycManager.reviewKYC({
        kycId: selectedSubmission.id,
        reviewerId: 'admin_user', // In real app, get from auth context
        decision,
        comments: reviewComments
      });

      if (result.success) {
        // Update local state
        setSubmissions(prev => prev.filter(s => s.id !== selectedSubmission.id));
        setSelectedSubmission(null);
        setReviewComments('');
        
        // Refresh statistics
        const stats = await kycManager.getKYCStatistics();
        setStatistics(stats);

        console.log(`✅ KYC ${decision} completed`);
      } else {
        console.error('❌ KYC review failed:', result.error);
      }

    } catch (error) {
      console.error('❌ KYC review error:', error);
    } finally {
      setIsReviewing(false);
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'pending_review': return 'text-yellow-600 bg-yellow-100';
      case 'expired': return 'text-gray-600 bg-gray-100';
      default: return 'text-blue-600 bg-blue-100';
    }
  };

  // Get confidence color
  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-lg">Loading KYC dashboard...</span>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">KYC Admin Dashboard</h1>
        <p className="text-gray-600">Review and manage customer verification submissions</p>
      </div>

      {/* Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Submissions</p>
              <p className="text-2xl font-bold text-gray-900">{statistics.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Review</p>
              <p className="text-2xl font-bold text-yellow-600">{statistics.pending}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Approval Rate</p>
              <p className="text-2xl font-bold text-green-600">{statistics.approvalRate}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Processing</p>
              <p className="text-2xl font-bold text-purple-600">{statistics.averageProcessingTime.toFixed(1)}h</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Submissions List */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">Pending Submissions</h3>
                <button
                  onClick={fetchKYCData}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Refresh
                </button>
              </div>
            </div>

            <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
              {submissions.length === 0 ? (
                <div className="px-6 py-8 text-center">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p className="text-gray-500">No pending submissions</p>
                </div>
              ) : (
                submissions.map((submission) => (
                  <div
                    key={submission.id}
                    className={`px-6 py-4 cursor-pointer hover:bg-gray-50 ${
                      selectedSubmission?.id === submission.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                    }`}
                    onClick={() => setSelectedSubmission(submission)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h4 className="text-sm font-medium text-gray-900">
                            {submission.first_name} {submission.last_name}
                          </h4>
                          <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(submission.verification_status)}`}>
                            {submission.verification_status.replace('_', ' ').toUpperCase()}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {submission.users.email} • ID: {submission.id_number}
                        </p>
                        <div className="flex items-center mt-2 text-xs text-gray-500">
                          <span>Score: </span>
                          <span className={`ml-1 font-medium ${getConfidenceColor(submission.overall_verification_score)}`}>
                            {(submission.overall_verification_score * 100).toFixed(1)}%
                          </span>
                          <span className="mx-2">•</span>
                          <span>Submitted: {new Date(submission.submitted_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {submission.liveness_check_passed && (
                          <span className="text-green-500" title="Liveness check passed">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                        )}
                        {submission.document_valid && (
                          <span className="text-blue-500" title="Document valid">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Review Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Review Details</h3>
            </div>

            {selectedSubmission ? (
              <div className="p-6 space-y-6">
                {/* User Information */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">User Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Name:</span>
                      <span className="font-medium">{selectedSubmission.first_name} {selectedSubmission.last_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span className="font-medium">{selectedSubmission.users.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">ID Number:</span>
                      <span className="font-medium">{selectedSubmission.id_number}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Submitted:</span>
                      <span className="font-medium">{new Date(selectedSubmission.submitted_at).toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Verification Scores */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Verification Scores</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-600">Overall Score</span>
                        <span className={`font-medium ${getConfidenceColor(selectedSubmission.overall_verification_score)}`}>
                          {(selectedSubmission.overall_verification_score * 100).toFixed(1)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            selectedSubmission.overall_verification_score >= 0.8 ? 'bg-green-500' :
                            selectedSubmission.overall_verification_score >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${selectedSubmission.overall_verification_score * 100}%` }}
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-600">Face Match</span>
                        <span className={`font-medium ${getConfidenceColor(selectedSubmission.face_match_confidence)}`}>
                          {(selectedSubmission.face_match_confidence * 100).toFixed(1)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            selectedSubmission.face_match_confidence >= 0.8 ? 'bg-green-500' :
                            selectedSubmission.face_match_confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${selectedSubmission.face_match_confidence * 100}%` }}
                        />
                      </div>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Liveness Check:</span>
                      <span className={`font-medium ${selectedSubmission.liveness_check_passed ? 'text-green-600' : 'text-red-600'}`}>
                        {selectedSubmission.liveness_check_passed ? 'PASSED' : 'FAILED'}
                      </span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Document Valid:</span>
                      <span className={`font-medium ${selectedSubmission.document_valid ? 'text-green-600' : 'text-red-600'}`}>
                        {selectedSubmission.document_valid ? 'VALID' : 'INVALID'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Review Comments */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-2">
                    Review Comments
                  </label>
                  <textarea
                    rows={3}
                    value={reviewComments}
                    onChange={(e) => setReviewComments(e.target.value)}
                    placeholder="Add comments about this submission..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleReview('approve')}
                    disabled={isReviewing}
                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    {isReviewing ? 'Processing...' : 'Approve'}
                  </button>
                  <button
                    onClick={() => handleReview('reject')}
                    disabled={isReviewing}
                    className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    {isReviewing ? 'Processing...' : 'Reject'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="p-6 text-center">
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <p className="text-gray-500">Select a submission to review</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCAdminDashboard;
