import React, { useState, useEffect } from 'react';
import { competitionService, Competition, PrizeTier, LeaderboardEntry, CompetitionStats } from '../../lib/services/competitionService';
import { referralSyncService } from '../../lib/services/referralSyncService';
import { supabase } from '../../lib/supabase';

interface InvestmentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  is_active: boolean;
}

interface NewCompetitionForm {
  name: string;
  description: string;
  phaseId: number;
  totalPrizePool: number;
  minimumQualification: number;
  endDate: string;
}

const CompetitionManager: React.FC = () => {
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null);
  const [prizeTiers, setPrizeTiers] = useState<PrizeTier[]>([]);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [investmentPhases, setInvestmentPhases] = useState<InvestmentPhase[]>([]);
  const [stats, setStats] = useState<CompetitionStats>({
    totalParticipants: 0,
    qualifiedParticipants: 0,
    leadingVolume: 0,
    totalPrizePool: 0,
    minimumQualification: 2500
  });
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [activeTab, setActiveTab] = useState<'phases' | 'competitions' | 'leaderboard'>('phases');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedPhaseId, setSelectedPhaseId] = useState<number | null>(null);
  const [newCompetition, setNewCompetition] = useState<NewCompetitionForm>({
    name: '',
    description: 'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
    phaseId: 0,
    totalPrizePool: 210000,
    minimumQualification: 2500,
    endDate: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load investment phases
      const { data: phases, error: phasesError } = await supabase
        .from('investment_phases')
        .select('*')
        .order('phase_number', { ascending: true });

      if (!phasesError && phases) {
        setInvestmentPhases(phases);

        // Set default phase for new competition form
        const activePhase = phases.find(p => p.is_active);
        if (activePhase && newCompetition.phaseId === 0) {
          setNewCompetition(prev => ({
            ...prev,
            phaseId: activePhase.id,
            name: `Gold Diggers Club - ${activePhase.phase_name}`
          }));
        }
      }

      // Get current competition
      const currentCompetition = await competitionService.getCurrentCompetition();
      if (currentCompetition) {
        setSelectedCompetition(currentCompetition);

        // Load competition data
        const [prizeTiersData, leaderboardData, statsData] = await Promise.all([
          competitionService.getPrizeTiers(currentCompetition.id),
          competitionService.getLeaderboard(currentCompetition.id, 20),
          competitionService.getCompetitionStats(currentCompetition.id)
        ]);

        setPrizeTiers(prizeTiersData);
        setLeaderboard(leaderboardData);
        setStats(statsData);
      }
    } catch (error) {
      console.error('Error loading competition data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCompetition = async () => {
    try {
      if (!newCompetition.name || !newCompetition.phaseId) {
        alert('Please fill in all required fields');
        return;
      }

      const competitionId = await competitionService.createCompetition(
        newCompetition.name,
        newCompetition.description,
        newCompetition.phaseId,
        newCompetition.totalPrizePool,
        newCompetition.minimumQualification
      );

      if (competitionId) {
        // Set up default prize tiers
        await competitionService.setupDefaultPrizeTiers(competitionId);

        // Reload data
        await loadData();

        // Reset form
        setShowCreateForm(false);
        setNewCompetition({
          name: '',
          description: 'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
          phaseId: 0,
          totalPrizePool: 210000,
          minimumQualification: 2500,
          endDate: ''
        });

        alert('Competition created successfully!');
      } else {
        alert('Failed to create competition');
      }
    } catch (error) {
      console.error('Error creating competition:', error);
      alert('Failed to create competition');
    }
  };

  const handleSyncData = async () => {
    try {
      setSyncing(true);
      const success = await referralSyncService.syncReferralData();
      
      if (success) {
        // Reload data after sync
        await loadData();
        alert('Data synced successfully!');
      } else {
        alert('Sync failed. Check console for details.');
      }
    } catch (error) {
      console.error('Error syncing data:', error);
      alert('Sync failed. Check console for details.');
    } finally {
      setSyncing(false);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-4">Competition Manager</h2>
        <div className="text-center py-8">Loading...</div>
      </div>
    );
  }

  if (!selectedCompetition) {
    return (
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-4">Competition Manager</h2>
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
          No active competition found. Please run the setup script to create a competition.
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">🏆 Phase Competition Management</h2>
          <p className="text-gray-400 mt-1">
            Create and manage competitions for each investment phase
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={handleSyncData}
            disabled={syncing}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2"
          >
            <span>{syncing ? '🔄' : '🔄'}</span>
            <span>{syncing ? 'Syncing...' : 'Sync Referral Data'}</span>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        <nav className="flex space-x-8">
          {[
            { id: 'phases', name: 'Phase Overview', icon: '📊' },
            { id: 'competitions', name: 'Active Competitions', icon: '🏆' },
            { id: 'leaderboard', name: 'Leaderboard', icon: '🥇' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-yellow-500 text-yellow-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Competition Creation Form */}
      {showCreateForm && (
        <div className="bg-white rounded-lg shadow mb-8 p-6">
          <h3 className="text-lg font-semibold mb-4">Create New Competition</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Competition Name *
              </label>
              <input
                type="text"
                value={newCompetition.name}
                onChange={(e) => setNewCompetition(prev => ({ ...prev, name: e.target.value }))}
                className="w-full border border-gray-300 rounded px-3 py-2"
                placeholder="Gold Diggers Club - Phase X"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Investment Phase *
              </label>
              <select
                value={newCompetition.phaseId}
                onChange={(e) => {
                  const phaseId = parseInt(e.target.value);
                  const phase = investmentPhases.find(p => p.id === phaseId);
                  setNewCompetition(prev => ({
                    ...prev,
                    phaseId,
                    name: phase ? `Gold Diggers Club - ${phase.phase_name}` : prev.name
                  }));
                }}
                className="w-full border border-gray-300 rounded px-3 py-2"
              >
                <option value={0}>Select Phase</option>
                {investmentPhases.map(phase => (
                  <option key={phase.id} value={phase.id}>
                    {phase.phase_name} (${phase.price_per_share}/share)
                    {phase.is_active ? ' - ACTIVE' : ''}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Total Prize Pool ($)
              </label>
              <input
                type="number"
                value={newCompetition.totalPrizePool}
                onChange={(e) => setNewCompetition(prev => ({ ...prev, totalPrizePool: parseFloat(e.target.value) || 0 }))}
                className="w-full border border-gray-300 rounded px-3 py-2"
                min="0"
                step="1000"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Minimum Qualification ($)
              </label>
              <input
                type="number"
                value={newCompetition.minimumQualification}
                onChange={(e) => setNewCompetition(prev => ({ ...prev, minimumQualification: parseFloat(e.target.value) || 0 }))}
                className="w-full border border-gray-300 rounded px-3 py-2"
                min="0"
                step="500"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={newCompetition.description}
                onChange={(e) => setNewCompetition(prev => ({ ...prev, description: e.target.value }))}
                className="w-full border border-gray-300 rounded px-3 py-2"
                rows={3}
              />
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-4">
            <button
              onClick={() => setShowCreateForm(false)}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateCompetition}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Create Competition
            </button>
          </div>
        </div>
      )}

      {/* Competition Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-2">Competition Info</h3>
          <div className="space-y-2 text-sm">
            <div><strong>Name:</strong> {selectedCompetition.name}</div>
            <div><strong>Status:</strong> 
              <span className={`ml-2 px-2 py-1 rounded text-xs ${
                selectedCompetition.status === 'active' ? 'bg-green-100 text-green-800' :
                selectedCompetition.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {selectedCompetition.status.toUpperCase()}
              </span>
            </div>
            <div><strong>Prize Pool:</strong> {formatCurrency(selectedCompetition.total_prize_pool)}</div>
            <div><strong>Min Qualification:</strong> {formatCurrency(selectedCompetition.minimum_qualification_amount)}</div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-2">Participants</h3>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-blue-600">{stats.totalParticipants}</div>
            <div className="text-sm text-gray-600">Total Participants</div>
            <div className="text-lg font-semibold text-green-600">{stats.qualifiedParticipants}</div>
            <div className="text-sm text-gray-600">Qualified</div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-2">Leading Volume</h3>
          <div className="space-y-2">
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(stats.leadingVolume)}
            </div>
            <div className="text-sm text-gray-600">Highest Referral Volume</div>
          </div>
        </div>
      </div>

      {/* Prize Tiers */}
      <div className="bg-white rounded-lg shadow mb-8">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold">Prize Distribution</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {prizeTiers.map((tier) => (
              <div key={tier.id} className="border rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <span className="text-2xl mr-2">{tier.emoji}</span>
                  <span className="font-semibold">{tier.tier_name}</span>
                </div>
                <div className="text-lg font-bold text-green-600">
                  {tier.tier_rank_start === tier.tier_rank_end 
                    ? formatCurrency(tier.prize_amount)
                    : `${formatCurrency(tier.prize_amount)} each`
                  }
                </div>
                <div className="text-sm text-gray-600">
                  Rank {tier.tier_rank_start}
                  {tier.tier_rank_start !== tier.tier_rank_end && ` - ${tier.tier_rank_end}`}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Leaderboard */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h3 className="text-lg font-semibold">Current Leaderboard</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rank
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Participant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Referral Volume
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Direct Referrals
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Qualified Referrals
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {leaderboard.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                    No participants yet. Sync referral data to populate the leaderboard.
                  </td>
                </tr>
              ) : (
                leaderboard.map((participant, index) => (
                  <tr key={participant.user_id} className={index < 3 ? 'bg-yellow-50' : ''}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">
                          {index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '🏆'}
                        </span>
                        <span className="font-semibold">#{participant.calculated_rank}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="font-medium text-gray-900">
                          {participant.full_name || participant.username}
                        </div>
                        <div className="text-sm text-gray-500">ID: {participant.user_id}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-lg font-semibold text-green-600">
                        {formatCurrency(participant.total_referral_volume)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className="text-lg font-semibold">
                        {participant.direct_referrals_count}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <span className="text-lg font-semibold text-green-600">
                        {participant.qualified_referrals_count}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 rounded text-xs ${
                        participant.is_qualified 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {participant.is_qualified ? 'QUALIFIED' : 'NOT QUALIFIED'}
                      </span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CompetitionManager;
