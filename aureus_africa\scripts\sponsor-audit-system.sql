-- AUREUS ALLIANCE - SPONSOR CHANGE AUDIT SYSTEM
-- This script creates comprehensive audit tracking for sponsor changes
-- Execute in Supabase SQL Editor

-- ========================================
-- 1. CREATE SPONSOR CHANGE LOG TABLE
-- ========================================

CREATE TABLE IF NOT EXISTS public.sponsor_change_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- User being changed
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  user_username VARCHAR(255) NOT NULL,
  user_email VARCHAR(255),
  
  -- Old sponsor information
  old_sponsor_id INTEGER REFERENCES public.users(id) ON DELETE SET NULL,
  old_sponsor_username VARCHAR(255),
  old_referral_id UUID,
  
  -- New sponsor information
  new_sponsor_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  new_sponsor_username VARCHAR(255) NOT NULL,
  new_referral_id UUID,
  
  -- Change metadata
  change_reason TEXT,
  changed_by_admin VARCHAR(255) NOT NULL,
  changed_by_telegram_id BIGINT,
  change_method VARCHAR(50) DEFAULT 'manual', -- 'manual', 'telegram_bot', 'web_admin', 'api'
  
  -- Audit information
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(255),
  
  -- Timestamps
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_sponsor_change_log_user_id ON public.sponsor_change_log(user_id);
CREATE INDEX IF NOT EXISTS idx_sponsor_change_log_old_sponsor ON public.sponsor_change_log(old_sponsor_id);
CREATE INDEX IF NOT EXISTS idx_sponsor_change_log_new_sponsor ON public.sponsor_change_log(new_sponsor_id);
CREATE INDEX IF NOT EXISTS idx_sponsor_change_log_changed_at ON public.sponsor_change_log(changed_at);
CREATE INDEX IF NOT EXISTS idx_sponsor_change_log_admin ON public.sponsor_change_log(changed_by_admin);

-- Add table comment
COMMENT ON TABLE public.sponsor_change_log IS 'Comprehensive audit log for all sponsor changes in the system';

-- ========================================
-- 2. CREATE REFERRAL AUDIT TRIGGER
-- ========================================

-- Function to automatically log referral changes
CREATE OR REPLACE FUNCTION public.log_referral_changes()
RETURNS TRIGGER AS $$
DECLARE
  old_sponsor_username VARCHAR(255);
  new_sponsor_username VARCHAR(255);
  user_info RECORD;
BEGIN
  -- Only log status changes from active to inactive (sponsor changes)
  IF TG_OP = 'UPDATE' AND OLD.status = 'active' AND NEW.status = 'inactive' THEN
    
    -- Get old sponsor username
    SELECT username INTO old_sponsor_username 
    FROM public.users 
    WHERE id = OLD.referrer_id;
    
    -- Get user information
    SELECT username, email INTO user_info
    FROM public.users 
    WHERE id = OLD.referred_id;
    
    -- Log the deactivation (part 1 of sponsor change)
    INSERT INTO public.sponsor_change_log (
      user_id,
      user_username,
      user_email,
      old_sponsor_id,
      old_sponsor_username,
      old_referral_id,
      new_sponsor_id,
      new_sponsor_username,
      new_referral_id,
      change_reason,
      changed_by_admin,
      change_method,
      changed_at
    ) VALUES (
      OLD.referred_id,
      user_info.username,
      user_info.email,
      OLD.referrer_id,
      old_sponsor_username,
      OLD.id,
      OLD.referrer_id, -- Temporary, will be updated when new referral is created
      'DEACTIVATED',
      NULL,
      'Referral relationship deactivated',
      'system',
      'trigger',
      NOW()
    );
    
  END IF;
  
  -- Log new referral creation
  IF TG_OP = 'INSERT' AND NEW.status = 'active' THEN
    
    -- Get new sponsor username
    SELECT username INTO new_sponsor_username 
    FROM public.users 
    WHERE id = NEW.referrer_id;
    
    -- Get user information
    SELECT username, email INTO user_info
    FROM public.users 
    WHERE id = NEW.referred_id;
    
    -- Check if this is part of a sponsor change (look for recent deactivation)
    IF EXISTS (
      SELECT 1 FROM public.sponsor_change_log 
      WHERE user_id = NEW.referred_id 
      AND changed_at > NOW() - INTERVAL '5 minutes'
      AND new_sponsor_username = 'DEACTIVATED'
    ) THEN
      
      -- Update the existing log entry with new sponsor information
      UPDATE public.sponsor_change_log 
      SET 
        new_sponsor_id = NEW.referrer_id,
        new_sponsor_username = new_sponsor_username,
        new_referral_id = NEW.id,
        change_reason = COALESCE(change_reason, '') || ' - New sponsor assigned'
      WHERE user_id = NEW.referred_id 
      AND changed_at > NOW() - INTERVAL '5 minutes'
      AND new_sponsor_username = 'DEACTIVATED';
      
    ELSE
      
      -- Log as new referral (not a sponsor change)
      INSERT INTO public.sponsor_change_log (
        user_id,
        user_username,
        user_email,
        old_sponsor_id,
        old_sponsor_username,
        old_referral_id,
        new_sponsor_id,
        new_sponsor_username,
        new_referral_id,
        change_reason,
        changed_by_admin,
        change_method,
        changed_at
      ) VALUES (
        NEW.referred_id,
        user_info.username,
        user_info.email,
        NULL,
        NULL,
        NULL,
        NEW.referrer_id,
        new_sponsor_username,
        NEW.id,
        'Initial sponsor assignment',
        'system',
        'trigger',
        NOW()
      );
      
    END IF;
    
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS referral_audit_trigger ON public.referrals;
CREATE TRIGGER referral_audit_trigger
  AFTER INSERT OR UPDATE ON public.referrals
  FOR EACH ROW
  EXECUTE FUNCTION public.log_referral_changes();

-- ========================================
-- 3. CREATE SPONSOR STATISTICS VIEW
-- ========================================

CREATE OR REPLACE VIEW public.sponsor_statistics AS
SELECT 
  -- Overall statistics
  (SELECT COUNT(*) FROM public.users WHERE is_active = true) as total_active_users,
  (SELECT COUNT(DISTINCT referred_id) FROM public.referrals WHERE status = 'active') as users_with_sponsors,
  (SELECT COUNT(*) FROM public.users WHERE is_active = true) - 
  (SELECT COUNT(DISTINCT referred_id) FROM public.referrals WHERE status = 'active') as users_without_sponsors,
  (SELECT COUNT(*) FROM public.referrals WHERE status = 'active') as total_active_referrals,
  
  -- Change statistics
  (SELECT COUNT(*) FROM public.sponsor_change_log WHERE changed_at > NOW() - INTERVAL '30 days') as changes_last_30_days,
  (SELECT COUNT(*) FROM public.sponsor_change_log WHERE changed_at > NOW() - INTERVAL '7 days') as changes_last_7_days,
  (SELECT COUNT(*) FROM public.sponsor_change_log WHERE changed_at > NOW() - INTERVAL '1 day') as changes_last_24_hours,
  
  -- Most active admin
  (SELECT changed_by_admin FROM public.sponsor_change_log 
   GROUP BY changed_by_admin 
   ORDER BY COUNT(*) DESC 
   LIMIT 1) as most_active_admin;

-- ========================================
-- 4. CREATE TOP SPONSORS VIEW
-- ========================================

CREATE OR REPLACE VIEW public.top_sponsors AS
SELECT 
  u.id,
  u.username,
  u.full_name,
  u.email,
  COUNT(r.referred_id) as total_referrals,
  COALESCE(SUM(cb.total_earned_usdt), 0) as total_commission_earned,
  COALESCE(AVG(cb.total_earned_usdt), 0) as avg_commission_per_referral,
  MIN(r.created_at) as first_referral_date,
  MAX(r.created_at) as latest_referral_date
FROM public.users u
LEFT JOIN public.referrals r ON u.id = r.referrer_id AND r.status = 'active'
LEFT JOIN public.commission_balances cb ON u.id = cb.user_id
WHERE u.is_active = true
GROUP BY u.id, u.username, u.full_name, u.email
HAVING COUNT(r.referred_id) > 0
ORDER BY total_referrals DESC, total_commission_earned DESC;

-- ========================================
-- 5. CREATE SPONSOR CHANGE SUMMARY FUNCTION
-- ========================================

CREATE OR REPLACE FUNCTION public.get_sponsor_change_summary(
  user_id_param INTEGER DEFAULT NULL,
  days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
  change_id UUID,
  user_username VARCHAR(255),
  old_sponsor VARCHAR(255),
  new_sponsor VARCHAR(255),
  change_reason TEXT,
  changed_by VARCHAR(255),
  change_method VARCHAR(50),
  changed_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    scl.id,
    scl.user_username,
    scl.old_sponsor_username,
    scl.new_sponsor_username,
    scl.change_reason,
    scl.changed_by_admin,
    scl.change_method,
    scl.changed_at
  FROM public.sponsor_change_log scl
  WHERE 
    (user_id_param IS NULL OR scl.user_id = user_id_param)
    AND scl.changed_at > NOW() - (days_back || ' days')::INTERVAL
  ORDER BY scl.changed_at DESC;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 6. CREATE SPONSOR VALIDATION FUNCTION
-- ========================================

CREATE OR REPLACE FUNCTION public.validate_sponsor_change(
  user_id_param INTEGER,
  new_sponsor_id_param INTEGER
)
RETURNS TABLE (
  is_valid BOOLEAN,
  error_message TEXT
) AS $$
DECLARE
  user_exists BOOLEAN;
  sponsor_exists BOOLEAN;
  is_circular BOOLEAN;
  same_user BOOLEAN;
BEGIN
  -- Check if user exists and is active
  SELECT EXISTS(SELECT 1 FROM public.users WHERE id = user_id_param AND is_active = true) INTO user_exists;
  
  -- Check if sponsor exists and is active
  SELECT EXISTS(SELECT 1 FROM public.users WHERE id = new_sponsor_id_param AND is_active = true) INTO sponsor_exists;
  
  -- Check if user is trying to sponsor themselves
  SELECT (user_id_param = new_sponsor_id_param) INTO same_user;
  
  -- Check for circular reference (simplified check)
  SELECT EXISTS(
    SELECT 1 FROM public.referrals 
    WHERE referrer_id = user_id_param 
    AND referred_id = new_sponsor_id_param 
    AND status = 'active'
  ) INTO is_circular;
  
  -- Return validation result
  IF NOT user_exists THEN
    RETURN QUERY SELECT false, 'User not found or inactive';
  ELSIF NOT sponsor_exists THEN
    RETURN QUERY SELECT false, 'Sponsor not found or inactive';
  ELSIF same_user THEN
    RETURN QUERY SELECT false, 'User cannot sponsor themselves';
  ELSIF is_circular THEN
    RETURN QUERY SELECT false, 'Circular reference detected';
  ELSE
    RETURN QUERY SELECT true, 'Validation passed';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 7. GRANT PERMISSIONS
-- ========================================

-- Grant permissions to authenticated users (adjust as needed)
GRANT SELECT ON public.sponsor_change_log TO authenticated;
GRANT SELECT ON public.sponsor_statistics TO authenticated;
GRANT SELECT ON public.top_sponsors TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_sponsor_change_summary TO authenticated;
GRANT EXECUTE ON FUNCTION public.validate_sponsor_change TO authenticated;

-- Grant full access to service role (for admin operations)
GRANT ALL ON public.sponsor_change_log TO service_role;

-- ========================================
-- 8. CREATE SAMPLE QUERIES
-- ========================================

-- View recent sponsor changes
-- SELECT * FROM public.get_sponsor_change_summary(NULL, 7);

-- View sponsor statistics
-- SELECT * FROM public.sponsor_statistics;

-- View top sponsors
-- SELECT * FROM public.top_sponsors LIMIT 10;

-- Validate a sponsor change
-- SELECT * FROM public.validate_sponsor_change(123, 456);

-- View all changes for a specific user
-- SELECT * FROM public.get_sponsor_change_summary(123, 365);

COMMENT ON SCHEMA public IS 'Sponsor audit system installed successfully. Use the views and functions to monitor sponsor changes.';

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ SPONSOR AUDIT SYSTEM INSTALLED SUCCESSFULLY!';
  RAISE NOTICE '';
  RAISE NOTICE '📊 Available Views:';
  RAISE NOTICE '   • sponsor_statistics - Overall system statistics';
  RAISE NOTICE '   • top_sponsors - Ranking of sponsors by referrals';
  RAISE NOTICE '';
  RAISE NOTICE '🔧 Available Functions:';
  RAISE NOTICE '   • get_sponsor_change_summary(user_id, days) - Change history';
  RAISE NOTICE '   • validate_sponsor_change(user_id, sponsor_id) - Validation';
  RAISE NOTICE '';
  RAISE NOTICE '📋 Tables Created:';
  RAISE NOTICE '   • sponsor_change_log - Comprehensive audit trail';
  RAISE NOTICE '';
  RAISE NOTICE '⚡ Triggers Active:';
  RAISE NOTICE '   • referral_audit_trigger - Automatic change logging';
END $$;
