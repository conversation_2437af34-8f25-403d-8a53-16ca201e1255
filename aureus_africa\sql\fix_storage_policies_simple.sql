-- Simple Storage Policy Fix for Web App
-- Execute this SQL in your Supabase SQL editor

-- =====================================================
-- 1. DROP ALL EXISTING RESTRICTIVE POLICIES
-- =====================================================

-- Drop all existing policies on storage.objects
DROP POLICY IF EXISTS "Authenticated users can upload payment proofs" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload to proof bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload to proof bucket" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view proof files" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their proof files" ON storage.objects;
DROP POLICY IF EXISTS "Admins can upload marketing materials" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view marketing materials" ON storage.objects;
DROP POLICY IF EXISTS "Admins can manage marketing materials" ON storage.objects;

-- =====================================================
-- 2. CREATE SIMPLE PERMISSIVE POLICIES
-- =====================================================

-- Allow service role to do everything (for web app functionality)
CREATE POLICY "Service role can manage all storage" ON storage.objects
    FOR ALL USING (true) WITH CHECK (true);

-- Allow anyone to upload to proof bucket (simplified for web app)
CREATE POLICY "Allow uploads to proof bucket" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'proof');

-- Allow anyone to view files in proof bucket
CREATE POLICY "Allow viewing proof files" ON storage.objects
    FOR SELECT USING (bucket_id = 'proof');

-- Allow updates to proof files
CREATE POLICY "Allow updating proof files" ON storage.objects
    FOR UPDATE USING (bucket_id = 'proof');

-- Allow deletes for proof files (for admin cleanup)
CREATE POLICY "Allow deleting proof files" ON storage.objects
    FOR DELETE USING (bucket_id = 'proof');

-- =====================================================
-- 3. ENSURE PROOF BUCKET EXISTS
-- =====================================================

-- Create proof bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, owner, public, file_size_limit, allowed_mime_types)
VALUES (
    'proof',
    'proof',
    NULL,
    false, -- Keep private for security
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf']
)
ON CONFLICT (id) DO UPDATE SET
    file_size_limit = 10485760,
    allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf'];

-- =====================================================
-- 4. SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Storage policies simplified successfully!';
    RAISE NOTICE '📁 Proof bucket: All operations now allowed';
    RAISE NOTICE '🔒 Service role has full access';
    RAISE NOTICE '🚀 File upload should now work!';
END $$;
