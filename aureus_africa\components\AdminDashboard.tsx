import React, { useState, useEffect } from 'react'
import { signOut, getCurrentUser, getSiteContent } from '../lib/supabase'
import { checkAdminStatus } from '../lib/adminAuth'
// import { ContentEditor } from './ContentEditor'
// import { MiningDataEditor } from './MiningDataEditor'
import { GalleryManager } from './admin/GalleryManager'
import { DatabaseTest } from './admin/DatabaseTest'
import { DatabaseDebugger } from './admin/DatabaseDebugger'
import { SponsorManager } from './admin/SponsorManager'
import { PaymentManager } from './admin/PaymentManager'
import { UserManager } from './admin/UserManager'
import { AuditLogViewer } from './admin/AuditLogViewer'
import { MarketingMaterialsManager } from './admin/MarketingMaterialsManager'
import CertificateManagement from './admin/CertificateManagement'
import { AdminWalletManager } from './admin/AdminWalletManager'
import { TestUserAuditReport } from './admin/TestUserAuditReport'
import { FundManagementDashboard } from './admin/FundManagementDashboard'
import { DownlineManager } from './admin/DownlineManager'
import { PhaseCommissionTest } from './test/PhaseCommissionTest'
import PhaseCompetitionManager from './admin/PhaseCompetitionManager'

interface AdminDashboardProps {
  onLogout: () => void
  user?: any
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({ onLogout, user: propUser }) => {
  const [activeTab, setActiveTab] = useState('users')
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)
  const [adminVerified, setAdminVerified] = useState(false)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser()

      // 🔒 CRITICAL SECURITY CHECK: Verify admin status
      if (currentUser?.email) {
        console.log('🔍 AdminDashboard: Verifying admin status for:', currentUser.email)
        const adminUser = await checkAdminStatus(currentUser.email)

        if (!adminUser) {
          console.log('❌ AdminDashboard: Access denied - user is not an admin')
          alert('Access denied. You do not have admin privileges.')
          onLogout()
          return
        }

        console.log('✅ AdminDashboard: Admin verified:', adminUser.email, adminUser.role)
        setUser({ ...currentUser, adminUser })
        setAdminVerified(true)
      } else {
        console.log('❌ AdminDashboard: No user found')
        onLogout()
        return
      }
    } catch (error) {
      console.error('Error loading user:', error)
      onLogout()
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  const tabs = [
    { id: 'users', name: 'User Management', icon: '👥' },
    { id: 'payments', name: 'Payment Management', icon: '💰' },
    { id: 'funds', name: 'Fund Management', icon: '💸' },
    { id: 'wallets', name: 'Wallet Management', icon: '🏦' },
    { id: 'certificates', name: 'Certificate Management', icon: '📜' },
    { id: 'sponsors', name: 'Sponsor Management', icon: '🤝' },
    { id: 'downline', name: 'Downline Management', icon: '🌳' },
    { id: 'competitions', name: 'Competition Management', icon: '🏆' },
    { id: 'gallery', name: 'Gallery Management', icon: '📸' },
    { id: 'marketing', name: 'Marketing Materials', icon: '📁' },
    { id: 'audit', name: 'Audit Logs', icon: '📋' },
    { id: 'test-audit', name: 'Test User Audit', icon: '🔍' },
    { id: 'phase-test', name: 'Phase Commission Test', icon: '🧪' },
    { id: 'debug', name: 'Debug System', icon: '🐛' }
  ]

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Verifying admin credentials...</p>
        </div>
      </div>
    )
  }

  // 🔒 SECURITY: Only render dashboard if admin is verified
  if (!adminVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">🚫</div>
          <h2 className="text-2xl font-bold text-white mb-2">Access Denied</h2>
          <p className="text-gray-400 mb-4">You do not have admin privileges.</p>
          <button
            onClick={onLogout}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            Return to Login
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="admin-dashboard min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <header className="glass-card border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
                AUREUS Admin
              </h1>
              <span className="text-gray-400">|</span>
              <span className="text-gray-300">Content Management System</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-gray-400">Welcome, {user?.email}</span>

              {/* Debug Status Indicator - Coming Soon */}
              <div className="flex items-center gap-2 px-3 py-1 bg-gray-900/30 border border-gray-400/30 rounded-lg">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span className="text-gray-400 text-sm font-medium">Debug System</span>
                <span className="text-gray-300 text-xs">Available</span>
              </div>

              <button
                onClick={handleLogout}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Sidebar Navigation */}
          <div className="w-64 flex-shrink-0">
            <nav className="glass-card p-4">
              <h2 className="text-lg font-semibold text-white mb-4">Sections</h2>
              <ul className="space-y-2">
                {tabs.map((tab) => (
                  <li key={tab.id}>
                    <button
                      onClick={() => {
                        console.log('Switching to tab:', tab.id);
                        setActiveTab(tab.id);
                      }}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                          : 'text-gray-300 hover:bg-gray-800/50 hover:text-white'
                      }`}
                    >
                      <span className="text-xl">{tab.icon}</span>
                      <span className="font-medium">{tab.name}</span>
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Main Content Area */}
          <div className="flex-1">
            <div className="glass-card p-6">
              <div className="mb-4 text-xs text-gray-500">DEBUG: activeTab = "{activeTab}"</div>
              {activeTab === 'gallery' ? (
                <GalleryManager />
              ) : activeTab === 'sponsors' ? (
                <SponsorManager currentUser={user} />
              ) : activeTab === 'downline' ? (
                <DownlineManager />
              ) : activeTab === 'competitions' ? (
                <PhaseCompetitionManager />
              ) : activeTab === 'payments' ? (
                <PaymentManager currentUser={user} />
              ) : activeTab === 'funds' ? (
                <FundManagementDashboard />
              ) : activeTab === 'wallets' ? (
                <AdminWalletManager />
              ) : activeTab === 'certificates' ? (
                <CertificateManagement />
              ) : activeTab === 'users' ? (
                <UserManager currentUser={user} />
              ) : activeTab === 'marketing' ? (
                <MarketingMaterialsManager currentUser={user} />
              ) : activeTab === 'audit' ? (
                <AuditLogViewer />
              ) : activeTab === 'test-audit' ? (
                <TestUserAuditReport />
              ) : activeTab === 'phase-test' ? (
                <PhaseCommissionTest />
              ) : activeTab === 'debug' ? (
                <div>
                  <h3 className="text-xl text-amber-400 mb-6">🐛 Debug Panel</h3>
                  <p className="text-gray-400 mb-4">Current active tab: {activeTab}</p>
                  <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
                    <h4 className="text-white text-lg mb-4">Debug Panel is Working!</h4>
                    <p className="text-gray-400 mb-4">If you can see this, the debug tab is functioning correctly.</p>
                    <div className="bg-blue-900/20 border border-blue-800 rounded p-4">
                      <p className="text-blue-400 text-sm">
                        <strong>Next:</strong> Database connection test will be loaded here.
                      </p>
                    </div>
                  </div>
                  <div className="mt-6">
                    <DatabaseTest />
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <h3 className="text-xl text-amber-400 mb-4">📝 Unknown Section</h3>
                  <p className="text-gray-400">Section not found: <span className="text-amber-300">{activeTab}</span></p>
                  <p className="text-gray-400 mt-2">Please select a valid section from the sidebar.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Debug System Placeholder - Will be implemented later
const DebugSystemPlaceholder: React.FC = () => {
  return (
    <div className="text-center py-12">
      <div className="text-gray-400 text-lg mb-4">🐛</div>
      <h2 className="text-xl font-semibold text-white mb-2">Debug System</h2>
      <p className="text-gray-400">Debug system will be available in a future update.</p>
      <p className="text-gray-300 mt-4 text-sm">Features coming soon:</p>
      <ul className="text-gray-400 text-sm mt-2 space-y-1">
        <li>• Error monitoring and reporting</li>
        <li>• Performance tracking</li>
        <li>• Real-time debugging tools</li>
      </ul>
    </div>
  );
};

