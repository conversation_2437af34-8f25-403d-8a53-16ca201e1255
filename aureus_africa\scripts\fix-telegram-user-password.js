import { createClient } from '@supabase/supabase-js'
import crypto from 'crypto'

// Initialize Supabase client with service role key
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Hash password using the same method as the web app
const hashPassword = async (password) => {
  try {
    // Check if crypto.subtle is available
    if (typeof crypto !== 'undefined' && crypto.subtle) {
      const encoder = new TextEncoder()
      const data = encoder.encode(password + 'aureus_salt_2024')
      const hashBuffer = await crypto.subtle.digest('SHA-256', data)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
    } else {
      // Fallback for Node.js environment
      const hash = crypto.createHash('sha256')
      hash.update(password + 'aureus_salt_2024')
      return hash.digest('hex')
    }
  } catch (error) {
    console.error('Error hashing password:', error)
    throw error
  }
}

async function fixTelegramUserPassword() {
  try {
    console.log('🔧 Fixing Telegram user password hash...')
    
    const telegramId = '1270124602'
    const correctPassword = 'Gunst0n5oO!@#'
    
    console.log(`🔍 Looking for user with telegram_id: ${telegramId}`)
    
    // Find the user by telegram_id
    const { data: user, error: findError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()
    
    if (findError) {
      console.error('❌ Error finding user:', findError)
      return
    }
    
    if (!user) {
      console.error('❌ User not found with telegram_id:', telegramId)
      return
    }
    
    console.log('✅ Found user:', {
      id: user.id,
      username: user.username,
      email: user.email,
      current_password_hash: user.password_hash ? user.password_hash.substring(0, 20) + '...' : 'null'
    })
    
    // Generate the correct password hash
    const correctHash = await hashPassword(correctPassword)
    console.log('✅ Generated correct password hash:', correctHash.substring(0, 20) + '...')
    
    // Update the user's password hash
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update({ 
        password_hash: correctHash,
        updated_at: new Date().toISOString()
      })
      .eq('telegram_id', telegramId)
      .select()
      .single()
    
    if (updateError) {
      console.error('❌ Error updating password hash:', updateError)
      return
    }
    
    console.log('✅ Password hash updated successfully!')
    console.log('✅ User can now log in with:')
    console.log(`   Telegram ID: ${telegramId}`)
    console.log(`   Password: ${correctPassword}`)
    
  } catch (error) {
    console.error('❌ Script error:', error)
  }
}

// Run the fix
fixTelegramUserPassword()
