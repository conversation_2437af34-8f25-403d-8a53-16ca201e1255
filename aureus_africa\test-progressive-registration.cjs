const { createClient } = require('@supabase/supabase-js')

// Test progressive registration with existing email
async function testProgressiveRegistration() {
  console.log('🧪 Testing progressive registration with potentially existing email...')
  
  const testEmail = '<EMAIL>'
  const testUsername = 'testprogressive123'
  
  try {
    // Test the registration API endpoint
    const response = await fetch('http://localhost:8002/api/register-progressive', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
        username: testUsername,
        sponsorUsername: 'TTTFOUNDER',
        campaignSource: 'test'
      }),
    })

    const result = await response.json()
    
    console.log('📊 Registration result:', {
      status: response.status,
      success: result.success,
      error: result.error,
      user: result.user ? { id: result.user.id, email: result.user.email } : null
    })

    if (result.success) {
      console.log('✅ Progressive registration successful!')
    } else {
      console.log('❌ Progressive registration failed:', result.error)
    }

  } catch (error) {
    console.error('❌ Test error:', error.message)
  }
}

// Run the test
testProgressiveRegistration()
