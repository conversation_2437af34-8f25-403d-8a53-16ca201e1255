-- Database Upgrades for Aureus Alliance - Working with Existing Tables
-- Execute this SQL in your Supabase SQL editor
--
-- NOTE: This works with the existing Telegram bot database structure
-- Tables that already exist: investment_phases, aureus_share_purchases, referrals, users, etc.

-- =====================================================
-- 1. ADD MISSING COLUMNS TO EXISTING TABLES
-- =====================================================

-- Add missing columns to crypto_payment_transactions table
ALTER TABLE public.crypto_payment_transactions
ADD COLUMN IF NOT EXISTS shares_to_purchase INTEGER DEFAULT 0;

-- Add missing columns to users table for better integration (optional)
ALTER TABLE public.users
ADD COLUMN IF NOT EXISTS first_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS last_name VARCHAR(100);

-- Update existing users to split full_name into first_name and last_name (optional)
UPDATE public.users
SET
    first_name = SPLIT_PART(full_name, ' ', 1),
    last_name = CASE
        WHEN ARRAY_LENGTH(STRING_TO_ARRAY(full_name, ' '), 1) > 1
        THEN SUBSTRING(full_name FROM LENGTH(SPLIT_PART(full_name, ' ', 1)) + 2)
        ELSE ''
    END
WHERE full_name IS NOT NULL AND (first_name IS NULL OR last_name IS NULL);

-- =====================================================
-- 2. CREATE VIEWS FOR BETTER DATA ACCESS
-- =====================================================

-- View for user share holdings summary (using existing tables)
CREATE OR REPLACE VIEW public.user_share_holdings AS
SELECT
    u.id as user_id,
    u.username,
    u.email,
    u.full_name,
    COALESCE(u.first_name, SPLIT_PART(u.full_name, ' ', 1)) as first_name,
    COALESCE(u.last_name, SUBSTRING(u.full_name FROM LENGTH(SPLIT_PART(u.full_name, ' ', 1)) + 2)) as last_name,
    COUNT(asp.id) as total_purchases,
    SUM(asp.shares_purchased) as total_shares,
    SUM(asp.total_amount) as total_invested,
    SUM(asp.commission_used) as total_commission_used,
    MAX(asp.created_at) as last_purchase_date
FROM public.users u
LEFT JOIN public.aureus_share_purchases asp ON u.id = asp.user_id AND asp.status = 'active'
GROUP BY u.id, u.username, u.email, u.full_name, u.first_name, u.last_name;

-- View for investment phase statistics (using existing tables)
CREATE OR REPLACE VIEW public.phase_statistics AS
SELECT
    ip.id,
    ip.phase_name,
    ip.phase_number,
    ip.price_per_share,
    ip.total_shares_available,
    ip.shares_sold,
    COALESCE(COUNT(asp.id), 0) as total_purchases,
    COALESCE(SUM(asp.total_amount), 0) as total_revenue,
    ROUND((ip.shares_sold::NUMERIC / ip.total_shares_available * 100), 2) as completion_percentage,
    ip.is_active,
    ip.start_date,
    ip.end_date
FROM public.investment_phases ip
LEFT JOIN public.aureus_share_purchases asp ON ip.phase_name = asp.package_name AND asp.status = 'active'
GROUP BY ip.id, ip.phase_name, ip.phase_number, ip.price_per_share, ip.total_shares_available, ip.shares_sold, ip.is_active, ip.start_date, ip.end_date
ORDER BY ip.phase_number;

-- =====================================================
-- 3. ENSURE STORAGE BUCKETS EXIST (handled by application code)
-- =====================================================
-- The application will automatically create these buckets:
-- - payment-proofs (for payment proof uploads)
-- - marketing-materials (for admin marketing material uploads)

-- =====================================================
-- 4. GRANT PERMISSIONS FOR VIEWS
-- =====================================================

-- Grant necessary permissions for the views
GRANT SELECT ON public.user_share_holdings TO authenticated;
GRANT SELECT ON public.phase_statistics TO authenticated;

GRANT ALL ON public.user_share_holdings TO service_role;
GRANT ALL ON public.phase_statistics TO service_role;

-- =====================================================
-- 5. SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Database upgrades completed successfully!';
    RAISE NOTICE '📈 Created views: user_share_holdings, phase_statistics';
    RAISE NOTICE '🔧 Added missing columns to crypto_payment_transactions';
    RAISE NOTICE '👥 Added optional first_name/last_name columns to users';
    RAISE NOTICE '🔒 Granted necessary permissions for views';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your existing Telegram bot database is now compatible with the web upgrades!';
    RAISE NOTICE '📊 All features will work with your existing data structure';
END $$;
