import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface TrainingCenterProps {
  userId: number;
}

interface TrainingModule {
  id: string;
  title: string;
  description: string;
  duration: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
  topics: string[];
  completed: boolean;
  progress: number;
}

export const TrainingCenter: React.FC<TrainingCenterProps> = ({ userId }) => {
  const [modules, setModules] = useState<TrainingModule[]>([]);
  const [userProgress, setUserProgress] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTrainingModules();
    loadUserProgress();
  }, [userId]);

  const loadTrainingModules = async () => {
    // For now, use static data. In production, this would come from database
    const trainingModules: TrainingModule[] = [
      {
        id: 'referral-basics',
        title: '🚀 Getting Started with Referral Marketing',
        description: 'Learn the fundamentals of successful referral marketing and how to build your network effectively.',
        duration: '15 min',
        level: 'Beginner',
        topics: [
          'Understanding referral systems',
          'Setting up your first campaign',
          'Building trust with prospects',
          'First steps to success'
        ],
        completed: false,
        progress: 0
      },
      {
        id: 'social-media-mastery',
        title: '📱 Social Media Mastery',
        description: 'Optimize your content for different social platforms and maximize engagement.',
        duration: '25 min',
        level: 'Intermediate',
        topics: [
          'Platform-specific strategies',
          'Content optimization techniques',
          'Engagement tactics that work',
          'Building your personal brand'
        ],
        completed: false,
        progress: 0
      },
      {
        id: 'analytics-optimization',
        title: '📊 Analytics & Optimization',
        description: 'Use data to improve your marketing performance and track your success.',
        duration: '20 min',
        level: 'Advanced',
        topics: [
          'Reading analytics dashboards',
          'A/B testing strategies',
          'Performance optimization',
          'ROI measurement'
        ],
        completed: false,
        progress: 0
      },
      {
        id: 'advanced-targeting',
        title: '🎯 Advanced Targeting Strategies',
        description: 'Identify and reach your ideal audience with precision targeting techniques.',
        duration: '30 min',
        level: 'Expert',
        topics: [
          'Audience research methods',
          'Advanced targeting techniques',
          'Conversion optimization',
          'Scaling successful campaigns'
        ],
        completed: false,
        progress: 0
      },
      {
        id: 'gold-mining-education',
        title: '🏆 Gold Mining Industry Insights',
        description: 'Understand the gold mining industry to better communicate value to prospects.',
        duration: '18 min',
        level: 'Intermediate',
        topics: [
          'Gold mining fundamentals',
          'Market trends and opportunities',
          'Aureus Africa operations',
          'Communicating value effectively'
        ],
        completed: false,
        progress: 0
      },
      {
        id: 'commission-maximization',
        title: '💰 Commission Maximization',
        description: 'Learn strategies to maximize your earnings and build passive income streams.',
        duration: '22 min',
        level: 'Advanced',
        topics: [
          'Commission structure understanding',
          'Building recurring income',
          'Team building strategies',
          'Long-term wealth building'
        ],
        completed: false,
        progress: 0
      }
    ];

    setModules(trainingModules);
  };

  const loadUserProgress = async () => {
    try {
      const { data, error } = await supabase
        .from('marketing_training_progress')
        .select('module_id, progress_percentage, completed')
        .eq('user_id', userId);

      if (error) throw error;

      const progressMap: Record<string, number> = {};
      data?.forEach(item => {
        progressMap[item.module_id] = item.progress_percentage;
      });

      setUserProgress(progressMap);
    } catch (error) {
      console.error('Error loading user progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const startModule = async (moduleId: string) => {
    try {
      // Track module start
      await supabase
        .from('marketing_training_progress')
        .upsert({
          user_id: userId,
          module_id: moduleId,
          progress_percentage: 5,
          started_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      // Update local state
      setUserProgress(prev => ({ ...prev, [moduleId]: 5 }));

      // In a real implementation, this would open the training content
      alert(`Starting module: ${modules.find(m => m.id === moduleId)?.title}`);
    } catch (error) {
      console.error('Error starting module:', error);
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return '#10b981';
      case 'Intermediate': return '#f59e0b';
      case 'Advanced': return '#ef4444';
      case 'Expert': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress === 0) return '#6b7280';
    if (progress < 50) return '#f59e0b';
    if (progress < 100) return '#3b82f6';
    return '#10b981';
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px', color: '#9ca3af' }}>
        <div style={{ fontSize: '24px', marginBottom: '16px' }}>📚</div>
        Loading training modules...
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <h2 style={{ color: 'white', fontSize: '28px', marginBottom: '12px' }}>
          🎓 Training & Resources
        </h2>
        <p style={{ color: '#9ca3af', fontSize: '16px', maxWidth: '600px', margin: '0 auto' }}>
          Master the art of referral marketing with our comprehensive training modules. 
          Track your progress and unlock advanced strategies.
        </p>
      </div>

      {/* Progress Overview */}
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '32px',
        border: '1px solid #4b5563'
      }}>
        <h3 style={{ color: 'white', fontSize: '18px', marginBottom: '16px' }}>
          📈 Your Progress
        </h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '16px' }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#3b82f6', fontSize: '24px', fontWeight: 'bold' }}>
              {Object.values(userProgress).filter(p => p > 0).length}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Modules Started</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#10b981', fontSize: '24px', fontWeight: 'bold' }}>
              {Object.values(userProgress).filter(p => p >= 100).length}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Completed</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#f59e0b', fontSize: '24px', fontWeight: 'bold' }}>
              {Math.round(Object.values(userProgress).reduce((sum, p) => sum + p, 0) / modules.length)}%
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Overall Progress</div>
          </div>
        </div>
      </div>

      {/* Training Modules */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
        gap: '24px'
      }}>
        {modules.map((module) => {
          const progress = userProgress[module.id] || 0;
          const isStarted = progress > 0;
          const isCompleted = progress >= 100;

          return (
            <div key={module.id} style={{
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              borderRadius: '12px',
              padding: '24px',
              border: `1px solid ${isCompleted ? '#10b981' : isStarted ? '#3b82f6' : '#4b5563'}`,
              position: 'relative'
            }}>
              {/* Completion Badge */}
              {isCompleted && (
                <div style={{
                  position: 'absolute',
                  top: '12px',
                  right: '12px',
                  backgroundColor: '#10b981',
                  color: 'white',
                  padding: '4px 8px',
                  borderRadius: '12px',
                  fontSize: '10px',
                  fontWeight: 'bold'
                }}>
                  ✓ COMPLETED
                </div>
              )}

              <h4 style={{ color: '#f3f4f6', fontSize: '18px', fontWeight: '600', marginBottom: '12px' }}>
                {module.title}
              </h4>
              
              <p style={{ color: '#9ca3af', fontSize: '14px', marginBottom: '16px', lineHeight: '1.5' }}>
                {module.description}
              </p>
              
              {/* Module Info */}
              <div style={{ display: 'flex', gap: '12px', marginBottom: '16px', fontSize: '12px' }}>
                <span style={{
                  padding: '6px 12px',
                  backgroundColor: 'rgba(59, 130, 246, 0.2)',
                  border: '1px solid #3b82f6',
                  borderRadius: '6px',
                  color: '#60a5fa'
                }}>
                  ⏱️ {module.duration}
                </span>
                <span style={{
                  padding: '6px 12px',
                  backgroundColor: `rgba(${getLevelColor(module.level)}, 0.2)`,
                  border: `1px solid ${getLevelColor(module.level)}`,
                  borderRadius: '6px',
                  color: getLevelColor(module.level)
                }}>
                  📊 {module.level}
                </span>
              </div>

              {/* Progress Bar */}
              {isStarted && (
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: '6px'
                  }}>
                    <span style={{ color: '#9ca3af', fontSize: '12px' }}>Progress</span>
                    <span style={{ color: getProgressColor(progress), fontSize: '12px', fontWeight: 'bold' }}>
                      {progress}%
                    </span>
                  </div>
                  <div style={{
                    width: '100%',
                    height: '6px',
                    backgroundColor: '#374151',
                    borderRadius: '3px',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      width: `${progress}%`,
                      height: '100%',
                      backgroundColor: getProgressColor(progress),
                      transition: 'width 0.3s ease'
                    }} />
                  </div>
                </div>
              )}

              {/* Topics */}
              <div style={{ marginBottom: '20px' }}>
                <div style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '8px' }}>
                  What you'll learn:
                </div>
                <ul style={{ color: '#d1d5db', fontSize: '13px', margin: 0, paddingLeft: '16px' }}>
                  {module.topics.map((topic, topicIndex) => (
                    <li key={topicIndex} style={{ marginBottom: '4px' }}>{topic}</li>
                  ))}
                </ul>
              </div>

              {/* Action Button */}
              <button 
                onClick={() => startModule(module.id)}
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: isCompleted ? '#10b981' : isStarted ? '#3b82f6' : '#f59e0b',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.opacity = '0.9';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.opacity = '1';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                {isCompleted ? '✓ Review Module' : isStarted ? '▶️ Continue' : '🚀 Start Module'}
              </button>
            </div>
          );
        })}
      </div>

      {/* Quick Tips Section */}
      <div style={{
        marginTop: '40px',
        backgroundColor: 'rgba(55, 65, 81, 0.3)',
        borderRadius: '12px',
        padding: '24px',
        border: '1px solid #4b5563'
      }}>
        <h3 style={{ color: 'white', fontSize: '18px', marginBottom: '16px' }}>
          💡 Quick Tips for Success
        </h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
          {[
            '🎯 Focus on building genuine relationships, not just making sales',
            '📱 Use multiple platforms to reach different audiences',
            '📊 Track your results and optimize based on data',
            '🤝 Always provide value before asking for anything',
            '⏰ Consistency is key - small daily actions compound',
            '🎓 Keep learning and adapting to new strategies'
          ].map((tip, index) => (
            <div key={index} style={{
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              padding: '12px',
              borderRadius: '8px',
              color: '#d1d5db',
              fontSize: '13px',
              border: '1px solid rgba(59, 130, 246, 0.2)'
            }}>
              {tip}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
