import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';
import AdminCertificateCreator from './AdminCertificateCreator';
import { certificateService } from '../../lib/certificateService';

interface Certificate {
  id: string;
  user_id: number;
  purchase_id: string;
  certificate_number: string;
  shares_count: number;
  issue_date: string;
  status: string;
  certificate_data: any;
}

interface Purchase {
  id: string;
  user_id: number;
  package_name: string;
  shares_purchased: number;
  total_amount: number;
  status: string;
  created_at: string;
  user_email?: string;
  user_name?: string;
}

export const CertificateManagement: React.FC = () => {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [pendingPurchases, setPendingPurchases] = useState<Purchase[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [selectedPurchases, setSelectedPurchases] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState<'list' | 'create'>('list');
  const [stats, setStats] = useState({
    total: 0,
    issued: 0,
    revoked: 0,
    totalShares: 0,
    totalValue: 0
  });

  useEffect(() => {
    loadData();
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const certificateStats = await certificateService.getCertificateStats();
      setStats(certificateStats);
    } catch (error) {
      console.error('Error loading certificate stats:', error);
    }
  };

  const loadData = async () => {
    setLoading(true);
    try {
      // Use service role client for admin operations
      const adminClient = getServiceRoleClient();

      // Load existing certificates
      const { data: certsData, error: certsError } = await adminClient
        .from('certificates')
        .select('*')
        .order('created_at', { ascending: false });

      if (certsError) {
        console.error('Error loading certificates:', certsError);
        setCertificates([]);
      } else {
        // Load user details for each certificate
        const certsWithUsers = await Promise.all(
          (certsData || []).map(async (cert) => {
            const { data: userData } = await adminClient
              .from('users')
              .select('email, full_name')
              .eq('id', cert.user_id)
              .single();

            return {
              ...cert,
              user: userData || { email: 'Unknown', full_name: 'Unknown User' }
            };
          })
        );

        setCertificates(certsWithUsers);
      }

      // Load approved purchases without certificates
      console.log('🔄 Loading active share purchases...');
      const { data: purchasesData, error: purchasesError } = await adminClient
        .from('aureus_share_purchases')
        .select('*')
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (purchasesError) {
        console.error('❌ Error loading purchases:', purchasesError);
        setPendingPurchases([]);
      } else {
        console.log('✅ Found', purchasesData?.length || 0, 'active purchases');
        // Filter out purchases that already have certificates
        const existingCertPurchaseIds = new Set(certsData?.map(c => c.purchase_id) || []);
        const purchasesWithoutCerts = (purchasesData || []).filter(
          p => !existingCertPurchaseIds.has(p.id)
        );

        // Load user details for each purchase
        const purchasesWithUsers = await Promise.all(
          purchasesWithoutCerts.map(async (purchase) => {
            const { data: userData } = await adminClient
              .from('users')
              .select('email, full_name')
              .eq('id', purchase.user_id)
              .single();

            return {
              ...purchase,
              user: userData || { email: 'Unknown', full_name: 'Unknown User' }
            };
          })
        );

        setPendingPurchases(purchasesWithUsers);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCertificate = async (purchase: Purchase) => {
    try {
      setCreating(true);
      
      // Call the admin function to create certificate
      const { data, error } = await supabase.rpc('admin_create_certificate', {
        p_user_id: purchase.user_id,
        p_purchase_id: purchase.id,
        p_shares_count: purchase.shares_purchased,
        p_certificate_data: {
          package_name: purchase.package_name,
          total_amount: purchase.total_amount,
          created_by: 'admin_manual'
        }
      });

      if (error) {
        console.error('Error creating certificate:', error);
        alert(`Error creating certificate: ${error.message}`);
      } else {
        alert(`Certificate created successfully: ${data}`);
        await loadData(); // Refresh data
      }
    } catch (error) {
      console.error('Error creating certificate:', error);
      alert('Error creating certificate');
    } finally {
      setCreating(false);
    }
  };

  const createBatchCertificates = async () => {
    if (selectedPurchases.size === 0) {
      alert('Please select purchases to create certificates for');
      return;
    }

    try {
      setCreating(true);
      let successCount = 0;
      let errorCount = 0;

      for (const purchaseId of selectedPurchases) {
        const purchase = pendingPurchases.find(p => p.id === purchaseId);
        if (!purchase) continue;

        const { data, error } = await supabase.rpc('admin_create_certificate', {
          p_user_id: purchase.user_id,
          p_purchase_id: purchase.id,
          p_shares_count: purchase.shares_purchased,
          p_certificate_data: {
            package_name: purchase.package_name,
            total_amount: purchase.total_amount,
            created_by: 'admin_batch'
          }
        });

        if (error) {
          console.error(`Error creating certificate for purchase ${purchaseId}:`, error);
          errorCount++;
        } else {
          successCount++;
        }
      }

      alert(`Batch creation complete: ${successCount} certificates created, ${errorCount} errors`);
      setSelectedPurchases(new Set());
      await loadData();
    } catch (error) {
      console.error('Error in batch creation:', error);
      alert('Error in batch certificate creation');
    } finally {
      setCreating(false);
    }
  };

  const togglePurchaseSelection = (purchaseId: string) => {
    const newSelection = new Set(selectedPurchases);
    if (newSelection.has(purchaseId)) {
      newSelection.delete(purchaseId);
    } else {
      newSelection.add(purchaseId);
    }
    setSelectedPurchases(newSelection);
  };

  const selectAllPurchases = () => {
    if (selectedPurchases.size === pendingPurchases.length) {
      setSelectedPurchases(new Set());
    } else {
      setSelectedPurchases(new Set(pendingPurchases.map(p => p.id)));
    }
  };

  const downloadCertificate = async (certificate: Certificate) => {
    try {
      const fileUrl = certificate.certificate_data?.file_url;
      if (!fileUrl) {
        alert('Certificate file not found');
        return;
      }

      // Fetch the certificate file
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch certificate');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = `${certificate.certificate_number}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading certificate:', error);
      alert('Failed to download certificate');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-white">Loading certificate data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">📜 Certificate Management</h2>
          <p className="text-gray-400">Generate and manage professional share certificates</p>
        </div>

        {/* Certificate Stats */}
        <div className="grid grid-cols-4 gap-4 text-center">
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <p className="text-2xl font-bold text-blue-400">{stats.total}</p>
            <p className="text-xs text-gray-400">Total Certificates</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <p className="text-2xl font-bold text-green-400">{stats.issued}</p>
            <p className="text-xs text-gray-400">Issued</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <p className="text-2xl font-bold text-yellow-400">{stats.totalShares.toLocaleString()}</p>
            <p className="text-xs text-gray-400">Total Shares</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <p className="text-2xl font-bold text-purple-400">${stats.totalValue.toLocaleString()}</p>
            <p className="text-xs text-gray-400">Total Value</p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('list')}
          className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'list'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          📋 Certificate List
        </button>
        <button
          onClick={() => setActiveTab('create')}
          className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'create'
              ? 'bg-blue-600 text-white'
              : 'text-gray-400 hover:text-white hover:bg-gray-700'
          }`}
        >
          ⚡ Instant Generator
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'create' ? (
        <AdminCertificateCreator />
      ) : (
        <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-700">
            <div className="text-2xl font-bold text-blue-400">{certificates.length}</div>
            <div className="text-blue-200">Total Certificates</div>
          </div>
          <div className="bg-yellow-900/30 rounded-lg p-4 border border-yellow-700">
            <div className="text-2xl font-bold text-yellow-400">{pendingPurchases.length}</div>
            <div className="text-yellow-200">Pending Certificates</div>
          </div>
          <div className="bg-green-900/30 rounded-lg p-4 border border-green-700">
            <div className="text-2xl font-bold text-green-400">
              {certificates.reduce((sum, cert) => sum + cert.shares_count, 0)}
            </div>
            <div className="text-green-200">Total Certified Shares</div>
          </div>
        </div>
      </div>

      {/* Pending Purchases - Need Certificates */}
      {pendingPurchases.length > 0 && (
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-white">⏳ Approved Purchases Needing Certificates</h3>
            <div className="space-x-2">
              <button
                onClick={selectAllPurchases}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                disabled={creating}
              >
                {selectedPurchases.size === pendingPurchases.length ? 'Deselect All' : 'Select All'}
              </button>
              <button
                onClick={createBatchCertificates}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                disabled={creating || selectedPurchases.size === 0}
              >
                {creating ? 'Creating...' : `Create ${selectedPurchases.size} Certificates`}
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left p-2 text-gray-400">Select</th>
                  <th className="text-left p-2 text-gray-400">User</th>
                  <th className="text-left p-2 text-gray-400">Package</th>
                  <th className="text-left p-2 text-gray-400">Shares</th>
                  <th className="text-left p-2 text-gray-400">Amount</th>
                  <th className="text-left p-2 text-gray-400">Date</th>
                  <th className="text-left p-2 text-gray-400">Action</th>
                </tr>
              </thead>
              <tbody>
                {pendingPurchases.map((purchase) => (
                  <tr key={purchase.id} className="border-b border-gray-700/50">
                    <td className="p-2">
                      <input
                        type="checkbox"
                        checked={selectedPurchases.has(purchase.id)}
                        onChange={() => togglePurchaseSelection(purchase.id)}
                        className="rounded"
                      />
                    </td>
                    <td className="p-2 text-white">
                      <div>{purchase.user?.full_name || 'N/A'}</div>
                      <div className="text-xs text-gray-400">{purchase.user?.email}</div>
                    </td>
                    <td className="p-2 text-white">{purchase.package_name}</td>
                    <td className="p-2 text-white">{purchase.shares_purchased.toLocaleString()}</td>
                    <td className="p-2 text-white">${purchase.total_amount.toLocaleString()}</td>
                    <td className="p-2 text-gray-400">
                      {new Date(purchase.created_at).toLocaleDateString()}
                    </td>
                    <td className="p-2">
                      <button
                        onClick={() => createCertificate(purchase)}
                        className="px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 disabled:opacity-50"
                        disabled={creating}
                      >
                        {creating ? 'Creating...' : 'Create Certificate'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

        {/* Existing Certificates */}
        <div className="mt-6">
          <h3 className="text-xl font-semibold text-white mb-4">📋 Issued Certificates</h3>
        
        {certificates.length === 0 ? (
          <p className="text-gray-400">No certificates issued yet.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left p-2 text-gray-400">Certificate #</th>
                  <th className="text-left p-2 text-gray-400">User</th>
                  <th className="text-left p-2 text-gray-400">Shares</th>
                  <th className="text-left p-2 text-gray-400">Issue Date</th>
                  <th className="text-left p-2 text-gray-400">Status</th>
                  <th className="text-left p-2 text-gray-400">Actions</th>
                </tr>
              </thead>
              <tbody>
                {certificates.map((certificate) => (
                  <tr key={certificate.id} className="border-b border-gray-700/50">
                    <td className="p-2 text-white font-mono">{certificate.certificate_number}</td>
                    <td className="p-2 text-white">
                      <div>{certificate.user?.full_name || 'N/A'}</div>
                      <div className="text-xs text-gray-400">{certificate.user?.email}</div>
                    </td>
                    <td className="p-2 text-white">{certificate.shares_count.toLocaleString()}</td>
                    <td className="p-2 text-gray-400">
                      {new Date(certificate.issue_date).toLocaleDateString()}
                    </td>
                    <td className="p-2">
                      <span className={`px-2 py-1 rounded text-xs ${
                        certificate.status === 'issued'
                          ? 'bg-green-900/30 text-green-400 border border-green-700'
                          : 'bg-gray-900/30 text-gray-400 border border-gray-700'
                      }`}>
                        {certificate.status}
                      </span>
                    </td>
                    <td className="p-2">
                      {certificate.certificate_data?.file_url && (
                        <button
                          onClick={() => downloadCertificate(certificate)}
                          className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                        >
                          📥 Download
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        </div>
        </div>
      )}
    </div>
  );
};

export default CertificateManagement;
