#!/usr/bin/env node

/**
 * FIX TELEGRAM PASSWORD ISSUE
 * 
 * This script fixes the Telegram login password issue by:
 * 1. Adding password_hash column to telegram_users table
 * 2. Setting the password for the specific user having issues
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

const fixTelegramPasswordIssue = async () => {
  try {
    console.log('🔧 Fixing Telegram password issue...\n');

    // Step 1: Check if password_hash column exists
    console.log('📋 Step 1: Checking if password_hash column exists...');

    const { error: checkError } = await supabase
      .from('telegram_users')
      .select('password_hash')
      .limit(1);

    if (checkError && checkError.message.includes('column "password_hash" does not exist')) {
      console.log('❌ password_hash column does not exist in telegram_users table');
      console.log('\n🔧 MANUAL ACTION REQUIRED:');
      console.log('   1. Go to: https://supabase.com/dashboard/project/fgubaqoftdeefcakejwu/editor');
      console.log('   2. Navigate to: telegram_users table');
      console.log('   3. Click "Add Column" button');
      console.log('   4. Add column with these settings:');
      console.log('      - Name: password_hash');
      console.log('      - Type: varchar');
      console.log('      - Length: 255');
      console.log('      - Nullable: Yes (checked)');
      console.log('      - Default: Leave empty');
      console.log('   5. Click "Save"');
      console.log('   6. Run this script again');
      console.log('\n⏸️ Script paused. Please add the column and run again.');
      return;
    } else if (checkError) {
      console.error('❌ Unexpected error checking column:', checkError);
      return;
    } else {
      console.log('✅ password_hash column exists');
    }

    // Step 2: Hash the password that was set by admin
    const newPassword = 'Gunst0n5o0!@#';
    console.log(`\n📋 Step 2: Hashing password: "${newPassword}"`);
    const hashedPassword = await hashPassword(newPassword);
    console.log('✅ Password hashed successfully');

    // Step 3: Update the specific Telegram user
    const telegramId = '1393852532';
    console.log(`\n📋 Step 3: Updating Telegram user ${telegramId}...`);

    const { data: updateData, error: updateError } = await supabase
      .from('telegram_users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('telegram_id', telegramId)
      .select();

    if (updateError) {
      console.error('❌ Error updating telegram user:', updateError);
      return;
    }

    if (!updateData || updateData.length === 0) {
      console.error('❌ No telegram user found with ID:', telegramId);
      return;
    }

    console.log('✅ Telegram user updated successfully');

    // Step 4: Verify the fix
    console.log('\n📋 Step 4: Verifying the fix...');
    
    const { data: verifyData, error: verifyError } = await supabase
      .from('telegram_users')
      .select('telegram_id, username, first_name, password_hash')
      .eq('telegram_id', telegramId)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying fix:', verifyError);
      return;
    }

    console.log('📋 Updated Telegram User:');
    console.log(`   Telegram ID: ${verifyData.telegram_id}`);
    console.log(`   Username: ${verifyData.username}`);
    console.log(`   First Name: ${verifyData.first_name}`);
    console.log(`   Password Hash: ${verifyData.password_hash ? 'SET ✅' : 'NOT SET ❌'}`);

    if (verifyData.password_hash) {
      // Test password verification
      const isValid = await bcrypt.compare(newPassword, verifyData.password_hash);
      console.log(`   Password Verification: ${isValid ? '✅ SUCCESS' : '❌ FAILED'}`);
    }

    console.log('\n🎉 Fix completed successfully!');
    console.log('The user should now be able to login with the password set by admin.');

  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
};

fixTelegramPasswordIssue();
