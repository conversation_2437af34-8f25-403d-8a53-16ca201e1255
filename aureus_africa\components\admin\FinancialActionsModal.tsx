import React, { useState } from 'react'
import { supabase } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'

interface User {
  id: number
  username: string
  full_name: string | null
  email: string
  commission_balances?: {
    usdt_balance: number
    share_balance: number
    total_earned_usdt: number
    total_earned_shares: number
    total_withdrawn_usdt: number
  }
  payment_transactions?: Array<{
    id: string
    amount: number
    currency: string
    network: string
    status: string
    created_at: string
  }>
}

interface FinancialActionsModalProps {
  isOpen: boolean
  onClose: () => void
  user: User
  adminUser?: any
  onUpdate: () => void
}

export const FinancialActionsModal: React.FC<FinancialActionsModalProps> = ({
  isOpen,
  onClose,
  user,
  adminUser,
  onUpdate
}) => {
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'commission' | 'payments' | 'shares'>('commission')
  
  // Commission adjustment states
  const [usdtAdjustment, setUsdtAdjustment] = useState('')
  const [shareAdjustment, setShareAdjustment] = useState('')
  const [adjustmentReason, setAdjustmentReason] = useState('')
  
  // Payment management states
  const [selectedPayment, setSelectedPayment] = useState<string>('')
  const [paymentAction, setPaymentAction] = useState<'approve' | 'reject'>('approve')
  const [paymentNotes, setPaymentNotes] = useState('')

  if (!isOpen) return null

  const handleCommissionAdjustment = async () => {
    if (!usdtAdjustment && !shareAdjustment) {
      alert('Please enter an adjustment amount')
      return
    }

    if (!adjustmentReason.trim()) {
      alert('Please provide a reason for the adjustment')
      return
    }

    setLoading(true)
    try {
      const currentBalance = user.commission_balances || {
        usdt_balance: 0,
        share_balance: 0,
        total_earned_usdt: 0,
        total_earned_shares: 0,
        total_withdrawn_usdt: 0
      }

      const newUsdtBalance = currentBalance.usdt_balance + (parseFloat(usdtAdjustment) || 0)
      const newShareBalance = currentBalance.share_balance + (parseFloat(shareAdjustment) || 0)

      // Update commission balances
      const { error } = await supabase
        .from('commission_balances')
        .upsert({
          user_id: user.id,
          usdt_balance: Math.max(0, newUsdtBalance),
          share_balance: Math.max(0, newShareBalance),
          total_earned_usdt: currentBalance.total_earned_usdt + Math.max(0, parseFloat(usdtAdjustment) || 0),
          total_earned_shares: currentBalance.total_earned_shares + Math.max(0, parseFloat(shareAdjustment) || 0),
          total_withdrawn_usdt: currentBalance.total_withdrawn_usdt,
          last_updated: new Date().toISOString()
        })

      if (error) throw error

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        'ADJUST_COMMISSION',
        'commission_balances',
        user.id.toString(),
        {
          usdt_adjustment: usdtAdjustment,
          share_adjustment: shareAdjustment,
          reason: adjustmentReason,
          previous_usdt: currentBalance.usdt_balance,
          previous_shares: currentBalance.share_balance,
          new_usdt: newUsdtBalance,
          new_shares: newShareBalance
        }
      )

      alert('Commission balance adjusted successfully')
      setUsdtAdjustment('')
      setShareAdjustment('')
      setAdjustmentReason('')
      onUpdate()
    } catch (err: any) {
      console.error('Error adjusting commission:', err)
      alert('Failed to adjust commission: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentAction = async () => {
    if (!selectedPayment) {
      alert('Please select a payment to manage')
      return
    }

    setLoading(true)
    try {
      // IMPORTANT: Use 'approved' status to trigger commission processing
      const newStatus = paymentAction === 'approve' ? 'approved' : 'rejected'

      const { error } = await supabase
        .from('crypto_payment_transactions')
        .update({
          status: newStatus,
          admin_notes: paymentNotes,
          approved_at: paymentAction === 'approve' ? new Date().toISOString() : null,
          approved_by_admin_id: paymentAction === 'approve' ? adminUser?.id : null,
          verification_status: paymentAction === 'approve' ? 'verified' : 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedPayment)

      if (error) throw error

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        paymentAction === 'approve' ? 'APPROVE_PAYMENT' : 'REJECT_PAYMENT',
        'crypto_payment_transactions',
        selectedPayment,
        {
          user_id: user.id,
          action: paymentAction,
          notes: paymentNotes,
          new_status: newStatus
        }
      )

      alert(`Payment ${paymentAction}d successfully`)
      setSelectedPayment('')
      setPaymentAction('approve')
      setPaymentNotes('')
      onUpdate()
    } catch (err: any) {
      console.error('Error managing payment:', err)
      alert('Failed to manage payment: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-white">
              ⚙️ Financial Management - {user.full_name || user.username}
            </h2>
            <p className="text-gray-400 mt-1">@{user.username} (ID: {user.id})</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700">
          <button
            onClick={() => setActiveTab('commission')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'commission'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            💰 Commission Management
          </button>
          <button
            onClick={() => setActiveTab('payments')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'payments'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            💳 Payment Management
          </button>
          <button
            onClick={() => setActiveTab('shares')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'shares'
                ? 'text-yellow-400 border-b-2 border-yellow-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            📈 Share Management
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Commission Management Tab */}
          {activeTab === 'commission' && (
            <div className="space-y-6">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Current Commission Balances</h3>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center">
                    <p className="text-sm text-gray-400">USDT Balance</p>
                    <p className="text-2xl font-bold text-blue-400">
                      ${(user.commission_balances?.usdt_balance || 0).toLocaleString()}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-400">Share Balance</p>
                    <p className="text-2xl font-bold text-yellow-400">
                      {(user.commission_balances?.share_balance || 0).toLocaleString()}
                    </p>
                  </div>
                </div>

                <h4 className="text-md font-semibold text-white mb-4">Adjust Balances</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      USDT Adjustment (+ or -)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={usdtAdjustment}
                      onChange={(e) => setUsdtAdjustment(e.target.value)}
                      placeholder="e.g., +100 or -50"
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Share Adjustment (+ or -)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={shareAdjustment}
                      onChange={(e) => setShareAdjustment(e.target.value)}
                      placeholder="e.g., +10 or -5"
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                    />
                  </div>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Reason for Adjustment *
                  </label>
                  <textarea
                    value={adjustmentReason}
                    onChange={(e) => setAdjustmentReason(e.target.value)}
                    placeholder="Explain why this adjustment is being made..."
                    rows={3}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                  />
                </div>
                <button
                  onClick={handleCommissionAdjustment}
                  disabled={loading}
                  className="w-full bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg disabled:opacity-50"
                >
                  {loading ? 'Processing...' : 'Apply Commission Adjustment'}
                </button>
              </div>
            </div>
          )}

          {/* Payment Management Tab */}
          {activeTab === 'payments' && (
            <div className="space-y-6">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Manage Payments</h3>
                
                {user.payment_transactions && user.payment_transactions.length > 0 ? (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Select Payment to Manage
                      </label>
                      <select
                        value={selectedPayment}
                        onChange={(e) => setSelectedPayment(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                      >
                        <option value="">Select a payment...</option>
                        {user.payment_transactions.map((payment) => (
                          <option key={payment.id} value={payment.id}>
                            ${payment.amount} {payment.currency} - {payment.network} - {payment.status} - {new Date(payment.created_at).toLocaleDateString()}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Action
                        </label>
                        <select
                          value={paymentAction}
                          onChange={(e) => setPaymentAction(e.target.value as 'approve' | 'reject')}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                        >
                          <option value="approve">Approve Payment</option>
                          <option value="reject">Reject Payment</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Admin Notes
                      </label>
                      <textarea
                        value={paymentNotes}
                        onChange={(e) => setPaymentNotes(e.target.value)}
                        placeholder="Add notes about this payment action..."
                        rows={3}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                      />
                    </div>

                    <button
                      onClick={handlePaymentAction}
                      disabled={loading || !selectedPayment}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg disabled:opacity-50"
                    >
                      {loading ? 'Processing...' : `${paymentAction === 'approve' ? 'Approve' : 'Reject'} Payment`}
                    </button>
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No payment transactions found for this user</p>
                )}
              </div>
            </div>
          )}

          {/* Share Management Tab */}
          {activeTab === 'shares' && (
            <div className="space-y-6">
              <div className="glass-card p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Share Management</h3>
                <p className="text-gray-400 text-center py-8">
                  Share management features coming soon...
                  <br />
                  This will include manual share allocation and investment status management.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
