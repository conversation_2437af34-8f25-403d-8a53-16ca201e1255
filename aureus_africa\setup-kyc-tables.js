const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql, description) {
  console.log(`🔄 ${description}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ ${description} failed:`, error.message);
      return false;
    } else {
      console.log(`✅ ${description} completed`);
      return true;
    }
  } catch (err) {
    console.error(`❌ ${description} error:`, err.message);
    return false;
  }
}

async function setupKYCTables() {
  console.log('🚀 Setting up KYC tables...');
  
  // Step 1: Create kyc_information table
  const createKYCTableSQL = `
    CREATE TABLE IF NOT EXISTS kyc_information (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      
      -- Personal Information
      first_name VARCHAR(100) NOT NULL,
      last_name VARCHAR(100) NOT NULL,
      full_legal_name VARCHAR(200) GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
      
      -- Government ID (encrypted)
      id_type VARCHAR(20) NOT NULL CHECK (id_type IN ('national_id', 'passport', 'drivers_license')),
      id_number_encrypted TEXT NOT NULL,
      id_number_hash VARCHAR(64) NOT NULL,
      
      -- Contact Information
      phone_number VARCHAR(20) NOT NULL,
      email_address VARCHAR(255) NOT NULL,
      
      -- Address Information
      street_address TEXT NOT NULL,
      city VARCHAR(100) NOT NULL,
      postal_code VARCHAR(20) NOT NULL,
      country_code VARCHAR(3) NOT NULL,
      country_name VARCHAR(100) NOT NULL,
      
      -- Compliance and Audit
      data_consent_given BOOLEAN NOT NULL DEFAULT FALSE,
      privacy_policy_accepted BOOLEAN NOT NULL DEFAULT FALSE,
      kyc_completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      kyc_status VARCHAR(20) NOT NULL DEFAULT 'completed' CHECK (kyc_status IN ('pending', 'completed', 'rejected', 'expired')),
      
      -- Certificate tracking
      certificate_requested BOOLEAN DEFAULT FALSE,
      certificate_generated_at TIMESTAMP WITH TIME ZONE,
      certificate_sent_at TIMESTAMP WITH TIME ZONE,
      
      -- Audit Trail
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      created_by_telegram_id BIGINT,
      last_modified_by VARCHAR(100),
      
      -- Constraints
      UNIQUE(user_id),
      UNIQUE(id_number_hash)
    );
  `;
  
  if (!await executeSQL(createKYCTableSQL, 'Creating kyc_information table')) {
    return false;
  }
  
  // Step 2: Create indexes
  const indexesSQL = `
    CREATE INDEX IF NOT EXISTS idx_kyc_information_user_id ON kyc_information(user_id);
    CREATE INDEX IF NOT EXISTS idx_kyc_information_kyc_status ON kyc_information(kyc_status);
    CREATE INDEX IF NOT EXISTS idx_kyc_information_id_number_hash ON kyc_information(id_number_hash);
    CREATE INDEX IF NOT EXISTS idx_kyc_information_created_at ON kyc_information(created_at);
  `;
  
  if (!await executeSQL(indexesSQL, 'Creating KYC indexes')) {
    return false;
  }
  
  // Step 3: Create audit log table (optional)
  const auditTableSQL = `
    CREATE TABLE IF NOT EXISTS kyc_audit_log (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      kyc_id UUID REFERENCES kyc_information(id) ON DELETE CASCADE,
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      
      -- Audit Information
      action VARCHAR(50) NOT NULL,
      field_changed VARCHAR(100),
      old_value_hash VARCHAR(64),
      new_value_hash VARCHAR(64),
      
      -- Context
      performed_by_telegram_id BIGINT,
      performed_by_username VARCHAR(255),
      ip_address VARCHAR(45),
      user_agent TEXT,
      performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  await executeSQL(auditTableSQL, 'Creating kyc_audit_log table (optional)');
  
  // Step 4: Create helper functions
  const functionsSQL = `
    -- Function to check KYC completion
    CREATE OR REPLACE FUNCTION check_kyc_completion(p_user_id INTEGER)
    RETURNS BOOLEAN AS $$
    BEGIN
      RETURN EXISTS (
        SELECT 1 FROM kyc_information 
        WHERE user_id = p_user_id 
        AND kyc_status = 'completed'
      );
    END;
    $$ LANGUAGE plpgsql;
    
    -- Function to hash ID numbers
    CREATE OR REPLACE FUNCTION hash_id_number(p_id_number TEXT)
    RETURNS VARCHAR(64) AS $$
    BEGIN
      RETURN encode(digest(p_id_number, 'sha256'), 'hex');
    END;
    $$ LANGUAGE plpgsql;
    
    -- Function to log KYC actions (optional)
    CREATE OR REPLACE FUNCTION log_kyc_action(
      p_kyc_id UUID,
      p_user_id INTEGER,
      p_action VARCHAR(50),
      p_performed_by_username VARCHAR(255) DEFAULT NULL
    )
    RETURNS UUID AS $$
    DECLARE
      log_id UUID;
    BEGIN
      INSERT INTO kyc_audit_log (
        kyc_id,
        user_id,
        action,
        performed_by_username
      ) VALUES (
        p_kyc_id,
        p_user_id,
        p_action,
        p_performed_by_username
      ) RETURNING id INTO log_id;
      
      RETURN log_id;
    EXCEPTION
      WHEN OTHERS THEN
        -- If audit table doesn't exist, just return a dummy UUID
        RETURN gen_random_uuid();
    END;
    $$ LANGUAGE plpgsql;
  `;
  
  if (!await executeSQL(functionsSQL, 'Creating KYC helper functions')) {
    return false;
  }
  
  // Step 5: Ensure proof bucket exists for document uploads
  console.log('🗂️ Checking proof storage bucket...');

  try {
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

    if (!bucketsError) {
      const proofBucket = buckets.find(bucket => bucket.name === 'proof');
      if (!proofBucket) {
        console.log('📦 Creating proof bucket for KYC documents...');
        const { error: createError } = await supabase.storage.createBucket('proof', {
          public: false, // Keep KYC documents private
          allowedMimeTypes: [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'application/pdf'
          ],
          fileSizeLimit: 10 * 1024 * 1024 // 10MB
        });

        if (createError) {
          console.error('❌ Failed to create proof bucket:', createError.message);
        } else {
          console.log('✅ Proof bucket created successfully');
        }
      } else {
        console.log('✅ Proof bucket already exists');
      }
    }
  } catch (bucketError) {
    console.error('⚠️ Could not check/create proof bucket:', bucketError.message);
    console.log('ℹ️ You may need to create the "proof" bucket manually in Supabase Storage');
  }

  // Step 6: Test the setup
  console.log('🧪 Testing KYC table setup...');
  
  try {
    // Test table access
    const { data: testData, error: testError } = await supabase
      .from('kyc_information')
      .select('count(*)')
      .limit(1);
    
    if (testError) {
      console.error('❌ KYC table access test failed:', testError.message);
      return false;
    }
    
    console.log('✅ KYC table is accessible');
    
    // Test function
    const { data: funcTest, error: funcError } = await supabase
      .rpc('check_kyc_completion', { p_user_id: 999 });
    
    if (funcError) {
      console.error('❌ KYC function test failed:', funcError.message);
      return false;
    }
    
    console.log('✅ KYC functions working, test result:', funcTest);
    
    return true;
    
  } catch (error) {
    console.error('❌ KYC testing failed:', error.message);
    return false;
  }
}

// Test with sample data
async function testKYCWithSampleData() {
  console.log('🧪 Testing KYC with sample data...');
  
  try {
    // Check if we have any users to test with
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(1);
    
    if (usersError || !users || users.length === 0) {
      console.log('ℹ️ No users found for KYC testing');
      return;
    }
    
    const testUser = users[0];
    console.log(`📋 Testing KYC for user ${testUser.id} (${testUser.email})`);
    
    // Check if user already has KYC
    const { data: existingKYC } = await supabase
      .from('kyc_information')
      .select('id, kyc_status')
      .eq('user_id', testUser.id)
      .single();
    
    if (existingKYC) {
      console.log(`ℹ️ User already has KYC: ${existingKYC.kyc_status}`);
      return;
    }
    
    console.log('✅ KYC system ready for user submissions');
    
  } catch (error) {
    console.error('❌ KYC sample data test failed:', error.message);
  }
}

// Main execution
if (require.main === module) {
  setupKYCTables()
    .then((success) => {
      if (success) {
        console.log('✅ KYC tables setup completed successfully!');
        return testKYCWithSampleData();
      } else {
        console.error('❌ KYC tables setup failed');
        process.exit(1);
      }
    })
    .then(() => {
      console.log('🎉 KYC system setup and tests completed!');
      console.log('');
      console.log('📋 Next steps:');
      console.log('1. Users can now complete KYC verification in the portfolio');
      console.log('2. KYC data will be stored in the kyc_information table');
      console.log('3. Certificate access will be controlled by KYC status');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ KYC setup process failed:', error);
      process.exit(1);
    });
}

module.exports = { setupKYCTables };
