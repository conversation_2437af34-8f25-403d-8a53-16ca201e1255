import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 8002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('dist'));

// Dynamically load and register API routes
const apiDir = path.join(__dirname, 'api');
if (fs.existsSync(apiDir)) {
  const apiFiles = fs.readdirSync(apiDir).filter(file => file.endsWith('.js'));
  
  for (const file of apiFiles) {
    const routeName = file.replace('.js', '');
    const routePath = `/api/${routeName}`;
    
    try {
      const { default: handler } = await import(`./api/${file}`);
      
      // Create a wrapper to handle the Next.js-style API route
      app.all(routePath, async (req, res) => {
        // Create a mock Next.js-style request/response object
        const mockReq = {
          ...req,
          query: { ...req.query, ...req.params },
          body: req.body
        };
        
        const mockRes = {
          status: (code) => {
            res.status(code);
            return mockRes;
          },
          json: (data) => {
            res.json(data);
            return mockRes;
          },
          end: () => {
            res.end();
            return mockRes;
          },
          setHeader: (name, value) => {
            res.setHeader(name, value);
            return mockRes;
          }
        };
        
        await handler(mockReq, mockRes);
      });
      
      console.log(`✅ Registered API route: ${routePath}`);
    } catch (error) {
      console.error(`❌ Failed to load API route ${routePath}:`, error.message);
    }
  }
}

// Serve React app for all other routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📁 Serving static files from: ${path.join(__dirname, 'dist')}`);
  console.log(`🔌 API routes available at: http://localhost:${PORT}/api/*`);
});
