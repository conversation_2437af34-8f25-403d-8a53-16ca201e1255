-- Gold Diggers Club Competition System
-- Dynamic competition and leaderboard management
--
-- IMPORTANT: This system rewards NETWORK LEADERS based on referral sales performance
-- Rankings are based on total USD volume of shares sold through their referral network
-- NOT based on their personal investment amounts

-- 1. Competitions Table
-- Manages different competition phases/periods
CREATE TABLE IF NOT EXISTS competitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    phase_id INTEGER REFERENCES investment_phases(id) ON DELETE CASCADE,
    
    -- Competition timing
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Competition rules
    minimum_qualification_amount DECIMAL(15,2) DEFAULT 2500.00,
    total_prize_pool DECIMAL(15,2) NOT NULL,
    max_participants INTEGER,
    
    -- Competition status
    status VARCHAR(50) DEFAULT 'upcoming', -- upcoming, active, ended, cancelled
    total_participants INTEGER DEFAULT 0,
    qualified_participants INTEGER DEFAULT 0,
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by INTEGER REFERENCES users(id)
);

-- 2. Competition Prize Tiers Table
-- Defines prize distribution for each competition
CREATE TABLE IF NOT EXISTS competition_prize_tiers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    competition_id UUID NOT NULL REFERENCES competitions(id) ON DELETE CASCADE,
    
    -- Prize tier details
    tier_name VARCHAR(100) NOT NULL, -- "1st Place", "2nd Place", "4th-10th Place"
    tier_rank_start INTEGER NOT NULL, -- 1 for 1st place, 4 for 4th-10th
    tier_rank_end INTEGER NOT NULL,   -- 1 for 1st place, 10 for 4th-10th
    
    -- Prize amounts
    prize_amount DECIMAL(15,2) NOT NULL,
    prize_type VARCHAR(50) DEFAULT 'usd', -- usd, shares, percentage
    
    -- Display
    display_order INTEGER DEFAULT 1,
    emoji VARCHAR(10) DEFAULT '🏆',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Competition Participants Table
-- Tracks NETWORK LEADERS participating in each competition
-- These are users who have referred others and are competing based on their referral sales
CREATE TABLE IF NOT EXISTS competition_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    competition_id UUID NOT NULL REFERENCES competitions(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- The network leader (referrer)

    -- Network performance metrics (based on referral sales, NOT personal purchases)
    total_referral_volume DECIMAL(15,2) DEFAULT 0.00, -- Total USD volume sold through their network
    direct_referrals_count INTEGER DEFAULT 0, -- Number of people they directly referred
    qualified_referrals_count INTEGER DEFAULT 0, -- Number of referrals who bought >= $2,500
    
    -- Status
    is_qualified BOOLEAN DEFAULT FALSE,
    current_rank INTEGER,
    final_rank INTEGER,
    
    -- Timestamps
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT unique_competition_participant UNIQUE(competition_id, user_id)
);

-- 4. Competition Leaderboard View
-- Real-time leaderboard calculation
CREATE OR REPLACE VIEW competition_leaderboard AS
SELECT 
    cp.competition_id,
    cp.user_id,
    u.username,
    u.full_name,
    cp.total_referral_volume,
    cp.direct_referrals_count,
    cp.qualified_referrals_count,
    cp.is_qualified,
    cp.current_rank,
    cp.joined_at,
    ROW_NUMBER() OVER (
        PARTITION BY cp.competition_id 
        ORDER BY cp.total_referral_volume DESC, cp.joined_at ASC
    ) as calculated_rank
FROM competition_participants cp
JOIN users u ON cp.user_id = u.id
WHERE cp.is_qualified = TRUE;

-- 5. Indexes for performance
CREATE INDEX IF NOT EXISTS idx_competitions_active ON competitions(is_active, status);
CREATE INDEX IF NOT EXISTS idx_competitions_phase ON competitions(phase_id);
CREATE INDEX IF NOT EXISTS idx_competition_participants_competition ON competition_participants(competition_id);
CREATE INDEX IF NOT EXISTS idx_competition_participants_user ON competition_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_competition_participants_qualified ON competition_participants(is_qualified);
CREATE INDEX IF NOT EXISTS idx_competition_participants_volume ON competition_participants(total_referral_volume DESC);
CREATE INDEX IF NOT EXISTS idx_competition_prize_tiers_competition ON competition_prize_tiers(competition_id);

-- 6. Functions for competition management

-- Function to update participant rankings
CREATE OR REPLACE FUNCTION update_competition_rankings(comp_id UUID)
RETURNS void AS $$
BEGIN
    -- Update current_rank for all participants in the competition
    UPDATE competition_participants 
    SET current_rank = ranked.new_rank,
        last_updated = NOW()
    FROM (
        SELECT 
            user_id,
            ROW_NUMBER() OVER (ORDER BY total_referral_volume DESC, joined_at ASC) as new_rank
        FROM competition_participants 
        WHERE competition_id = comp_id AND is_qualified = TRUE
    ) ranked
    WHERE competition_participants.competition_id = comp_id 
    AND competition_participants.user_id = ranked.user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check qualification status
CREATE OR REPLACE FUNCTION check_participant_qualification(comp_id UUID, participant_user_id INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    min_amount DECIMAL(15,2);
    participant_volume DECIMAL(15,2);
BEGIN
    -- Get minimum qualification amount for this competition
    SELECT minimum_qualification_amount INTO min_amount
    FROM competitions WHERE id = comp_id;
    
    -- Get participant's current volume
    SELECT total_referral_volume INTO participant_volume
    FROM competition_participants 
    WHERE competition_id = comp_id AND user_id = participant_user_id;
    
    -- Return qualification status
    RETURN participant_volume >= min_amount;
END;
$$ LANGUAGE plpgsql;

-- 7. Triggers to maintain data consistency

-- Trigger to update qualification status when volume changes
CREATE OR REPLACE FUNCTION update_qualification_status()
RETURNS TRIGGER AS $$
DECLARE
    comp_min_amount DECIMAL(15,2);
BEGIN
    -- Get competition minimum amount
    SELECT minimum_qualification_amount INTO comp_min_amount
    FROM competitions WHERE id = NEW.competition_id;
    
    -- Update qualification status
    NEW.is_qualified = (NEW.total_referral_volume >= comp_min_amount);
    
    -- Update rankings if qualified
    IF NEW.is_qualified THEN
        PERFORM update_competition_rankings(NEW.competition_id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_qualification
    BEFORE UPDATE ON competition_participants
    FOR EACH ROW
    WHEN (OLD.total_referral_volume IS DISTINCT FROM NEW.total_referral_volume)
    EXECUTE FUNCTION update_qualification_status();

-- 8. Initial data setup function
CREATE OR REPLACE FUNCTION setup_default_competition()
RETURNS UUID AS $$
DECLARE
    comp_id UUID;
    current_phase_id INTEGER;
BEGIN
    -- Get current active phase
    SELECT id INTO current_phase_id 
    FROM investment_phases 
    WHERE is_active = TRUE 
    ORDER BY phase_number DESC 
    LIMIT 1;
    
    -- Create default competition
    INSERT INTO competitions (
        name,
        description,
        phase_id,
        start_date,
        minimum_qualification_amount,
        total_prize_pool,
        status
    ) VALUES (
        'Gold Diggers Club - Phase ' || COALESCE(current_phase_id::text, '1'),
        'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
        current_phase_id,
        NOW(),
        2500.00,
        150000.00, -- Total of all prizes: $60k + $30k + $18k + 7×$6k = $150k
        'active'
    ) RETURNING id INTO comp_id;
    
    -- Create prize tiers - TOP 10 PRIZES
    INSERT INTO competition_prize_tiers (competition_id, tier_name, tier_rank_start, tier_rank_end, prize_amount, display_order, emoji) VALUES
    (comp_id, '1st Place', 1, 1, 60000.00, 1, '🥇'),
    (comp_id, '2nd Place', 2, 2, 30000.00, 2, '🥈'),
    (comp_id, '3rd Place', 3, 3, 18000.00, 3, '🥉'),
    (comp_id, '4th Place', 4, 4, 6000.00, 4, '🏆'),
    (comp_id, '5th Place', 5, 5, 6000.00, 5, '🏆'),
    (comp_id, '6th Place', 6, 6, 6000.00, 6, '🏆'),
    (comp_id, '7th Place', 7, 7, 6000.00, 7, '🏆'),
    (comp_id, '8th Place', 8, 8, 6000.00, 8, '🏆'),
    (comp_id, '9th Place', 9, 9, 6000.00, 9, '🏆'),
    (comp_id, '10th Place', 10, 10, 6000.00, 10, '🏆');
    
    RETURN comp_id;
END;
$$ LANGUAGE plpgsql;
