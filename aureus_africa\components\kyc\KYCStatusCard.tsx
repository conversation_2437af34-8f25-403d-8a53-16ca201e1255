import React from 'react';

interface KYCStatusCardProps {
  status: 'pending' | 'completed' | 'rejected' | 'expired';
  kycData?: any;
  onStartKYC?: () => void;
  onEditKYC?: () => void;
}

export const KYCStatusCard: React.FC<KYCStatusCardProps> = ({
  status,
  kycData,
  onStartKYC,
  onEditKYC
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'completed':
        return {
          icon: '✅',
          title: 'KYC Verification Complete',
          description: 'Your identity has been successfully verified',
          bgColor: 'bg-green-900/30',
          borderColor: 'border-green-700',
          textColor: 'text-green-400',
          descColor: 'text-green-200'
        };
      case 'rejected':
        return {
          icon: '❌',
          title: 'KYC Verification Rejected',
          description: 'Please review and resubmit your information',
          bgColor: 'bg-red-900/30',
          borderColor: 'border-red-700',
          textColor: 'text-red-400',
          descColor: 'text-red-200'
        };
      case 'expired':
        return {
          icon: '⏰',
          title: 'KYC Verification Expired',
          description: 'Your verification has expired and needs to be renewed',
          bgColor: 'bg-orange-900/30',
          borderColor: 'border-orange-700',
          textColor: 'text-orange-400',
          descColor: 'text-orange-200'
        };
      default: // pending
        return {
          icon: '⏳',
          title: 'KYC Verification Required',
          description: 'Complete your identity verification to access all features',
          bgColor: 'bg-yellow-900/30',
          borderColor: 'border-yellow-700',
          textColor: 'text-yellow-400',
          descColor: 'text-yellow-200'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`${config.bgColor} rounded-lg p-6 border ${config.borderColor}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-4">
          <span className="text-3xl">{config.icon}</span>
          <div className="flex-1">
            <h3 className={`${config.textColor} font-semibold text-lg mb-1`}>
              {config.title}
            </h3>
            <p className={`${config.descColor} text-sm mb-4`}>
              {config.description}
            </p>

            {/* KYC Details for completed status */}
            {status === 'completed' && kycData && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm mb-4">
                <div>
                  <span className="text-gray-400">Full Name:</span>
                  <span className="text-white ml-2">{kycData.full_legal_name}</span>
                </div>
                <div>
                  <span className="text-gray-400">ID Type:</span>
                  <span className="text-white ml-2">
                    {kycData.id_type === 'national_id' ? 'National ID' :
                     kycData.id_type === 'passport' ? 'Passport' :
                     kycData.id_type === 'drivers_license' ? 'Driver\'s License' : kycData.id_type}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">Country:</span>
                  <span className="text-white ml-2">{kycData.country_name}</span>
                </div>
                <div>
                  <span className="text-gray-400">Verified:</span>
                  <span className="text-white ml-2">
                    {new Date(kycData.kyc_completed_at).toLocaleDateString()}
                  </span>
                </div>
                {kycData.certificate_data?.documents && (
                  <div className="col-span-2">
                    <span className="text-gray-400">Documents:</span>
                    <div className="ml-2 flex space-x-4 text-sm">
                      {kycData.certificate_data.documents.id_document && (
                        <span className="text-green-400">✓ ID Document</span>
                      )}
                      {kycData.certificate_data.documents.proof_of_residence && (
                        <span className="text-green-400">✓ Proof of Residence</span>
                      )}
                    </div>
                  </div>
                )}
                {kycData.certificate_data?.facial_recognition && (
                  <div className="col-span-2">
                    <span className="text-gray-400">Facial Recognition:</span>
                    <div className="ml-2 flex items-center space-x-4 text-sm">
                      <span className="text-green-400">
                        ✓ Verified ({(kycData.certificate_data.facial_recognition.confidence_score * 100).toFixed(1)}%)
                      </span>
                      <div className="flex space-x-2">
                        {kycData.certificate_data.facial_recognition.liveness_checks.blink_detection && (
                          <span className="text-xs bg-green-900/30 text-green-400 px-2 py-1 rounded">Blink</span>
                        )}
                        {kycData.certificate_data.facial_recognition.liveness_checks.head_movement && (
                          <span className="text-xs bg-green-900/30 text-green-400 px-2 py-1 rounded">Movement</span>
                        )}
                        {kycData.certificate_data.facial_recognition.liveness_checks.smile_detection && (
                          <span className="text-xs bg-green-900/30 text-green-400 px-2 py-1 rounded">Smile</span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3">
              {status === 'pending' && (
                <button
                  onClick={onStartKYC}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium"
                >
                  Start KYC Verification
                </button>
              )}

              {(status === 'completed' || status === 'rejected' || status === 'expired') && (
                <button
                  onClick={onEditKYC}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 text-sm font-medium"
                >
                  {status === 'completed' ? 'Edit Information' : 'Resubmit KYC'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      {status === 'pending' && (
        <div className="mt-4 p-3 bg-blue-900/30 rounded-lg border border-blue-700">
          <h4 className="text-blue-400 font-medium text-sm mb-2">What you'll need:</h4>
          <ul className="text-blue-200 text-xs space-y-1">
            <li>• Valid government-issued ID (National ID or Passport)</li>
            <li>• Current residential address</li>
            <li>• Phone number and email address</li>
            <li>• 5-10 minutes to complete the form</li>
          </ul>
        </div>
      )}

      {status === 'rejected' && (
        <div className="mt-4 p-3 bg-red-900/30 rounded-lg border border-red-700">
          <h4 className="text-red-400 font-medium text-sm mb-2">Common rejection reasons:</h4>
          <ul className="text-red-200 text-xs space-y-1">
            <li>• Unclear or blurry document images</li>
            <li>• Information doesn't match official documents</li>
            <li>• Incomplete or missing required fields</li>
            <li>• Document has expired or is invalid</li>
          </ul>
        </div>
      )}

      {status === 'completed' && (
        <div className="mt-4 p-3 bg-green-900/30 rounded-lg border border-green-700">
          <div className="flex items-center space-x-2">
            <span className="text-green-400">🎉</span>
            <p className="text-green-200 text-sm">
              <strong>Congratulations!</strong> You can now access and download your share certificates.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default KYCStatusCard;
