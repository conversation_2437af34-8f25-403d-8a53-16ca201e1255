#!/usr/bin/env node

/**
 * TEST LOGIN WITH NEW PASSWORD
 * 
 * This script tests that the user can login with the new password after reset
 */

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const serviceClient = createClient(supabaseUrl, supabaseServiceKey);

async function testLoginWithNewPassword() {
  console.log('🧪 Testing login with new password...\n');
  
  const testEmail = '<EMAIL>';
  const newPassword = 'NewTestPassword123!';
  
  try {
    console.log('1️⃣ Checking user exists...');
    
    // Get user data
    const { data: user, error: userError } = await serviceClient
      .from('users')
      .select('id, email, username, password_hash')
      .eq('email', testEmail)
      .single();
    
    if (userError || !user) {
      console.error('❌ User not found:', userError?.message);
      return false;
    }
    
    console.log('✅ User found:', user.email);
    
    console.log('\n2️⃣ Testing password verification...');
    
    // Test password verification
    const passwordMatch = await bcrypt.compare(newPassword, user.password_hash);
    
    if (!passwordMatch) {
      console.error('❌ Password verification failed');
      console.log('Expected password:', newPassword);
      console.log('Stored hash:', user.password_hash);
      return false;
    }
    
    console.log('✅ Password verification successful');
    
    console.log('\n3️⃣ Testing Supabase auth login...');
    
    // Test Supabase auth login
    const publicClient = createClient(supabaseUrl, process.env.VITE_SUPABASE_ANON_KEY);
    const { data: authData, error: authError } = await publicClient.auth.signInWithPassword({
      email: testEmail,
      password: newPassword
    });
    
    if (authError) {
      console.error('❌ Supabase auth login failed:', authError.message);
      return false;
    }
    
    if (!authData.user) {
      console.error('❌ No user returned from auth');
      return false;
    }
    
    console.log('✅ Supabase auth login successful');
    console.log('👤 Auth user:', {
      id: authData.user.id,
      email: authData.user.email
    });
    
    console.log('\n🎉 Login with new password test PASSED!');
    
    console.log('\n🔗 Frontend Testing Instructions:');
    console.log('1. Go to: http://localhost:8004/login');
    console.log('2. Click "Login (Web)" tab');
    console.log('3. Click "Forgot your password?"');
    console.log(`4. Enter email: ${testEmail}`);
    console.log('5. Enter PIN: 999888');
    console.log(`6. Set new password: ${newPassword}`);
    console.log('7. Click "Update Password"');
    console.log('8. Should see confirmation and auto-login to dashboard');
    
    return true;
    
  } catch (error) {
    console.error('❌ Login test failed:', error);
    return false;
  }
}

// Run the test
testLoginWithNewPassword().then(success => {
  console.log(`\n${success ? '🎉' : '💥'} Test ${success ? 'PASSED' : 'FAILED'}`);
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
