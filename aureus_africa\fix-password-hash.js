#!/usr/bin/env node

/**
 * FIX PASSWORD HASH
 * 
 * Directly sets the correct password hash for the user
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

const fixPasswordHash = async () => {
  try {
    console.log('🔧 FIXING PASSWORD HASH DIRECTLY...\n');

    const telegramId = '1393852532';
    const correctPassword = 'Gunst0n5o0!@#';

    // Get the user details
    const { data: telegramUser } = await supabaseAdmin
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (!telegramUser) {
      console.log('❌ Telegram user not found');
      return;
    }

    const { data: linkedUser } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', telegramUser.user_id)
      .single();

    if (!linkedUser) {
      console.log('❌ Linked user not found');
      return;
    }

    console.log('📋 Current User Details:');
    console.log(`   User ID: ${linkedUser.id}`);
    console.log(`   Username: ${linkedUser.username}`);
    console.log(`   Email: ${linkedUser.email}`);
    console.log(`   Current Hash: ${linkedUser.password_hash ? 'EXISTS' : 'MISSING'}`);

    // Generate new hash with higher salt rounds for security
    console.log('\n🔐 Generating new password hash...');
    const newHash = await bcrypt.hash(correctPassword, 12);
    console.log(`   New hash generated: ${newHash.substring(0, 20)}...`);

    // Update the user record
    const { error: updateError } = await supabaseAdmin
      .from('users')
      .update({
        password_hash: newHash,
        updated_at: new Date().toISOString()
      })
      .eq('id', linkedUser.id);

    if (updateError) {
      console.log('❌ Password update failed:', updateError.message);
      return;
    }

    console.log('✅ Password hash updated successfully');

    // Verify the new hash works
    console.log('\n🧪 Verifying new password hash...');
    const isValid = await bcrypt.compare(correctPassword, newHash);
    console.log(`   Verification result: ${isValid ? '✅ SUCCESS' : '❌ FAILED'}`);

    if (isValid) {
      // Test the complete login flow
      console.log('\n🔍 Testing complete login flow...');
      
      const { data: updatedUser } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', linkedUser.id)
        .single();

      const finalVerification = await bcrypt.compare(correctPassword, updatedUser.password_hash);
      console.log(`   Final verification: ${finalVerification ? '✅ SUCCESS' : '❌ FAILED'}`);

      if (finalVerification) {
        console.log('\n🎉 PASSWORD FIX COMPLETED SUCCESSFULLY!');
        console.log('📋 Login Credentials:');
        console.log(`   Telegram ID: ${telegramId}`);
        console.log(`   Password: ${correctPassword}`);
        console.log('   Status: ✅ READY FOR LOGIN');

        // Also try to fix the Supabase Auth user
        console.log('\n🔧 Attempting to sync Supabase Auth user...');
        try {
          const { data: authUsers } = await supabaseAdmin.auth.admin.listUsers();
          const authUser = authUsers.users.find(u => u.email === linkedUser.email);
          
          if (authUser) {
            const { error: authUpdateError } = await supabaseAdmin.auth.admin.updateUserById(
              authUser.id,
              { password: correctPassword }
            );
            
            if (authUpdateError) {
              console.log('⚠️ Auth user sync failed:', authUpdateError.message);
              console.log('   Database login will still work');
            } else {
              console.log('✅ Auth user password synced');
            }
          }
        } catch (authErr) {
          console.log('⚠️ Auth sync error:', authErr.message);
          console.log('   Database login will still work');
        }
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎯 PASSWORD FIX SUMMARY:');
    console.log('✅ Password hash updated in database');
    console.log('✅ Password verification confirmed working');
    console.log('✅ User should now be able to login');
    console.log('\n📋 NEXT STEPS:');
    console.log('1. Test login with the credentials above');
    console.log('2. If Supabase Auth still fails, use the emergency login bypass');
    console.log('3. Verify profile data loads correctly after login');
    console.log('='.repeat(60));

  } catch (error) {
    console.error('❌ Password hash fix failed:', error);
  }
};

fixPasswordHash();
