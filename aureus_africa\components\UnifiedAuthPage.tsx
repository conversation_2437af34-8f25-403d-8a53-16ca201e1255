import React, { useState, useEffect } from 'react';
import { supabase, signInWithTelegramId, authenticateWithTelegramBot, pollForAuthConfirmation, signInWithEmailEnhanced, getServiceRoleClient } from '../lib/supabase';
import { TermsAndConditions } from './TermsAndConditions';
import { PrivacyPolicy } from './PrivacyPolicy';
import { EmailRegistrationFormProgressive } from './EmailRegistrationFormProgressive';
import { PinPasswordResetForm } from './PinPasswordResetForm';

interface UnifiedAuthPageProps {
  onAuthSuccess: (user: any) => void;
  onBack: () => void;
}

interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}

export const UnifiedAuthPage: React.FC<UnifiedAuthPageProps> = ({ onAuthSuccess, onBack }) => {
  const [activeTab, setActiveTab] = useState<'telegram' | 'web-login' | 'web-register' | 'password-reset'>('web-register'); // Default to web register tab
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Debug logging
  console.log('🔍 UnifiedAuthPage rendered', { activeTab, isLoading, error, success });

  // Removed Remember Me functionality - was causing delays and not working properly
  


  // Web login form state
  const [loginForm, setLoginForm] = useState({
    email: '',
    password: ''
  });

  // Modal states
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);

  // Password visibility states
  const [showLoginPassword, setShowLoginPassword] = useState(true);

  // Telegram bot authentication state
  const [telegramAuthToken, setTelegramAuthToken] = useState<string | null>(null);
  const [showTelegramInstructions, setShowTelegramInstructions] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  const handleTelegramAuth = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔐 Starting Telegram bot authentication...');

      // Generate authentication token and show instructions
      const { token, success, error: authError } = await authenticateWithTelegramBot();

      if (!success || !token) {
        throw new Error(authError || 'Failed to generate authentication token');
      }

      setTelegramAuthToken(token);
      setShowTelegramInstructions(true);
      setIsPolling(true);

      // Start polling for confirmation
      console.log('🔄 Starting authentication polling...');
      const { success: pollSuccess, userData, error: pollError } = await pollForAuthConfirmation(token);

      if (!pollSuccess) {
        throw new Error(pollError || 'Authentication failed');
      }

      console.log('✅ Telegram authentication successful!', userData);
      console.log('🔍 [DEBUG] Full userData object:', JSON.stringify(userData, null, 2));

      // Check if userData exists and has required properties
      if (!userData) {
        console.log('⚠️ [DEBUG] No userData returned - user may not exist in telegram_users table');
        throw new Error('Authentication successful but user data not found. Please ensure you are registered with the Telegram bot first.');
      }

      console.log('🔍 [DEBUG] userData.user_status:', userData.user_status);
      console.log('🔍 [DEBUG] userData.telegram_id:', userData.telegram_id);

      // Process the authenticated user data
      const telegramId = userData.telegram_id;
      console.log('🔍 [DEBUG] Attempting signInWithTelegramId with:', telegramId);
      console.log('🔍 [DEBUG] User data from bot:', userData);

      const signInResult = await signInWithTelegramId(telegramId.toString());

      console.log('🔍 [DEBUG] signInWithTelegramId result:', signInResult);

      if (!signInResult.success) {
        console.error('❌ Authentication failed:', signInResult.error);
        setError(`Authentication failed: ${signInResult.error || 'Unknown error'}`);
        return;
      }

      if (!signInResult.user) {
        console.error('❌ No user object returned');
        setError('Authentication failed: No user data received');
        return;
      }

      const authenticatedUser = signInResult.user;

      console.log('✅ Authentication successful, calling onAuthSuccess with:', authenticatedUser);
      setSuccess('Successfully authenticated with Telegram!');
      setTimeout(() => onAuthSuccess(authenticatedUser), 1500);

    } catch (err: any) {
      console.error('❌ Telegram auth error:', err);
      setError(err.message || 'Failed to authenticate with Telegram');
    } finally {
      setIsLoading(false);
      setIsPolling(false);
      setShowTelegramInstructions(false);
    }
  };



  const handleLoginInputChange = (field: keyof typeof loginForm, value: string | boolean) => {
    setLoginForm(prev => ({ ...prev, [field]: value }));
  };

  const handleWebLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🔐 Starting web login', loginForm.email);
    setIsLoading(true);
    setError(null);

    // Basic validation
    if (!loginForm.email || !loginForm.password) {
      setError('Please enter both email and password');
      setIsLoading(false);
      return;
    }

    try {
      console.log('🔐 Attempting enhanced email login...');
      const { user, error: loginError } = await signInWithEmailEnhanced(loginForm.email, loginForm.password);

      if (loginError) {
        console.error('❌ Login error:', loginError.message);
        // Provide specific, user-friendly error messages
        let userFriendlyError = loginError.message;

        // Handle common Supabase auth errors with better messaging
        if (loginError.message?.includes('Invalid login credentials')) {
          userFriendlyError = 'Incorrect email or password. Please check your credentials and try again.';
        } else if (loginError.message?.includes('Email not confirmed')) {
          userFriendlyError = 'Please verify your email address before signing in. Check your inbox for a verification email.';
        } else if (loginError.message?.includes('Too many requests')) {
          userFriendlyError = 'Too many login attempts. Please wait a few minutes before trying again.';
        } else if (loginError.message?.includes('User not found')) {
          userFriendlyError = 'No account found with this email address. Please check your email or create a new account.';
        }

        setError(userFriendlyError);
        return;
      }

      if (!user) {
        console.error('❌ No user returned from login');
        setError('Login failed - no user data received');
        return;
      }

      console.log('✅ Web login successful!', user);

      // Remember Me functionality removed - was causing delays and not working properly

      setSuccess('Login successful! Welcome back.');
      setTimeout(() => onAuthSuccess(user), 1500);

    } catch (err) {
      console.error('❌ Login exception:', err);
      // Provide more helpful error message based on the error type
      let errorMessage = 'Login failed. Please try again.';

      if (err instanceof Error) {
        if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        } else if (err.message.includes('timeout')) {
          errorMessage = 'Login request timed out. Please try again.';
        }
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white overflow-y-auto">
      {/* Enhanced Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-3/4 left-3/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-green-500/5 rounded-full blur-2xl animate-pulse" style={{animationDelay: '3s'}}></div>
      </div>

      {/* Header with Back Button */}
      <header className="relative z-10 p-6">
        <div className="container mx-auto">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors duration-200 group"
          >
            <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="font-medium">Back to Home</span>
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 px-4 py-8">
        <div className="container mx-auto max-w-6xl">
          {/* Header Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-6 shadow-2xl">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
                alt="Aureus Alliance Holdings Logo"
                className="w-12 h-12 object-contain"
              />
            </div>
            <h1 className="text-4xl md:text-5xl font-black uppercase tracking-wider text-white mb-4">
              Access Your Account
            </h1>
            <p className="text-xl text-gray-300 font-light max-w-3xl mx-auto">
              Choose your preferred way to access your <span className="gold-text font-semibold">Aureus Alliance</span> account. 
              Existing Telegram users can login instantly, or create a new web account to get started.
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="flex justify-center mb-8">
            <div className="bg-gray-800/50 rounded-lg p-1 backdrop-blur-sm border border-gray-600/30">
              <button
                onClick={() => {
                  console.log('🔄 Switching to Telegram tab');
                  setActiveTab('telegram');
                }}
                className={`px-4 py-3 rounded-md font-medium transition-all duration-200 text-sm ${
                  activeTab === 'telegram'
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <span className="flex items-center gap-2">
                  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"/>
                  </svg>
                  Existing User (Telegram)
                </span>
              </button>
              <button
                onClick={() => {
                  console.log('🔄 Switching to Web Login tab');
                  setActiveTab('web-login');
                }}
                className={`px-4 py-3 rounded-md font-medium transition-all duration-200 text-sm ${
                  activeTab === 'web-login'
                    ? 'bg-purple-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <span className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                  Login (Web)
                </span>
              </button>
              <button
                onClick={() => {
                  console.log('🔄 Switching to Web Register tab');
                  setActiveTab('web-register');
                }}
                className={`px-4 py-3 rounded-md font-medium transition-all duration-200 text-sm ${
                  activeTab === 'web-register'
                    ? 'bg-green-600 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <span className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  New User (Web)
                </span>
              </button>
            </div>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="max-w-2xl mx-auto mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
              <p className="text-red-400 text-center">{error}</p>
            </div>
          )}
          
          {success && (
            <div className="max-w-2xl mx-auto mb-6 p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
              <p className="text-green-400 text-center">{success}</p>
            </div>
          )}

          {/* Content - Single Column Based on Active Tab */}
          <div className="max-w-2xl mx-auto">
            {/* Telegram Login Section */}
            {activeTab === 'telegram' && (
            <div className="transition-all duration-300">
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-600/30 h-full">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"/>
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-2">Login with Telegram</h2>
                  <p className="text-gray-400">
                    Already have an account through our Telegram bot? Login instantly to access your shares, referrals, and purchase history.
                  </p>
                </div>

                <div className="space-y-6">
                  <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                    <h3 className="text-blue-400 font-semibold mb-2">✅ Instant Access</h3>
                    <ul className="text-sm text-gray-300 space-y-1">
                      <li>• View your existing share purchases</li>
                      <li>• Access your referral dashboard</li>
                      <li>• Check commission balances</li>
                      <li>• Download certificates</li>
                    </ul>
                  </div>

                  {/* Telegram Bot Authentication */}
                  <div className="text-center">
                    {!showTelegramInstructions ? (
                      <div className="space-y-4">
                        <button
                          onClick={handleTelegramAuth}
                          disabled={isLoading}
                          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isLoading ? (
                            <span className="flex items-center justify-center">
                              <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                              Connecting to Bot...
                            </span>
                          ) : (
                            <span className="flex items-center justify-center">
                              <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"/>
                              </svg>
                              Login with Telegram Bot
                            </span>
                          )}
                        </button>

                        <p className="text-sm text-gray-400">
                          Click above to authenticate with our Telegram bot
                        </p>
                      </div>
                    ) : (
                      <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-6">
                        <div className="text-blue-400 mb-4">
                          <svg className="w-8 h-8 mx-auto mb-2" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 0C5.374 0 0 5.373 0 12s5.374 12 12 12 12-5.373 12-12S18.626 0 12 0zm5.568 8.16l-1.61 7.59c-.12.54-.44.67-.89.42l-2.46-1.82-1.19 1.14c-.13.13-.24.24-.49.24l.17-2.43 4.47-4.03c.19-.17-.04-.27-.3-.1L9.28 13.47l-2.38-.75c-.52-.16-.53-.52.11-.77l9.28-3.58c.43-.16.81.11.67.73z"/>
                          </svg>
                        </div>
                        <h4 className="text-white font-semibold mb-3">Complete Authentication in Telegram</h4>
                        <div className="text-sm text-gray-300 space-y-4">
                          <p><strong>Step 1:</strong> Open Telegram and find our bot: <span className="text-blue-400">@AureusAllianceBot</span></p>
                          <p><strong>Step 2:</strong> Simply send this PIN to the bot (just type the numbers):</p>
                          <div className="bg-gray-800 rounded-lg p-4 text-center">
                            <div className="text-4xl font-bold text-green-400 font-mono tracking-widest mb-3">
                              {telegramAuthToken}
                            </div>
                            <button
                              onClick={() => navigator.clipboard?.writeText(telegramAuthToken || '')}
                              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                              Copy PIN
                            </button>
                            <p className="text-xs text-gray-400 mt-2">No commands needed - just send the 6 digits!</p>
                          </div>
                          <p><strong>Step 3:</strong> Confirm the authentication request in the bot</p>
                          <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-3 mt-4">
                            <p className="text-green-400 text-xs">
                              💡 <strong>Pro tip:</strong> Just paste or type the 6-digit PIN directly in Telegram - no need for any commands!
                            </p>
                          </div>
                        </div>

                        {isPolling && (
                          <div className="mt-4 text-blue-400">
                            <div className="animate-spin inline-block w-5 h-5 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                            Waiting for confirmation...
                          </div>
                        )}

                        <button
                          onClick={() => {
                            setShowTelegramInstructions(false);
                            setTelegramAuthToken(null);
                            setIsPolling(false);
                          }}
                          className="mt-4 text-sm text-gray-400 hover:text-white underline"
                        >
                          Cancel Authentication
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="text-center text-sm text-gray-400">
                    <p>Secure authentication powered by Telegram</p>
                  </div>
                </div>
              </div>
            </div>
            )}

            {/* Web Login Section */}
            {activeTab === 'web-login' && (
            <div className="transition-all duration-300">
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-600/30 h-full">
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-2">Login to Your Account</h2>
                  <p className="text-gray-400">
                    Already have a web account? Sign in with your email and password.
                  </p>
                </div>

                <form onSubmit={handleWebLogin} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Email Address or Username</label>
                    <input
                      type="text"
                      required
                      value={loginForm.email}
                      onChange={(e) => handleLoginInputChange('email', e.target.value)}
                      className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="Enter your email or username"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Password</label>
                    <div className="relative">
                      <input
                        type={showLoginPassword ? "text" : "password"}
                        required
                        value={loginForm.password}
                        onChange={(e) => handleLoginInputChange('password', e.target.value)}
                        className="w-full px-4 py-3 pr-12 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder="Enter your password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowLoginPassword(!showLoginPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 focus:outline-none"
                      >
                        {showLoginPassword ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>



                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white font-semibold py-3 px-6 rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center gap-2">
                        <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"/>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                        </svg>
                        Signing In...
                      </span>
                    ) : (
                      'Sign In'
                    )}
                  </button>
                </form>

                {/* Forgot Password Link */}
                <div className="mt-4 text-center">
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-300 text-sm transition-colors duration-200"
                    onClick={() => setActiveTab('password-reset')}
                  >
                    Forgot your password?
                  </button>
                </div>

                <div className="mt-6 text-center">
                  <p className="text-sm text-gray-400">
                    Don't have an account?{' '}
                    <button
                      onClick={() => setActiveTab('web-register')}
                      className="text-green-400 hover:text-green-300 font-medium"
                    >
                      Create one here
                    </button>
                  </p>
                </div>
              </div>
            </div>
            )}

            {/* Web Registration Section */}
            {activeTab === 'web-register' && (
              <div className="transition-all duration-300">
                <EmailRegistrationFormProgressive
                  onRegistrationSuccess={(user) => {
                    console.log('✅ Registration successful from EmailRegistrationFormProgressive:', user);
                    onAuthSuccess(user);
                  }}
                  onSwitchToLogin={() => setActiveTab('web-login')}
                  onSwitchToTelegram={() => setActiveTab('telegram')}
                />
              </div>
            )}

            {/* Password Reset Section */}
            {activeTab === 'password-reset' && (
              <div className="transition-all duration-300">
                <PinPasswordResetForm
                  onResetSuccess={() => {
                    console.log('✅ Password reset successful');
                    setSuccess('Password reset successful! You can now login with your new password.');
                    setActiveTab('web-login');
                  }}
                  onResetError={(error) => {
                    console.error('❌ Password reset error:', error);
                    setError(error);
                  }}
                  onBack={() => setActiveTab('web-login')}
                  onLoginSuccess={(user) => {
                    console.log('✅ Auto-login successful after password reset:', user);
                    setSuccess('Password reset and login successful! Welcome back!');
                    onAuthSuccess(user);
                  }}
                />
              </div>
            )}
          </div>

          {/* Bottom Info Section */}
          <div className="mt-12 text-center">
            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-600/20">
              <h3 className="text-xl font-semibold text-white mb-4">Choose Your Access Method</h3>
              <div className="grid md:grid-cols-3 gap-6 text-sm text-gray-300">
                <div>
                  <h4 className="font-semibold text-blue-400 mb-2">Telegram Login</h4>
                  <p>Perfect for existing users who have already purchased shares through our bot. Get instant access to all your data.</p>
                </div>
                <div>
                  <h4 className="font-semibold text-purple-400 mb-2">Web Login</h4>
                  <p>For existing web users who have already created an account. Sign in with your email and password.</p>
                </div>
                <div>
                  <h4 className="font-semibold text-green-400 mb-2">Web Registration</h4>
                  <p>Ideal for new users who want to explore the platform first, then connect to Telegram for purchases later.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Terms and Conditions Modal */}
      {showTermsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">Terms and Conditions</h2>
              <button
                onClick={() => setShowTermsModal(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <TermsAndConditions />
            </div>
          </div>
        </div>
      )}

      {/* Privacy Policy Modal */}
      {showPrivacyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-gray-700">
              <h2 className="text-2xl font-bold text-white">Privacy Policy</h2>
              <button
                onClick={() => setShowPrivacyModal(false)}
                className="text-gray-400 hover:text-white text-2xl"
              >
                ×
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <PrivacyPolicy />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
