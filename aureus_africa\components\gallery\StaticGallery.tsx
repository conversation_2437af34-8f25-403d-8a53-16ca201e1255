import React, { useState, useEffect } from 'react';
import { GalleryGrid } from './GalleryGrid';
import { GalleryLightbox, useLightboxNavigation } from './GalleryLightbox';
import type { GalleryImage } from '../../types/gallery';

// Helper function to get high-resolution image URL
const getHighResImageUrl = (filename: string) => {
  const baseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/';
  // Return the original image URL without transforms to get full resolution
  // Supabase storage serves original files when no transform parameters are used
  return `${baseUrl}${filename}`;
};

// Static fallback images from Supabase storage
const STATIC_GALLERY_IMAGES: GalleryImage[] = [
  {
    id: 'static-1',
    title: 'Mining Operations Overview',
    description: 'Comprehensive view of our mining operations and infrastructure',
    image_url: getHighResImageUrl('gallery-1.jpg'),
    alt_text: 'Aureus Alliance Holdings Professional Gold Mining Operations - Comprehensive View of Mining Infrastructure and Equipment',
    is_featured: true,
    is_active: true,
    display_order: 1,
    category_id: 'mining',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: {
      id: 'mining',
      name: 'Mining Operations',
      description: 'On-site mining activities',
      is_active: true,
      display_order: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  },
  {
    id: 'static-2',
    title: 'Equipment and Machinery',
    description: 'State-of-the-art mining equipment in action',
    image_url: getHighResImageUrl('gallery-2.jpg'),
    alt_text: 'Aureus Alliance Holdings Heavy Mining Equipment - Professional Grade Excavators and Processing Machinery for Gold Placer Mining',
    is_featured: true,
    is_active: true,
    display_order: 2,
    category_id: 'equipment',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: {
      id: 'equipment',
      name: 'Equipment',
      description: 'Mining equipment and machinery',
      is_active: true,
      display_order: 2,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  },
  {
    id: 'static-3',
    title: 'Site Location',
    description: 'Strategic mining site location and terrain',
    image_url: getHighResImageUrl('gallery-3.jpg'),
    alt_text: 'Mining site location',
    is_featured: true,
    is_active: true,
    display_order: 3,
    category_id: 'locations',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: { 
      id: 'locations',
      name: 'Locations',
      description: 'Mining site locations',
      is_active: true,
      display_order: 3,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  },
  {
    id: 'static-4',
    title: 'Team Collaboration',
    description: 'Our professional team working together on-site',
    image_url: getHighResImageUrl('gallery-4.jpg'),
    alt_text: 'Aureus Alliance Holdings Professional Mining Team - Experienced Engineers and Operators Collaborating on Gold Mining Operations',
    is_featured: true,
    is_active: true,
    display_order: 4,
    category_id: 'team',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: {
      id: 'team',
      name: 'Team',
      description: 'Our professional team',
      is_active: true,
      display_order: 4,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  },
  {
    id: 'static-5',
    title: 'Processing Facility',
    description: 'Advanced gold processing and refinement facility',
    image_url: getHighResImageUrl('gallery-5.jpg'),
    alt_text: 'Gold processing facility',
    is_featured: true,
    is_active: true,
    display_order: 5,
    category_id: 'mining',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: { 
      id: 'mining',
      name: 'Mining Operations',
      description: 'On-site mining activities',
      is_active: true,
      display_order: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  },
  {
    id: 'static-6',
    title: 'Extraction Process',
    description: 'Gold extraction and processing in progress',
    image_url: getHighResImageUrl('gallery-6.jpg'),
    alt_text: 'Gold extraction process',
    is_featured: true,
    is_active: true,
    display_order: 6,
    category_id: 'mining',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: { 
      id: 'mining',
      name: 'Mining Operations',
      description: 'On-site mining activities',
      is_active: true,
      display_order: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  },
  {
    id: 'static-7',
    title: 'Quality Control',
    description: 'Rigorous quality control and testing procedures',
    image_url: getHighResImageUrl('gallery-7.jpg'),
    alt_text: 'Quality control procedures',
    is_featured: true,
    is_active: true,
    display_order: 7,
    category_id: 'mining',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: { 
      id: 'mining',
      name: 'Mining Operations',
      description: 'On-site mining activities',
      is_active: true,
      display_order: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  },
  {
    id: 'static-8',
    title: 'Safety Standards',
    description: 'Maintaining highest safety standards in all operations',
    image_url: getHighResImageUrl('gallery-8.jpg'),
    alt_text: 'Safety standards implementation',
    is_featured: true,
    is_active: true,
    display_order: 8,
    category_id: 'team',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: { 
      id: 'team',
      name: 'Team',
      description: 'Our professional team',
      is_active: true,
      display_order: 4,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  },
  {
    id: 'static-9',
    title: 'Final Product',
    description: 'Refined gold ready for market distribution',
    image_url: getHighResImageUrl('gallery-9.jpg'),
    alt_text: 'Refined gold final product',
    is_featured: true,
    is_active: true,
    display_order: 9,
    category_id: 'products',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updated_by: 'system',
    category: { 
      id: 'products',
      name: 'Products',
      description: 'Final gold products',
      is_active: true,
      display_order: 5,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      updated_by: 'system'
    }
  }
];

interface StaticGalleryProps {
  showCategories?: boolean;
  itemsPerPage?: number;
}

export const StaticGallery: React.FC<StaticGalleryProps> = ({
  showCategories = true,
  itemsPerPage = 12
}) => {
  const [lightboxIndex, setLightboxIndex] = useState<number>(-1);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  // Filter images by category
  const filteredImages = selectedCategory 
    ? STATIC_GALLERY_IMAGES.filter(img => img.category_id === selectedCategory)
    : STATIC_GALLERY_IMAGES;

  // Get unique categories
  const categories = Array.from(
    new Map(
      STATIC_GALLERY_IMAGES.map(img => [img.category_id, img.category])
    ).values()
  );

  // Pagination logic
  const totalPages = Math.ceil(filteredImages.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedImages = filteredImages.slice(startIndex, endIndex);

  const handleImageClick = (image: GalleryImage) => {
    const index = filteredImages.findIndex(img => img.id === image.id);
    setLightboxIndex(index);
  };

  const handleLightboxClose = () => {
    setLightboxIndex(-1);
  };

  const handleLightboxNext = () => {
    setLightboxIndex(prev => (prev + 1) % filteredImages.length);
  };

  const handleLightboxPrevious = () => {
    setLightboxIndex(prev => (prev - 1 + filteredImages.length) % filteredImages.length);
  };

  const handleLightboxNavigate = (index: number) => {
    setLightboxIndex(index);
  };

  // Use the lightbox navigation hook
  useLightboxNavigation(filteredImages, handleLightboxNavigate);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    // Reset to first page when category changes
    if (newPage === 1 && currentPage !== 1) {
      setCurrentPage(1);
    }
  };

  // Reset to first page when category changes
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedCategory]);

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      {showCategories && (
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setSelectedCategory('')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                !selectedCategory
                  ? 'bg-gradient-to-r from-amber-500 to-amber-600 text-black shadow-lg'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-amber-400 border border-gray-600'
              }`}
            >
              All Categories ({STATIC_GALLERY_IMAGES.length})
            </button>
            {categories.map((category) => {
              const count = STATIC_GALLERY_IMAGES.filter(img => img.category_id === category.id).length;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-amber-500 to-amber-600 text-black shadow-lg'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-amber-400 border border-gray-600'
                  }`}
                >
                  {category.name} ({count})
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Gallery Grid */}
      <GalleryGrid
        images={paginatedImages}
        onImageClick={handleImageClick}
        columns={3}
        showOverlay={true}
        showCategories={false}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2 mt-8">
          <button
            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-600 rounded-lg hover:bg-gray-700 hover:text-amber-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                currentPage === page
                  ? 'bg-gradient-to-r from-amber-500 to-amber-600 text-black shadow-lg'
                  : 'text-gray-300 bg-gray-800 border border-gray-600 hover:bg-gray-700 hover:text-amber-400'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-4 py-2 text-sm font-medium text-gray-300 bg-gray-800 border border-gray-600 rounded-lg hover:bg-gray-700 hover:text-amber-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            Next
          </button>
        </div>
      )}

      {/* Lightbox */}
      <GalleryLightbox
        images={filteredImages}
        currentIndex={Math.max(0, lightboxIndex)}
        isOpen={lightboxIndex >= 0}
        onClose={handleLightboxClose}
        onNext={handleLightboxNext}
        onPrevious={handleLightboxPrevious}
      />
    </div>
  );
};
