-- R<PERSON> functions to check for existing emails and usernames
-- These functions run with elevated privileges and bypass RLS policies

-- Function to check if email exists in users table
CREATE OR REPLACE FUNCTION check_email_exists(email_to_check TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE LOWER(email) = LOWER(email_to_check)
  );
END;
$$;

-- Function to check if username exists in users table
CREATE OR REPLACE FUNCTION check_username_exists(username_to_check TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE LOWER(username) = LOWER(username_to_check)
  );
END;
$$;

-- <PERSON> execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION check_email_exists(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION check_username_exists(TEXT) TO authenticated;

-- <PERSON> execute permissions to anon users (for registration)
GRANT EXECUTE ON FUNCTION check_email_exists(TEXT) TO anon;
GRANT EXECUTE ON FUNCTION check_username_exists(TEXT) TO anon;
