import React, { useState, useEffect } from 'react';
import { SocialMediaContentGenerator } from './SocialMediaContentGenerator';
import { CampaignAnalytics } from './CampaignAnalytics';
import { MarketingMaterialsGenerator } from './MarketingMaterialsGenerator';
import { ReferralNetworkVisualization } from './ReferralNetworkVisualization';

interface AdvancedMarketingToolkitProps {
  user: any;
  getReferralUsername: (user: any) => string;
  currentPhase?: any;
}

export const AdvancedMarketingToolkit: React.FC<AdvancedMarketingToolkitProps> = ({
  user,
  getReferralUsername,
  currentPhase
}) => {
  const [activeTab, setActiveTab] = useState<'content' | 'analytics' | 'materials' | 'network' | 'training'>('content');
  const [marketingStats, setMarketingStats] = useState({
    totalCampaigns: 0,
    totalClicks: 0,
    totalConversions: 0,
    totalRevenue: 0,
    contentGenerated: 0,
    materialsCreated: 0
  });

  useEffect(() => {
    loadMarketingStats();
  }, [user]);

  const loadMarketingStats = async () => {
    // In a real implementation, this would fetch actual stats from the database
    setMarketingStats({
      totalCampaigns: 12,
      totalClicks: 1847,
      totalConversions: 156,
      totalRevenue: 3920.50,
      contentGenerated: 45,
      materialsCreated: 23
    });
  };

  const tabs = [
    { 
      id: 'content', 
      label: '✨ AI Content', 
      desc: 'Generate social media content',
      icon: '✨'
    },
    { 
      id: 'analytics', 
      label: '📊 Analytics', 
      desc: 'Track campaign performance',
      icon: '📊'
    },
    { 
      id: 'materials', 
      label: '🎨 Materials', 
      desc: 'Create marketing assets',
      icon: '🎨'
    },
    { 
      id: 'network', 
      label: '🌐 Network', 
      desc: 'Visualize referral network',
      icon: '🌐'
    },
    { 
      id: 'training', 
      label: '🎓 Training', 
      desc: 'Learn marketing strategies',
      icon: '🎓'
    }
  ];

  const renderTrainingContent = () => (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151'
    }}>
      <div style={{ marginBottom: '24px' }}>
        <h3 style={{
          fontSize: '20px',
          fontWeight: 'bold',
          color: '#f59e0b',
          marginBottom: '8px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          🎓 Marketing Training Center
        </h3>
        <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0 }}>
          Master the art of referral marketing with our comprehensive training resources
        </p>
      </div>

      {/* Training Modules */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '20px',
        marginBottom: '32px'
      }}>
        {[
          {
            title: '🚀 Getting Started with Referral Marketing',
            description: 'Learn the fundamentals of successful referral marketing',
            duration: '15 min',
            level: 'Beginner',
            topics: ['Understanding referrals', 'Setting up campaigns', 'First steps to success']
          },
          {
            title: '📱 Social Media Mastery',
            description: 'Optimize your content for different social platforms',
            duration: '25 min',
            level: 'Intermediate',
            topics: ['Platform-specific strategies', 'Content optimization', 'Engagement tactics']
          },
          {
            title: '📊 Analytics & Optimization',
            description: 'Use data to improve your marketing performance',
            duration: '20 min',
            level: 'Advanced',
            topics: ['Reading analytics', 'A/B testing', 'Performance optimization']
          },
          {
            title: '🎯 Advanced Targeting Strategies',
            description: 'Identify and reach your ideal audience',
            duration: '30 min',
            level: 'Expert',
            topics: ['Audience research', 'Targeting techniques', 'Conversion optimization']
          }
        ].map((module, index) => (
          <div key={index} style={{
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            borderRadius: '8px',
            padding: '20px',
            border: '1px solid #4b5563'
          }}>
            <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
              {module.title}
            </h4>
            <p style={{ color: '#9ca3af', fontSize: '14px', marginBottom: '12px' }}>
              {module.description}
            </p>
            
            <div style={{ display: 'flex', gap: '12px', marginBottom: '12px', fontSize: '12px' }}>
              <span style={{
                padding: '4px 8px',
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                border: '1px solid #3b82f6',
                borderRadius: '4px',
                color: '#60a5fa'
              }}>
                {module.duration}
              </span>
              <span style={{
                padding: '4px 8px',
                backgroundColor: 'rgba(16, 185, 129, 0.2)',
                border: '1px solid #10b981',
                borderRadius: '4px',
                color: '#10b981'
              }}>
                {module.level}
              </span>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '6px' }}>
                What you'll learn:
              </div>
              <ul style={{ color: '#d1d5db', fontSize: '12px', margin: 0, paddingLeft: '16px' }}>
                {module.topics.map((topic, topicIndex) => (
                  <li key={topicIndex}>{topic}</li>
                ))}
              </ul>
            </div>

            <button style={{
              width: '100%',
              padding: '10px',
              backgroundColor: '#f59e0b',
              border: 'none',
              borderRadius: '6px',
              color: 'white',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer'
            }}>
              Start Module
            </button>
          </div>
        ))}
      </div>

      {/* Quick Tips */}
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '24px'
      }}>
        <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
          💡 Quick Marketing Tips
        </h4>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '16px'
        }}>
          {[
            {
              tip: 'Post consistently across all platforms',
              description: 'Regular posting keeps your audience engaged and increases visibility'
            },
            {
              tip: 'Use compelling visuals',
              description: 'Posts with images get 2.3x more engagement than text-only posts'
            },
            {
              tip: 'Include clear call-to-actions',
              description: 'Tell your audience exactly what you want them to do next'
            },
            {
              tip: 'Track and analyze performance',
              description: 'Use analytics to understand what content works best'
            },
            {
              tip: 'Engage with your audience',
              description: 'Respond to comments and messages to build relationships'
            },
            {
              tip: 'Test different content types',
              description: 'Try videos, images, stories, and posts to see what resonates'
            }
          ].map((item, index) => (
            <div key={index} style={{
              padding: '12px',
              backgroundColor: 'rgba(75, 85, 99, 0.3)',
              borderRadius: '6px',
              border: '1px solid #6b7280'
            }}>
              <div style={{ color: '#10b981', fontSize: '14px', fontWeight: '600', marginBottom: '4px' }}>
                ✅ {item.tip}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>
                {item.description}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Best Practices */}
      <div style={{
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        border: '1px solid rgba(16, 185, 129, 0.3)',
        borderRadius: '8px',
        padding: '20px'
      }}>
        <h4 style={{ color: '#10b981', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
          🏆 Best Practices for Success
        </h4>
        
        <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
          <div style={{ marginBottom: '12px' }}>
            <strong style={{ color: '#10b981' }}>1. Know Your Audience:</strong> Understand who you're targeting and what motivates them to take action.
          </div>
          <div style={{ marginBottom: '12px' }}>
            <strong style={{ color: '#10b981' }}>2. Be Authentic:</strong> Share genuine experiences and build trust with your audience.
          </div>
          <div style={{ marginBottom: '12px' }}>
            <strong style={{ color: '#10b981' }}>3. Provide Value:</strong> Always focus on how your referrals can benefit from the opportunity.
          </div>
          <div style={{ marginBottom: '12px' }}>
            <strong style={{ color: '#10b981' }}>4. Follow Up:</strong> Stay in touch with your referrals and provide ongoing support.
          </div>
          <div>
            <strong style={{ color: '#10b981' }}>5. Stay Compliant:</strong> Always follow platform guidelines and legal requirements.
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '16px',
      padding: '32px',
      marginBottom: '32px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <h2 style={{ 
          fontSize: '28px', 
          fontWeight: 'bold', 
          color: 'white', 
          margin: '0 0 12px 0',
          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          🚀 Advanced Marketing Toolkit
        </h2>
        <p style={{ color: '#9ca3af', fontSize: '16px', margin: 0 }}>
          Professional-grade marketing tools powered by AI and advanced analytics
        </p>
      </div>

      {/* Marketing Stats Overview */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
        gap: '16px',
        marginBottom: '32px'
      }}>
        <div style={{
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          border: '1px solid rgba(59, 130, 246, 0.3)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '20px', marginBottom: '4px' }}>📊</div>
          <div style={{ color: '#60a5fa', fontSize: '20px', fontWeight: 'bold' }}>
            {marketingStats.totalCampaigns}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Campaigns</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          border: '1px solid rgba(16, 185, 129, 0.3)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '20px', marginBottom: '4px' }}>👆</div>
          <div style={{ color: '#10b981', fontSize: '20px', fontWeight: 'bold' }}>
            {marketingStats.totalClicks.toLocaleString()}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Total Clicks</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          border: '1px solid rgba(245, 158, 11, 0.3)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '20px', marginBottom: '4px' }}>💰</div>
          <div style={{ color: '#f59e0b', fontSize: '20px', fontWeight: 'bold' }}>
            {marketingStats.totalConversions}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Conversions</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          border: '1px solid rgba(139, 92, 246, 0.3)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '20px', marginBottom: '4px' }}>📈</div>
          <div style={{ color: '#a78bfa', fontSize: '20px', fontWeight: 'bold' }}>
            ${marketingStats.totalRevenue.toFixed(0)}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Revenue</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(236, 72, 153, 0.1)',
          border: '1px solid rgba(236, 72, 153, 0.3)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '20px', marginBottom: '4px' }}>✨</div>
          <div style={{ color: '#ec4899', fontSize: '20px', fontWeight: 'bold' }}>
            {marketingStats.contentGenerated}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Content</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          border: '1px solid rgba(34, 197, 94, 0.3)',
          borderRadius: '8px',
          padding: '16px',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '20px', marginBottom: '4px' }}>🎨</div>
          <div style={{ color: '#22c55e', fontSize: '20px', fontWeight: 'bold' }}>
            {marketingStats.materialsCreated}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Materials</div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        gap: '8px',
        marginBottom: '32px',
        borderBottom: '1px solid #374151',
        paddingBottom: '16px',
        flexWrap: 'wrap'
      }}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            style={{
              flex: 1,
              minWidth: '150px',
              padding: '16px 12px',
              backgroundColor: activeTab === tab.id ? 'rgba(59, 130, 246, 0.2)' : 'transparent',
              border: activeTab === tab.id ? '1px solid #3b82f6' : '1px solid #374151',
              borderRadius: '12px',
              color: activeTab === tab.id ? '#60a5fa' : '#9ca3af',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              textAlign: 'center',
              transition: 'all 0.2s ease'
            }}
          >
            <div style={{ fontSize: '20px', marginBottom: '4px' }}>{tab.icon}</div>
            <div>{tab.label}</div>
            <div style={{ fontSize: '11px', opacity: 0.8 }}>{tab.desc}</div>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'content' && (
          <SocialMediaContentGenerator
            user={user}
            getReferralUsername={getReferralUsername}
            currentPhase={currentPhase}
          />
        )}

        {activeTab === 'analytics' && (
          <CampaignAnalytics
            userId={user.database_user?.id || 0}
          />
        )}

        {activeTab === 'materials' && (
          <MarketingMaterialsGenerator
            user={user}
            getReferralUsername={getReferralUsername}
            currentPhase={currentPhase}
          />
        )}

        {activeTab === 'network' && (
          <ReferralNetworkVisualization
            userId={user.database_user?.id || 0}
            username={getReferralUsername(user)}
          />
        )}

        {activeTab === 'training' && renderTrainingContent()}
      </div>
    </div>
  );
};
