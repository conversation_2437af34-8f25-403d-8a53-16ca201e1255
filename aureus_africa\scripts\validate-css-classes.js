#!/usr/bin/env node

/**
 * CSS Class Validation Script
 *
 * This script validates that all CSS classes used in React components
 * are defined in the index.html file to prevent black screen issues.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// CSS classes that should be defined
const REQUIRED_CSS_CLASSES = [
  'fade-in-section',
  'slide-in-left', 
  'slide-in-right',
  'float-animation',
  'animated-gradient',
  'glow-gold',
  'glow-cyber', 
  'pulse-glow',
  'cyber-grid',
  'glass-card',
  'glass-card-strong',
  'text-gradient-gold',
  'text-gradient-cyber',
  'btn-primary',
  'mobile-menu',
  'aureus-container',
  'aureus-container-inner',
  'aureus-table-container',
  'aureus-table-inner'
];

// Animation keyframes that should be defined
const REQUIRED_KEYFRAMES = [
  'float',
  'gradientShift', 
  'pulseGlow'
];

function readIndexHtml() {
  const indexPath = path.join(path.dirname(__dirname), 'index.html');
  if (!fs.existsSync(indexPath)) {
    throw new Error('index.html not found');
  }
  return fs.readFileSync(indexPath, 'utf8');
}

function extractCSSClasses(content) {
  const classRegex = /\.([a-zA-Z][a-zA-Z0-9_-]*)\s*\{/g;
  const classes = new Set();
  let match;
  
  while ((match = classRegex.exec(content)) !== null) {
    classes.add(match[1]);
  }
  
  return classes;
}

function extractKeyframes(content) {
  const keyframeRegex = /@keyframes\s+([a-zA-Z][a-zA-Z0-9_-]*)/g;
  const keyframes = new Set();
  let match;
  
  while ((match = keyframeRegex.exec(content)) !== null) {
    keyframes.add(match[1]);
  }
  
  return keyframes;
}

function validateCSS() {
  console.log('🔍 Validating CSS classes and keyframes...\n');
  
  try {
    const indexContent = readIndexHtml();
    const definedClasses = extractCSSClasses(indexContent);
    const definedKeyframes = extractKeyframes(indexContent);
    
    let hasErrors = false;
    
    // Check required CSS classes
    console.log('📋 Checking required CSS classes:');
    const missingClasses = REQUIRED_CSS_CLASSES.filter(cls => !definedClasses.has(cls));
    
    if (missingClasses.length > 0) {
      hasErrors = true;
      console.log('❌ Missing CSS classes:');
      missingClasses.forEach(cls => console.log(`   - .${cls}`));
    } else {
      console.log('✅ All required CSS classes are defined');
    }
    
    console.log();
    
    // Check required keyframes
    console.log('🎬 Checking required keyframes:');
    const missingKeyframes = REQUIRED_KEYFRAMES.filter(kf => !definedKeyframes.has(kf));
    
    if (missingKeyframes.length > 0) {
      hasErrors = true;
      console.log('❌ Missing keyframes:');
      missingKeyframes.forEach(kf => console.log(`   - @keyframes ${kf}`));
    } else {
      console.log('✅ All required keyframes are defined');
    }
    
    console.log();
    
    if (hasErrors) {
      console.log('💥 CSS validation failed! This could cause rendering issues.');
      console.log('📝 Please add the missing CSS classes/keyframes to index.html');
      process.exit(1);
    } else {
      console.log('🎉 CSS validation passed! All required styles are present.');
    }
    
  } catch (error) {
    console.error('❌ CSS validation error:', error.message);
    process.exit(1);
  }
}

// Run validation if called directly
if (import.meta.url.startsWith('file:') && process.argv[1] && import.meta.url.endsWith(path.basename(process.argv[1]))) {
  validateCSS();
}

export { validateCSS, REQUIRED_CSS_CLASSES, REQUIRED_KEYFRAMES };
