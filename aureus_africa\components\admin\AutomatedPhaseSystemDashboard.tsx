import React, { useState, useEffect } from 'react';
import { PhaseAnalyticsDashboard } from './PhaseAnalyticsDashboard';
import { CommissionAutomationDashboard } from './CommissionAutomationDashboard';
import { SmartNotificationSystem } from '../notifications/SmartNotificationSystem';
import { PhaseAutomationService } from '../../lib/services/phaseAutomationService';
import { supabase } from '../../lib/supabase';

interface SystemStatus {
  phaseAutomationEnabled: boolean;
  commissionAutomationEnabled: boolean;
  currentPhase: any;
  nextPhaseETA: string;
  pendingTransitions: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  lastAutomationRun: string;
}

interface AutomatedPhaseSystemDashboardProps {
  user: any;
  isAdmin: boolean;
}

export const AutomatedPhaseSystemDashboard: React.FC<AutomatedPhaseSystemDashboardProps> = ({
  user,
  isAdmin
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'analytics' | 'commissions' | 'notifications' | 'settings'>('overview');
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [automationService] = useState(() => PhaseAutomationService.getInstance());

  useEffect(() => {
    if (isAdmin) {
      loadSystemStatus();
      
      // Set up periodic status updates
      const interval = setInterval(loadSystemStatus, 30000); // Update every 30 seconds
      return () => clearInterval(interval);
    }
  }, [isAdmin]);

  const loadSystemStatus = async () => {
    try {
      // Get current phase
      const currentPhase = await automationService.getCurrentPhase();
      
      // Get system settings
      const { data: settings, error: settingsError } = await supabase
        .from('system_settings')
        .select('key, value')
        .in('key', ['phase_automation_enabled', 'commission_automation_enabled']);

      if (settingsError) throw settingsError;

      const settingsMap = settings?.reduce((acc, setting) => {
        acc[setting.key] = setting.value === 'true';
        return acc;
      }, {} as Record<string, boolean>) || {};

      // Get pending transitions count
      const { data: pendingTransitions, error: pendingError } = await supabase
        .from('phase_transition_log')
        .select('id')
        .is('transition_completed_at', null);

      if (pendingError) throw pendingError;

      // Calculate system health
      let systemHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
      
      if (!settingsMap.phase_automation_enabled || !settingsMap.commission_automation_enabled) {
        systemHealth = 'warning';
      }
      
      if (currentPhase && currentPhase.completion_percentage > 95 && !settingsMap.phase_automation_enabled) {
        systemHealth = 'critical';
      }

      // Get last automation run
      const { data: lastRun, error: lastRunError } = await supabase
        .from('phase_transition_log')
        .select('transition_completed_at')
        .not('transition_completed_at', 'is', null)
        .order('transition_completed_at', { ascending: false })
        .limit(1)
        .single();

      setSystemStatus({
        phaseAutomationEnabled: settingsMap.phase_automation_enabled || false,
        commissionAutomationEnabled: settingsMap.commission_automation_enabled || false,
        currentPhase,
        nextPhaseETA: currentPhase ? calculateNextPhaseETA(currentPhase) : 'N/A',
        pendingTransitions: pendingTransitions?.length || 0,
        systemHealth,
        lastAutomationRun: lastRun?.transition_completed_at || 'Never'
      });

    } catch (error) {
      console.error('Error loading system status:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateNextPhaseETA = (currentPhase: any): string => {
    if (!currentPhase) return 'N/A';
    
    const remainingShares = currentPhase.total_shares_available - currentPhase.shares_sold;
    const dailyVelocity = 100; // Placeholder - would be calculated from actual data
    
    if (dailyVelocity > 0) {
      const daysRemaining = Math.ceil(remainingShares / dailyVelocity);
      const etaDate = new Date();
      etaDate.setDate(etaDate.getDate() + daysRemaining);
      return etaDate.toLocaleDateString();
    }
    
    return 'Unknown';
  };

  const toggleSystemAutomation = async (type: 'phase' | 'commission') => {
    try {
      const key = type === 'phase' ? 'phase_automation_enabled' : 'commission_automation_enabled';
      const currentValue = type === 'phase' ? systemStatus?.phaseAutomationEnabled : systemStatus?.commissionAutomationEnabled;
      const newValue = !currentValue;

      await supabase
        .from('system_settings')
        .upsert({
          key,
          value: newValue.toString(),
          updated_at: new Date().toISOString()
        });

      // Update automation service
      if (type === 'phase') {
        automationService.setAutomationEnabled(newValue);
      }

      // Log admin action
      await supabase.from('admin_actions').insert({
        admin_user_id: user.database_user?.id,
        action_type: `${type}_automation_toggle`,
        description: `${type} automation ${newValue ? 'enabled' : 'disabled'}`,
        performed_at: new Date().toISOString()
      });

      // Reload status
      await loadSystemStatus();

    } catch (error) {
      console.error('Error toggling automation:', error);
    }
  };

  const runSystemDiagnostics = async () => {
    try {
      setLoading(true);
      
      // Run comprehensive system checks
      const diagnostics = {
        phaseTransitionCheck: await automationService.checkPhaseTransition(),
        databaseConnectivity: true, // Would implement actual check
        commissionProcessing: true, // Would implement actual check
        notificationSystem: true // Would implement actual check
      };

      alert(`System Diagnostics Complete:
        ✅ Phase Transition: ${diagnostics.phaseTransitionCheck ? 'Ready' : 'Not Ready'}
        ✅ Database: ${diagnostics.databaseConnectivity ? 'Connected' : 'Error'}
        ✅ Commissions: ${diagnostics.commissionProcessing ? 'Active' : 'Error'}
        ✅ Notifications: ${diagnostics.notificationSystem ? 'Active' : 'Error'}`);

    } catch (error) {
      console.error('Error running diagnostics:', error);
      alert('Error running system diagnostics. Check console for details.');
    } finally {
      setLoading(false);
    }
  };

  if (!isAdmin) {
    return (
      <div style={{
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        border: '1px solid rgba(239, 68, 68, 0.3)',
        borderRadius: '12px',
        padding: '24px',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔒</div>
        <h3 style={{ color: '#ef4444', fontSize: '18px', marginBottom: '8px' }}>
          Access Denied
        </h3>
        <p style={{ color: '#9ca3af', fontSize: '14px' }}>
          This dashboard is only accessible to administrators.
        </p>
      </div>
    );
  }

  if (loading && !systemStatus) {
    return (
      <div style={{
        backgroundColor: 'rgba(31, 41, 55, 0.9)',
        borderRadius: '12px',
        padding: '24px',
        border: '1px solid #374151',
        textAlign: 'center'
      }}>
        <div style={{ color: '#9ca3af' }}>Loading automated phase system...</div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: '🏠 Overview', desc: 'System status and controls' },
    { id: 'analytics', label: '📊 Analytics', desc: 'Phase performance insights' },
    { id: 'commissions', label: '💰 Commissions', desc: 'Automated commission rules' },
    { id: 'notifications', label: '🔔 Notifications', desc: 'Smart notification system' },
    { id: 'settings', label: '⚙️ Settings', desc: 'System configuration' }
  ];

  const renderOverview = () => (
    <div>
      {/* System Status Header */}
      <div style={{
        backgroundColor: systemStatus?.systemHealth === 'healthy' ? 'rgba(16, 185, 129, 0.1)' :
                         systemStatus?.systemHealth === 'warning' ? 'rgba(245, 158, 11, 0.1)' :
                         'rgba(239, 68, 68, 0.1)',
        border: systemStatus?.systemHealth === 'healthy' ? '1px solid rgba(16, 185, 129, 0.3)' :
                systemStatus?.systemHealth === 'warning' ? '1px solid rgba(245, 158, 11, 0.3)' :
                '1px solid rgba(239, 68, 68, 0.3)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '24px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h4 style={{
            color: systemStatus?.systemHealth === 'healthy' ? '#10b981' :
                   systemStatus?.systemHealth === 'warning' ? '#f59e0b' : '#ef4444',
            fontSize: '18px',
            fontWeight: 'bold',
            margin: 0
          }}>
            🤖 System Status: {systemStatus?.systemHealth?.toUpperCase()}
          </h4>
          
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={runSystemDiagnostics}
              disabled={loading}
              style={{
                padding: '8px 16px',
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                border: '1px solid #3b82f6',
                borderRadius: '8px',
                color: '#60a5fa',
                fontSize: '12px',
                cursor: loading ? 'not-allowed' : 'pointer'
              }}
            >
              {loading ? '⏳ Running...' : '🔍 Run Diagnostics'}
            </button>
          </div>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>
              {systemStatus?.phaseAutomationEnabled ? '✅' : '❌'}
            </div>
            <div style={{ color: '#f3f4f6', fontSize: '14px', fontWeight: '600' }}>
              Phase Automation
            </div>
            <button
              onClick={() => toggleSystemAutomation('phase')}
              style={{
                marginTop: '8px',
                padding: '4px 8px',
                backgroundColor: systemStatus?.phaseAutomationEnabled ? 'rgba(239, 68, 68, 0.2)' : 'rgba(16, 185, 129, 0.2)',
                border: systemStatus?.phaseAutomationEnabled ? '1px solid #ef4444' : '1px solid #10b981',
                borderRadius: '4px',
                color: systemStatus?.phaseAutomationEnabled ? '#ef4444' : '#10b981',
                fontSize: '10px',
                cursor: 'pointer'
              }}
            >
              {systemStatus?.phaseAutomationEnabled ? 'Disable' : 'Enable'}
            </button>
          </div>

          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>
              {systemStatus?.commissionAutomationEnabled ? '✅' : '❌'}
            </div>
            <div style={{ color: '#f3f4f6', fontSize: '14px', fontWeight: '600' }}>
              Commission Automation
            </div>
            <button
              onClick={() => toggleSystemAutomation('commission')}
              style={{
                marginTop: '8px',
                padding: '4px 8px',
                backgroundColor: systemStatus?.commissionAutomationEnabled ? 'rgba(239, 68, 68, 0.2)' : 'rgba(16, 185, 129, 0.2)',
                border: systemStatus?.commissionAutomationEnabled ? '1px solid #ef4444' : '1px solid #10b981',
                borderRadius: '4px',
                color: systemStatus?.commissionAutomationEnabled ? '#ef4444' : '#10b981',
                fontSize: '10px',
                cursor: 'pointer'
              }}
            >
              {systemStatus?.commissionAutomationEnabled ? 'Disable' : 'Enable'}
            </button>
          </div>

          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#60a5fa', fontSize: '18px', fontWeight: 'bold' }}>
              {systemStatus?.currentPhase?.phase_name || 'N/A'}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Current Phase</div>
          </div>

          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold' }}>
              {systemStatus?.nextPhaseETA}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Next Phase ETA</div>
          </div>

          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#a78bfa', fontSize: '18px', fontWeight: 'bold' }}>
              {systemStatus?.pendingTransitions}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Pending Transitions</div>
          </div>

          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#22c55e', fontSize: '14px', fontWeight: 'bold' }}>
              {systemStatus?.lastAutomationRun !== 'Never' ? 
                new Date(systemStatus?.lastAutomationRun || '').toLocaleDateString() : 
                'Never'
              }
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Last Automation</div>
          </div>
        </div>
      </div>

      {/* Current Phase Details */}
      {systemStatus?.currentPhase && (
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '20px',
          marginBottom: '24px'
        }}>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
            🎯 Current Phase: {systemStatus.currentPhase.phase_name}
          </h4>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '16px',
            marginBottom: '16px'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#60a5fa', fontSize: '20px', fontWeight: 'bold' }}>
                ${systemStatus.currentPhase.price_per_share}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Price per Share</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#10b981', fontSize: '20px', fontWeight: 'bold' }}>
                {((systemStatus.currentPhase.shares_sold / systemStatus.currentPhase.total_shares_available) * 100).toFixed(1)}%
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Completion</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#f59e0b', fontSize: '20px', fontWeight: 'bold' }}>
                {systemStatus.currentPhase.shares_sold.toLocaleString()}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Shares Sold</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#a78bfa', fontSize: '20px', fontWeight: 'bold' }}>
                {(systemStatus.currentPhase.total_shares_available - systemStatus.currentPhase.shares_sold).toLocaleString()}
              </div>
              <div style={{ color: '#9ca3af', fontSize: '12px' }}>Remaining</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div style={{
            width: '100%',
            height: '12px',
            backgroundColor: 'rgba(75, 85, 99, 0.5)',
            borderRadius: '6px',
            overflow: 'hidden'
          }}>
            <div style={{
              width: `${(systemStatus.currentPhase.shares_sold / systemStatus.currentPhase.total_shares_available) * 100}%`,
              height: '100%',
              background: 'linear-gradient(90deg, #3b82f6 0%, #10b981 100%)',
              borderRadius: '6px',
              transition: 'width 0.3s ease'
            }} />
          </div>
        </div>
      )}

      {/* Commission Structure Display */}
      <div style={{
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        border: '1px solid rgba(16, 185, 129, 0.3)',
        borderRadius: '8px',
        padding: '20px'
      }}>
        <h4 style={{ color: '#10b981', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
          💎 Automated Commission Structure (15% USDT + 15% Shares)
        </h4>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
          gap: '12px',
          fontSize: '14px'
        }}>
          {[
            { level: 1, usdt: '15%', shares: '15%', desc: 'Direct Referrals' },
            { level: 2, usdt: '7.5%', shares: '7.5%', desc: '2nd Level' },
            { level: 3, usdt: '4.5%', shares: '4.5%', desc: '3rd Level' },
            { level: 4, usdt: '3%', shares: '3%', desc: '4th Level' },
            { level: 5, usdt: '1.5%', shares: '1.5%', desc: '5th Level' }
          ].map((item, index) => (
            <div key={index} style={{ textAlign: 'center' }}>
              <div style={{ color: '#10b981', fontSize: '16px', fontWeight: 'bold' }}>
                Level {item.level}
              </div>
              <div style={{ color: '#d1d5db', fontSize: '13px' }}>
                {item.usdt} USDT + {item.shares} Shares
              </div>
              <div style={{ color: '#9ca3af', fontSize: '11px' }}>
                {item.desc}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '16px',
      padding: '32px',
      marginBottom: '32px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <h2 style={{ 
          fontSize: '28px', 
          fontWeight: 'bold', 
          color: 'white', 
          margin: '0 0 12px 0',
          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          🤖 Automated Phase System
        </h2>
        <p style={{ color: '#9ca3af', fontSize: '16px', margin: 0 }}>
          Intelligent phase management with automated transitions and commission distribution
        </p>
      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        gap: '8px',
        marginBottom: '32px',
        borderBottom: '1px solid #374151',
        paddingBottom: '16px',
        flexWrap: 'wrap'
      }}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            style={{
              flex: 1,
              minWidth: '150px',
              padding: '16px 12px',
              backgroundColor: activeTab === tab.id ? 'rgba(59, 130, 246, 0.2)' : 'transparent',
              border: activeTab === tab.id ? '1px solid #3b82f6' : '1px solid #374151',
              borderRadius: '12px',
              color: activeTab === tab.id ? '#60a5fa' : '#9ca3af',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              textAlign: 'center',
              transition: 'all 0.2s ease'
            }}
          >
            <div style={{ marginBottom: '4px' }}>{tab.label}</div>
            <div style={{ fontSize: '11px', opacity: 0.8 }}>{tab.desc}</div>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'analytics' && <PhaseAnalyticsDashboard isAdmin={isAdmin} />}
        {activeTab === 'commissions' && <CommissionAutomationDashboard isAdmin={isAdmin} />}
        {activeTab === 'notifications' && (
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <SmartNotificationSystem userId={user.database_user?.id || 0} showPreferences={true} />
          </div>
        )}
        {activeTab === 'settings' && (
          <div style={{
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            borderRadius: '8px',
            padding: '20px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚙️</div>
            <h3 style={{ color: '#f3f4f6', fontSize: '18px', marginBottom: '8px' }}>
              Advanced Settings
            </h3>
            <p style={{ color: '#9ca3af', fontSize: '14px' }}>
              Advanced system configuration options will be available here.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
