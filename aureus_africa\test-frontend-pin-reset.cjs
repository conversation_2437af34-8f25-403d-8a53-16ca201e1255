#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupTestPinForFrontend() {
  console.log('🎯 Setting up test PIN for frontend testing...');
  
  const testEmail = '<EMAIL>';
  const testPin = '999888'; // Easy to remember test PIN
  
  try {
    // Set up a test PIN that can be used in the frontend
    console.log('📝 Creating test PIN for frontend...');
    
    const pinHash = await bcrypt.hash(testPin, 12);
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour for testing
    const resetData = `PIN:${pinHash}:0`;
    
    const { error: setupError } = await supabase
      .from('users')
      .update({
        reset_token: resetData,
        reset_token_expires: expiresAt.toISOString()
      })
      .eq('email', testEmail);
    
    if (setupError) {
      console.error('❌ Setup error:', setupError);
      return;
    }
    
    console.log('✅ Test PIN set up successfully for frontend testing!');
    console.log('');
    console.log('🔗 Frontend Testing Instructions:');
    console.log('1. Go to: http://localhost:8004/login');
    console.log('2. Click "Login (Web)" tab');
    console.log('3. Click "Forgot your password?"');
    console.log('4. Enter email: <EMAIL>');
    console.log('5. Skip the email step (already set up)');
    console.log('6. Enter PIN: 999888');
    console.log('7. Set a new password');
    console.log('8. Test login with new password');
    console.log('');
    console.log('⏰ This test PIN expires in 1 hour');
    console.log('🔑 Test PIN: 999888');
    console.log('📧 Test Email: <EMAIL>');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

setupTestPinForFrontend();
