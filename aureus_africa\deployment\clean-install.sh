#!/bin/bash

# Aureus Alliance - Clean Install Script
# This completely replaces your public_html with the website

set -e

echo "🧹 CLEAN INSTALL: Aureus Alliance Website"
echo "⚠️  This will REPLACE everything in public_html!"
echo ""

# Confirm with user
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Installation cancelled."
    exit 1
fi

echo "🚀 Starting clean installation..."

# Navigate to public_html
cd ~/public_html

# Backup existing files (just in case)
if [ "$(ls -A .)" ]; then
    echo "📦 Creating backup of existing files..."
    mkdir -p ~/backups
    tar -czf ~/backups/public_html_backup_$(date +%Y%m%d_%H%M%S).tar.gz .
    echo "✅ Backup created in ~/backups/"
fi

# Clear public_html completely
echo "🧹 Clearing public_html..."
rm -rf .* * 2>/dev/null || true

# Initialize git repository
echo "📥 Setting up Git repository..."
git init
git remote add origin https://github.com/JPRademeyer84/aureus_africa.git
git pull origin main

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the website
echo "🏗️ Building website..."
npm run build

# Copy built files to public_html root
echo "📁 Copying website files to root..."
cp -r dist/* ./

# Create environment file
echo "⚙️ Creating environment configuration..."
cat > .env << 'EOF'
NODE_ENV=production
PORT=8002
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_APP_NAME=Aureus Alliance Holdings
RESEND_API_KEY=your_resend_api_key_here
RESEND_FROM_EMAIL=<EMAIL>
EOF

# Create .htaccess for React Router
echo "🔧 Creating .htaccess for React Router..."
cat > .htaccess << 'EOF'
RewriteEngine On

# Handle API routes - proxy to Node.js server
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^api/(.*)$ http://localhost:8002/api/$1 [P,L]

# Handle React Router - serve index.html for all other routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Security headers
Header always set X-Frame-Options DENY
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"
EOF

# Start the Node.js server
echo "🚀 Starting Node.js server..."
if command -v pm2 &> /dev/null; then
    pm2 start server.js --name aureus-alliance
    pm2 save
    echo "✅ Server started with PM2"
else
    echo "⚠️  PM2 not found. Install PM2: npm install -g pm2"
fi

echo ""
echo "🎉 CLEAN INSTALL COMPLETE!"
echo ""
echo "📝 Next steps:"
echo "1. Edit .env file with your actual API keys"
echo "2. Visit your domain to see your website"
echo "3. Check PM2 status: pm2 status"
echo ""
echo "🌐 Your website is now live at: http://yourdomain.com"
