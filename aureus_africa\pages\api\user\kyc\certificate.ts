import { NextApiRequest, NextApiResponse } from 'next';
import { KYCService } from '../../../../lib/services/kycService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get user from session/auth
    const userId = parseInt(req.headers['x-user-id'] as string);
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    switch (req.method) {
      case 'POST':
        return await handleRequestCertificate(req, res, userId);
      case 'GET':
        return await handleGetCertificateStatus(req, res, userId);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Certificate API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleRequestCertificate(req: NextApiRequest, res: NextApiResponse, userId: number) {
  try {
    // Check if user has completed KYC
    const kycData = await KYCService.getKYCByUserId(userId);
    
    if (!kycData) {
      return res.status(404).json({ error: 'KYC information not found' });
    }

    if (kycData.kyc_status !== 'completed') {
      return res.status(400).json({ error: 'KYC must be completed before requesting certificate' });
    }

    if (kycData.certificate_requested) {
      return res.status(409).json({ error: 'Certificate already requested' });
    }

    // Request certificate
    const success = await KYCService.requestCertificate(userId);
    
    if (!success) {
      return res.status(500).json({ error: 'Failed to request certificate' });
    }

    return res.status(200).json({ 
      message: 'Certificate requested successfully',
      estimatedDelivery: '48-72 hours'
    });
  } catch (error) {
    console.error('Error requesting certificate:', error);
    return res.status(500).json({ error: 'Failed to request certificate' });
  }
}

async function handleGetCertificateStatus(req: NextApiRequest, res: NextApiResponse, userId: number) {
  try {
    const kycData = await KYCService.getKYCByUserId(userId);
    
    if (!kycData) {
      return res.status(404).json({ error: 'KYC information not found' });
    }

    const timeline = await KYCService.getCertificateTimeline();

    const response = {
      certificateRequested: kycData.certificate_requested,
      certificateGenerated: !!kycData.certificate_generated_at,
      certificateSent: !!kycData.certificate_sent_at,
      generatedAt: kycData.certificate_generated_at,
      sentAt: kycData.certificate_sent_at,
      estimatedTimeline: timeline,
      status: kycData.certificate_sent_at 
        ? 'sent' 
        : kycData.certificate_generated_at 
        ? 'generated' 
        : kycData.certificate_requested 
        ? 'processing' 
        : 'not_requested'
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('Error getting certificate status:', error);
    return res.status(500).json({ error: 'Failed to get certificate status' });
  }
}
