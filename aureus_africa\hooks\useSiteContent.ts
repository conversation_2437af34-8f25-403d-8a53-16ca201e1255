import { useState, useEffect } from 'react';
import { getSiteContent } from '../lib/supabase';

interface SiteContentHook {
  content: Record<string, any>;
  loading: boolean;
  error: string | null;
  getContent: (section: string, key: string, defaultValue?: any) => any;
  refreshContent: () => Promise<void>;
}

export const useSiteContent = (sections?: string[]): SiteContentHook => {
  const [content, setContent] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadContent = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Loading site content...');
      const data = await getSiteContent();
      console.log('📄 Site content loaded:', data);

      if (data && Array.isArray(data)) {
        const contentMap: Record<string, any> = {};

        data.forEach((item: any) => {
          if (item && item.section && item.key) {
            const sectionKey = `${item.section}.${item.key}`;
            try {
              // Parse JSON value, fallback to string if parsing fails
              contentMap[sectionKey] = typeof item.value === 'string'
                ? JSON.parse(item.value)
                : item.value;
            } catch {
              contentMap[sectionKey] = item.value;
            }
          }
        });

        setContent(contentMap);
      } else {
        // If no data or invalid data, just set empty content (don't error)
        setContent({});
      }
    } catch (err) {
      console.warn('⚠️ Site content not available, using defaults:', err);
      setError(null); // Don't set error, just use defaults
      setContent({}); // Set empty content so defaults are used
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadContent();
  }, []);

  const getContent = (section: string, key: string, defaultValue: any = '') => {
    const contentKey = `${section}.${key}`;
    return content[contentKey] || defaultValue;
  };

  const refreshContent = async () => {
    await loadContent();
  };

  return {
    content,
    loading,
    error,
    getContent,
    refreshContent
  };
};
