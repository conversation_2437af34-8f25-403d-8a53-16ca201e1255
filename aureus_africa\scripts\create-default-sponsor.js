import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Simple hash function for password (same as in lib/supabase.ts)
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password + 'aureus_salt_2024');
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

async function createDefaultSponsor() {
  try {
    console.log('🔍 Checking if TTTFOUNDER user exists...');
    
    // Check if TTTFOUNDER already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('*')
      .eq('username', 'TTTFOUNDER')
      .single();
    
    if (existingUser) {
      console.log('✅ TTTFOUNDER user already exists:', existingUser);
      return;
    }
    
    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ Error checking for existing user:', checkError);
      return;
    }
    
    console.log('📝 Creating TTTFOUNDER default sponsor user...');
    
    // Create the default sponsor user
    const passwordHash = await hashPassword('DefaultSponsor2024!');
    
    const { data: newUser, error: createError } = await supabase
      .from('users')
      .insert({
        username: 'TTTFOUNDER',
        email: '<EMAIL>',
        password_hash: passwordHash,
        full_name: 'TTT Founder',
        is_active: true,
        is_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (createError) {
      console.error('❌ Error creating TTTFOUNDER user:', createError);
      return;
    }
    
    console.log('✅ TTTFOUNDER default sponsor user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('👤 Username: TTTFOUNDER');
    console.log('🆔 User ID:', newUser.id);
    
    // Also create Supabase auth user for completeness
    console.log('🔐 Creating Supabase auth user for TTTFOUNDER...');
    
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'DefaultSponsor2024!',
      email_confirm: true,
      user_metadata: {
        full_name: 'TTT Founder',
        username: 'TTTFOUNDER',
        user_id: newUser.id,
        is_default_sponsor: true
      }
    });
    
    if (authError) {
      console.warn('⚠️ Could not create auth user (this is okay):', authError.message);
    } else {
      console.log('✅ Supabase auth user created for TTTFOUNDER');
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

createDefaultSponsor();
