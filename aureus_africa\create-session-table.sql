-- CREATE USER SESSIONS TABLE
-- This table stores secure session data for user authentication

-- Create user_sessions table
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(64) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    user_email VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    invalidated_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraint
    CONSTRAINT fk_user_sessions_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES public.users(id) 
        ON DELETE CASCADE,
    
    -- Indexes for performance
    CONSTRAINT idx_session_id UNIQUE (session_id),
    CONSTRAINT idx_user_sessions_user_id_active 
        UNIQUE (user_id, session_id) DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON public.user_sessions(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON public.user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON public.user_sessions(last_activity);

-- Enable Row Level Security
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own sessions
CREATE POLICY "user_sessions_user_policy" ON public.user_sessions
    FOR ALL USING (
        user_id = (auth.jwt() ->> 'sub')::integer OR
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE user_id = (auth.jwt() ->> 'sub')::integer 
            AND is_active = true
        ) OR
        current_setting('role') = 'service_role'
    );

-- Create function to automatically clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions() RETURNS void AS $$
BEGIN
    UPDATE public.user_sessions 
    SET is_active = false, invalidated_at = NOW()
    WHERE expires_at < NOW() AND is_active = true;
    
    -- Log cleanup activity
    INSERT INTO admin_audit_logs (
        admin_email,
        action,
        target_type,
        target_id,
        metadata,
        created_at
    ) VALUES (
        'session_cleanup',
        'EXPIRED_SESSIONS_CLEANED',
        'session_maintenance',
        'automatic',
        jsonb_build_object(
            'cleanup_time', NOW(),
            'expired_sessions_count', (
                SELECT COUNT(*) FROM public.user_sessions 
                WHERE expires_at < NOW() AND is_active = false 
                AND invalidated_at > NOW() - INTERVAL '1 minute'
            )
        ),
        NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update last_activity
CREATE OR REPLACE FUNCTION update_session_activity() RETURNS TRIGGER AS $$
BEGIN
    NEW.last_activity = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_session_activity
    BEFORE UPDATE ON public.user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_session_activity();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.user_sessions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_sessions TO service_role;
GRANT USAGE ON SEQUENCE user_sessions_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE user_sessions_id_seq TO service_role;

-- Log table creation
INSERT INTO admin_audit_logs (
    admin_email,
    action,
    target_type,
    target_id,
    metadata,
    created_at
) VALUES (
    'security_system',
    'SESSION_TABLE_CREATED',
    'database_schema',
    'user_sessions',
    jsonb_build_object(
        'table_name', 'user_sessions',
        'creation_date', NOW(),
        'security_features', ARRAY['RLS_enabled', 'automatic_cleanup', 'activity_tracking'],
        'purpose', 'secure_session_management'
    ),
    NOW()
);

-- Create a view for session statistics (admin only)
CREATE OR REPLACE VIEW session_statistics AS
SELECT 
    COUNT(*) as total_sessions,
    COUNT(*) FILTER (WHERE is_active = true) as active_sessions,
    COUNT(*) FILTER (WHERE is_active = false) as inactive_sessions,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(EXTRACT(EPOCH FROM (last_activity - created_at))/3600) as avg_session_duration_hours,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '24 hours') as sessions_last_24h,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '1 hour') as sessions_last_hour
FROM public.user_sessions
WHERE created_at > NOW() - INTERVAL '7 days';

-- Grant view access to admins only
GRANT SELECT ON session_statistics TO service_role;

COMMENT ON TABLE public.user_sessions IS 'Secure session management table with automatic cleanup and activity tracking';
COMMENT ON COLUMN public.user_sessions.session_id IS 'Cryptographically secure session identifier';
COMMENT ON COLUMN public.user_sessions.expires_at IS 'Session expiration time (24 hours from creation)';
COMMENT ON COLUMN public.user_sessions.last_activity IS 'Last time session was used (updated automatically)';
COMMENT ON COLUMN public.user_sessions.is_active IS 'Whether session is currently active';
COMMENT ON COLUMN public.user_sessions.invalidated_at IS 'When session was manually invalidated (logout)';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'User sessions table created successfully with security features:';
    RAISE NOTICE '- Row Level Security enabled';
    RAISE NOTICE '- Automatic session cleanup';
    RAISE NOTICE '- Activity tracking';
    RAISE NOTICE '- Performance indexes';
    RAISE NOTICE '- Admin statistics view';
END $$;
