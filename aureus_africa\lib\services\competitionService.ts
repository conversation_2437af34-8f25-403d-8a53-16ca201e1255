import { supabase, getServiceRoleClient } from '../supabase';

export interface Competition {
  id: string;
  name: string;
  description: string;
  phase_id: number;
  start_date: string;
  end_date?: string;
  is_active: boolean;
  minimum_qualification_amount: number;
  total_prize_pool: number;
  max_participants?: number;
  status: 'upcoming' | 'active' | 'ended' | 'cancelled';
  total_participants: number;
  qualified_participants: number;
  created_at: string;
  updated_at: string;
}

export interface PrizeTier {
  id: string;
  competition_id: string;
  tier_name: string;
  tier_rank_start: number;
  tier_rank_end: number;
  prize_amount: number;
  prize_type: string;
  display_order: number;
  emoji: string;
}

export interface CompetitionParticipant {
  id: string;
  competition_id: string;
  user_id: number;
  username?: string;
  full_name?: string;
  total_referral_volume: number;
  direct_referrals_count: number;
  qualified_referrals_count: number;
  is_qualified: boolean;
  current_rank?: number;
  final_rank?: number;
  joined_at: string;
  last_updated: string;
}

export interface LeaderboardEntry {
  competition_id: string;
  user_id: number;
  username: string;
  full_name: string;
  total_referral_volume: number;
  direct_referrals_count: number;
  qualified_referrals_count: number;
  is_qualified: boolean;
  current_rank?: number;
  calculated_rank: number;
  joined_at: string;
}

export interface CompetitionStats {
  totalParticipants: number;
  qualifiedParticipants: number;
  leadingVolume: number;
  totalPrizePool: number;
  minimumQualification: number;
}

class CompetitionService {
  private supabase = getServiceRoleClient();

  /**
   * Get the current active competition
   */
  async getCurrentCompetition(): Promise<Competition | null> {
    try {
      const { data, error } = await this.supabase
        .from('competitions')
        .select('*')
        .eq('is_active', true)
        .eq('status', 'active')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error('Error fetching current competition:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getCurrentCompetition:', error);
      return null;
    }
  }

  /**
   * Get prize tiers for a competition
   */
  async getPrizeTiers(competitionId: string): Promise<PrizeTier[]> {
    try {
      const { data, error } = await this.supabase
        .from('competition_prize_tiers')
        .select('*')
        .eq('competition_id', competitionId)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching prize tiers:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getPrizeTiers:', error);
      return [];
    }
  }

  /**
   * Get leaderboard for a competition
   */
  async getLeaderboard(competitionId: string, limit: number = 10): Promise<LeaderboardEntry[]> {
    try {
      const { data, error } = await this.supabase
        .from('competition_leaderboard')
        .select('*')
        .eq('competition_id', competitionId)
        .order('calculated_rank', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error fetching leaderboard:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getLeaderboard:', error);
      return [];
    }
  }

  /**
   * Get competition statistics
   */
  async getCompetitionStats(competitionId: string): Promise<CompetitionStats> {
    try {
      // Get basic competition info
      const { data: competition, error: compError } = await this.supabase
        .from('competitions')
        .select('total_prize_pool, minimum_qualification_amount')
        .eq('id', competitionId)
        .single();

      if (compError) {
        console.error('Error fetching competition info:', compError);
        return {
          totalParticipants: 0,
          qualifiedParticipants: 0,
          leadingVolume: 0,
          totalPrizePool: 0,
          minimumQualification: 2500
        };
      }

      // Get participant statistics
      const { data: stats, error: statsError } = await this.supabase
        .from('competition_participants')
        .select('total_referral_volume, is_qualified')
        .eq('competition_id', competitionId);

      if (statsError) {
        console.error('Error fetching participant stats:', statsError);
        return {
          totalParticipants: 0,
          qualifiedParticipants: 0,
          leadingVolume: 0,
          totalPrizePool: competition.total_prize_pool || 0,
          minimumQualification: competition.minimum_qualification_amount || 2500
        };
      }

      const totalParticipants = stats?.length || 0;
      const qualifiedParticipants = stats?.filter(p => p.is_qualified).length || 0;
      const leadingVolume = Math.max(...(stats?.map(p => p.total_referral_volume) || [0]));

      return {
        totalParticipants,
        qualifiedParticipants,
        leadingVolume,
        totalPrizePool: competition.total_prize_pool || 0,
        minimumQualification: competition.minimum_qualification_amount || 2500
      };
    } catch (error) {
      console.error('Error in getCompetitionStats:', error);
      return {
        totalParticipants: 0,
        qualifiedParticipants: 0,
        leadingVolume: 0,
        totalPrizePool: 0,
        minimumQualification: 2500
      };
    }
  }

  /**
   * Add or update a participant in the competition
   */
  async updateParticipant(
    competitionId: string, 
    userId: number, 
    referralVolume: number,
    directReferrals: number = 0,
    qualifiedReferrals: number = 0
  ): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('competition_participants')
        .upsert({
          competition_id: competitionId,
          user_id: userId,
          total_referral_volume: referralVolume,
          direct_referrals_count: directReferrals,
          qualified_referrals_count: qualifiedReferrals,
          last_updated: new Date().toISOString()
        }, {
          onConflict: 'competition_id,user_id'
        });

      if (error) {
        console.error('Error updating participant:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateParticipant:', error);
      return false;
    }
  }

  /**
   * Create a new competition for a phase
   * This creates a competition that rewards network leaders based on referral sales volume
   */
  async createCompetition(
    name: string,
    description: string,
    phaseId: number,
    totalPrizePool: number,
    minimumQualification: number = 2500,
    endDate?: string
  ): Promise<string | null> {
    try {
      // First, deactivate any existing active competitions for this phase
      await this.supabase
        .from('competitions')
        .update({ is_active: false, status: 'ended' })
        .eq('phase_id', phaseId)
        .eq('is_active', true);

      const { data, error } = await this.supabase
        .from('competitions')
        .insert({
          name,
          description,
          phase_id: phaseId,
          start_date: new Date().toISOString(),
          end_date: endDate,
          minimum_qualification_amount: minimumQualification,
          total_prize_pool: totalPrizePool,
          status: 'active',
          is_active: true
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating competition:', error);
        return null;
      }

      console.log(`✅ Created competition "${name}" for phase ${phaseId}`);
      return data.id;
    } catch (error) {
      console.error('Error in createCompetition:', error);
      return null;
    }
  }

  /**
   * Get all competitions (for admin interface)
   */
  async getAllCompetitions(): Promise<Competition[]> {
    try {
      const { data, error } = await this.supabase
        .from('competitions')
        .select(`
          *,
          investment_phases (
            phase_number,
            phase_name,
            price_per_share
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching all competitions:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getAllCompetitions:', error);
      return [];
    }
  }

  /**
   * Get competition by ID
   */
  async getCompetitionById(competitionId: string): Promise<Competition | null> {
    try {
      const { data, error } = await this.supabase
        .from('competitions')
        .select(`
          *,
          investment_phases (
            phase_name,
            phase_number
          )
        `)
        .eq('id', competitionId)
        .single();

      if (error) {
        console.error('Error fetching competition by ID:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getCompetitionById:', error);
      return null;
    }
  }

  /**
   * Update competition status
   */
  async updateCompetitionStatus(
    competitionId: string,
    status: 'upcoming' | 'active' | 'ended' | 'cancelled',
    endDate?: string
  ): Promise<boolean> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };

      if (endDate) {
        updateData.end_date = endDate;
      }

      if (status === 'ended' || status === 'cancelled') {
        updateData.is_active = false;
        if (!endDate) {
          updateData.end_date = new Date().toISOString();
        }
      }

      const { error } = await this.supabase
        .from('competitions')
        .update(updateData)
        .eq('id', competitionId);

      if (error) {
        console.error('Error updating competition status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateCompetitionStatus:', error);
      return false;
    }
  }

  /**
   * Setup default prize tiers for a competition
   */
  async setupDefaultPrizeTiers(competitionId: string): Promise<boolean> {
    try {
      const prizeTiers = [
        { tier_name: '1st Place', tier_rank_start: 1, tier_rank_end: 1, prize_amount: 60000, display_order: 1, emoji: '🥇' },
        { tier_name: '2nd Place', tier_rank_start: 2, tier_rank_end: 2, prize_amount: 30000, display_order: 2, emoji: '🥈' },
        { tier_name: '3rd Place', tier_rank_start: 3, tier_rank_end: 3, prize_amount: 18000, display_order: 3, emoji: '🥉' },
        { tier_name: '4th - 10th Place', tier_rank_start: 4, tier_rank_end: 10, prize_amount: 6000, display_order: 4, emoji: '🏆' }
      ];

      const { error } = await this.supabase
        .from('competition_prize_tiers')
        .insert(
          prizeTiers.map(tier => ({
            competition_id: competitionId,
            ...tier
          }))
        );

      if (error) {
        console.error('Error setting up prize tiers:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in setupDefaultPrizeTiers:', error);
      return false;
    }
  }
}

export const competitionService = new CompetitionService();
