-- Initial content setup for Aureus Alliance Holdings
-- Run this in your Supabase SQL Editor after running the main schema

-- Clear existing content (optional)
DELETE FROM site_content;

-- Hero Section Content
INSERT INTO site_content (section, key, value, updated_by) VALUES 
('hero', 'main_title', '"Secure Your Wealth with Real Gold"', 'system'),
('hero', 'subtitle', '"AUREUS ALLIANCE HOLDINGS"', 'system'),
('hero', 'description', '"A rare opportunity to purchase shares in a sustainable gold mining ecosystem, sharing profits via an NFT-backed structure"', 'system'),
('hero', 'share_price', '"5"', 'system'),
('hero', 'projected_dividend', '"134.51"', 'system'),
('hero', 'total_shares', '"1.4M"', 'system'),
('hero', 'cta_primary', '"Explore The Project"', 'system'),
('hero', 'cta_secondary', '"Try Calculator"', 'system'),
('hero', 'logo_url', '"https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png"', 'system');

-- About Section Content
INSERT INTO site_content (section, key, value, updated_by) VALUES 
('about', 'title', '"About Aureus Alliance Holdings"', 'system'),
('about', 'subtitle', '"Pioneering the convergence of traditional gold mining with digital innovation"', 'system'),
('about', 'main_description', '"Aureus Alliance Holdings is pioneering the convergence of traditional gold mining with the digital frontier of NFTs, gaming, and blockchain technology."', 'system'),
('about', 'foundation_text', '"Our foundation is built on physical gold mining operations that generate consistent revenue, providing a stable backing for our digital endeavors."', 'system'),
('about', 'ecosystem_text', '"The Aureus ecosystem includes premium collectible NFTs, an immersive MMO gaming experience, and exclusive opportunities for early shareholders."', 'system'),
('about', 'timeline_title', '"Share Purchase Timeline"', 'system');

-- Key Highlights Section Content
INSERT INTO site_content (section, key, value, updated_by) VALUES 
('highlights', 'title', '"Key Highlights & Value Proposition"', 'system'),
('highlights', 'subtitle', '"Core strengths and financial incentives that make Aureus a unique opportunity."', 'system'),
('highlights', 'presale_price', '"5"', 'system'),
('highlights', 'presale_shares', '"200000"', 'system'),
('highlights', 'annual_revenue', '"342"', 'system'),
('highlights', 'net_profit', '"188"', 'system'),
('highlights', 'projected_dividend_per_share', '"134.51"', 'system');

-- Calculator Settings
INSERT INTO site_content (section, key, value, updated_by) VALUES 
('calculator', 'title', '"Financial Calculator"', 'system'),
('calculator', 'subtitle', '"Experience the power of data-driven share ownership decisions with our interactive calculator"', 'system'),
('calculator', 'default_land_size', '"25"', 'system'),
('calculator', 'default_user_shares', '"1000"', 'system'),
('calculator', 'gold_price_default', '"100000"', 'system');

-- Contact Information
INSERT INTO site_content (section, key, value, updated_by) VALUES
('contact', 'email', '"<EMAIL>"', 'system'),
('contact', 'phone', '"+27 11 123 4567"', 'system'),
('contact', 'telegram_bot', '"https://t.me/AureusAllianceBot"', 'system'),
('contact', 'address', '"Johannesburg, South Africa"', 'system');

-- Site Settings
INSERT INTO site_content (section, key, value, updated_by) VALUES 
('settings', 'site_name', '"Aureus Alliance Holdings"', 'system'),
('settings', 'meta_description', '"Purchase shares in Aureus Alliance Holdings, a South African gold mining company. Join our ecosystem and earn dividends from real gold production through sustainable mining operations."', 'system'),
('settings', 'analytics_id', '""', 'system'),
('settings', 'logo_url', '"https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png"', 'system'),
('settings', 'favicon_url', '""', 'system');

-- Ensure the admin user exists
INSERT INTO admin_users (email, role) VALUES 
('<EMAIL>', 'super_admin')
ON CONFLICT (email) DO NOTHING;

-- Add some sample mining data if not exists
INSERT INTO mining_data (site_name, kg_produced, value_100k, value_150k, is_total, display_order, updated_by) VALUES 
('Existing 250-hectare block', 2400, *********, *********, false, 1, 'system'),
('Mutare Expansion (47 sites)', 7600, *********, 1140000000, false, 2, 'system'),
('Totals', 10000, 1000000000, 1500000000, true, 3, 'system')
ON CONFLICT DO NOTHING;
