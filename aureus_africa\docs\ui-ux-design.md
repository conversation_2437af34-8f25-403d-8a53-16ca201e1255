# 2.1 UI/UX Design - Aureus Alliance Web Dashboard

## Executive Summary
This document outlines the comprehensive UI/UX design for the Aureus Alliance Web Dashboard, including wireframes, user interface mockups, design system specifications, and user journey maps. The design maintains consistency with the existing brand identity while providing an intuitive and professional investment platform experience.

## Design Philosophy

### Core Design Principles
1. **Professional Trust**: Establish credibility for financial transactions
2. **Clarity First**: Clear information hierarchy and transparent data presentation
3. **Mobile-First**: Responsive design optimized for all devices
4. **Accessibility**: WCAG 2.1 AA compliance for inclusive access
5. **Brand Consistency**: Maintain Aureus Alliance brand identity
6. **Performance**: Optimized for fast loading and smooth interactions

### Visual Identity Analysis
**Current Brand Elements**:
- **Primary Colors**: Gold (#d4af37), Secondary Gold (#b8860b)
- **Accent Colors**: Blue (#1e40af), Cyber Blue (#06b6d4)
- **Typography**: Inter font family with professional spacing
- **Style**: Modern, professional, luxury investment aesthetic
- **Iconography**: Clean, minimal icons with consistent styling

## Wireframe Specifications

### 1. Authentication Pages

#### Login Page Wireframe
```
┌─────────────────────────────────────────────┐
│ [LOGO] Aureus Alliance                      │
│                                             │
│           Welcome Back                      │
│     Access Your Investment Portal          │
│                                             │
│   ┌───────────────────────────────────┐    │
│   │  [Telegram Icon]                  │    │
│   │  Login with Telegram              │    │
│   │  Secure OAuth Authentication      │    │
│   └───────────────────────────────────┘    │
│                                             │
│   New to Aureus Alliance?                  │
│   Learn more about our platform           │
│                                             │
│   [Footer Links] | [Legal] | [Contact]    │
└─────────────────────────────────────────────┘
```

#### Country Selection Wireframe
```
┌─────────────────────────────────────────────┐
│ [LOGO] Aureus Alliance                      │
│                                             │
│       Select Your Country                  │
│   Required for Compliance & Payments       │
│                                             │
│   ┌──────────────┐ ┌──────────────┐       │
│   │ [SA Flag]    │ │ [Globe Icon] │       │
│   │ South Africa │ │ International│       │
│   │ ZAR Payments │ │ USDT Payments│       │
│   └──────────────┘ └──────────────┘       │
│                                             │
│   □ I understand payment methods vary      │
│     by country selection                   │
│                                             │
│   [Continue] ────────────────────────────   │
└─────────────────────────────────────────────┘
```

### 2. Dashboard Layout

#### Main Dashboard Wireframe
```
┌─────────────────────────────────────────────────────────────────┐
│ [☰] Aureus Alliance         [🔔] [👤] [Settings] [Logout]      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ Welcome back, [Username]                    [Current Phase 4]   │
│ Your Investment Portfolio                   [Shares Available]  │
│                                                                 │
│ ┌──────────────┐ ┌──────────────┐ ┌──────────────┐ ┌─────────┐ │
│ │ Total Shares │ │ Commission   │ │ Portfolio    │ │ Pending │ │
│ │ 2,500        │ │ Balance      │ │ Value        │ │ KYC     │ │
│ │ $125,000     │ │ $1,234       │ │ $127,500     │ │ Review  │ │
│ └──────────────┘ └──────────────┘ └──────────────┘ └─────────┘ │
│                                                                 │
│ Quick Actions                                                   │
│ ┌──────────────┐ ┌──────────────┐ ┌──────────────┐            │
│ │ [+] Buy      │ │ [📄] Upload  │ │ [🔗] Share   │            │
│ │ More Shares  │ │ KYC Docs     │ │ Referral     │            │
│ └──────────────┘ └──────────────┘ └──────────────┘            │
│                                                                 │
│ Recent Activity                                                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ 📈 Share Purchase Approved    $5,000    Jan 15, 2025       │ │
│ │ 💰 Commission Earned          $125      Jan 14, 2025       │ │
│ │ 📄 KYC Documents Submitted    -         Jan 13, 2025       │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 3. Share Purchase Flow

#### Share Purchase Page Wireframe
```
┌─────────────────────────────────────────────────────────────────┐
│ [← Back] Purchase Shares                         Phase 4 Active │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ Current Phase Information                                       │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Phase 4: $50 per share                                      │ │
│ │ Available: 12,500 shares                                    │ │
│ │ Progress: ████████░░ 80% complete                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Purchase Details                                                │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Number of Shares: [     100     ] [+] [-]                  │ │
│ │ Price per Share:  $50                                       │ │
│ │ Total Cost:       $5,000                                    │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Payment Method (Based on Country: South Africa)                │ │
│ ● ZAR Bank Transfer                                            │ │
│ ○ Commission Balance ($1,234 available)                       │ │
│                                                                 │
│ [Continue to Payment] ─────────────────────────────────────     │
└─────────────────────────────────────────────────────────────────┘
```

#### Payment Processing Wireframe
```
┌─────────────────────────────────────────────────────────────────┐
│ [← Back] Payment Details                                        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ Order Summary                                                   │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Shares: 100 × $50 = $5,000                                 │ │
│ │ Payment Method: ZAR Bank Transfer                           │ │
│ │ Exchange Rate: 1 USD = 18.50 ZAR                          │ │
│ │ Total: R92,500                                              │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Bank Transfer Details                                           │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Bank: Absa Bank                                             │ │
│ │ Account Name: Aureus Alliance Holdings (Pty) Ltd           │ │
│ │ Account Number: **********                                  │ │
│ │ Branch Code: 632005                                         │ │
│ │ Reference: USER123-PHASE4-240115                           │ │
│ │ Amount: R92,500                                             │ │
│ │                                                             │ │
│ │ [📋 Copy Details] [📧 Email Details]                       │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Upload Proof of Payment                                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ [📎 Choose File] or Drag & Drop                            │ │
│ │ Supported: PDF, JPG, PNG (Max 10MB)                        │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ [Submit Payment] ──────────────────────────────────────────     │
└─────────────────────────────────────────────────────────────────┘
```

### 4. KYC Document Management

#### KYC Upload Wireframe
```
┌─────────────────────────────────────────────────────────────────┐
│ [← Back] KYC Document Upload                                    │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ Document Verification                                           │
│ Complete your KYC to unlock all features                       │
│                                                                 │
│ Required Documents                                              │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ ✅ Identity Document                                         │ │
│ │    [passport.pdf] [✓ Uploaded] [👁 Preview] [🗑 Remove]     │ │
│ │                                                             │ │
│ │ ⏳ Proof of Address (last 3 months)                        │ │
│ │    [📎 Upload Document]                                     │ │
│ │    Utility bill, bank statement, or lease agreement        │ │
│ │                                                             │ │
│ │ ⏳ Additional Documents (if required)                       │ │
│ │    [📎 Upload Document]                                     │ │
│ │    May be requested for large investments                   │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Upload Guidelines                                               │
│ • File types: PDF, JPG, PNG                                    │ │
│ • Maximum size: 10MB per file                                  │ │
│ • Documents must be clear and legible                          │ │
│ • Personal information must be visible                         │ │
│                                                                 │
│ [Submit for Review] ───────────────────────────────────────     │
└─────────────────────────────────────────────────────────────────┘
```

### 5. Referral System

#### Referral Dashboard Wireframe
```
┌─────────────────────────────────────────────────────────────────┐
│ [← Back] Referral Program                                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ Your Referral Performance                                       │
│ ┌──────────────┐ ┌──────────────┐ ┌──────────────┐ ┌─────────┐ │
│ │ Total        │ │ Active       │ │ Commission   │ │ This    │ │
│ │ Referrals    │ │ Referrals    │ │ Earned       │ │ Month   │ │
│ │ 12           │ │ 8            │ │ $2,450       │ │ $125    │ │
│ └──────────────┘ └──────────────┘ └──────────────┘ └─────────┘ │
│                                                                 │
│ Your Referral Link                                              │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ https://aureus.africa/ref/USER123ABC                        │ │
│ │ [📋 Copy Link] [📧 Email] [📱 Share] [📊 QR Code]          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Commission Management                                           │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Available Balance: $1,234                                   │ │
│ │ Pending Approval: $125                                      │ │
│ │ Total Earned: $2,450                                        │ │
│ │                                                             │ │
│ │ [💰 Request Withdrawal] [🔄 Convert to Shares]             │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Recent Referral Activity                                        │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ John D. purchased 200 shares    +$125    Jan 15, 2025      │ │
│ │ Sarah M. completed KYC          +$0      Jan 14, 2025      │ │
│ │ Mike R. joined platform         +$0      Jan 13, 2025      │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Design System Specifications

### Color Palette
```css
/* Primary Colors */
--aureus-gold: #d4af37;
--aureus-gold-dark: #b8860b;
--aureus-gold-light: #f4e4a6;

/* Secondary Colors */
--aureus-blue: #1e40af;
--aureus-blue-light: #3b82f6;
--aureus-cyan: #06b6d4;

/* Neutral Colors */
--text-primary: #ffffff;
--text-secondary: #e5e7eb;
--text-muted: #9ca3af;
--background-dark: #000000;
--background-card: rgba(0, 0, 0, 0.6);
--background-overlay: rgba(0, 0, 0, 0.8);

/* Status Colors */
--success: #10b981;
--warning: #f59e0b;
--error: #ef4444;
--info: #3b82f6;

/* Border Colors */
--border-subtle: rgba(255, 255, 255, 0.15);
--border-strong: rgba(255, 255, 255, 0.3);
```

### Typography Scale
```css
/* Font Families */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
--font-mono: 'Fira Code', monospace;

/* Font Sizes */
--text-xs: 0.75rem;     /* 12px */
--text-sm: 0.875rem;    /* 14px */
--text-base: 1rem;      /* 16px */
--text-lg: 1.125rem;    /* 18px */
--text-xl: 1.25rem;     /* 20px */
--text-2xl: 1.5rem;     /* 24px */
--text-3xl: 1.875rem;   /* 30px */
--text-4xl: 2.25rem;    /* 36px */
--text-5xl: 3rem;       /* 48px */

/* Font Weights */
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### Spacing System
```css
/* Spacing Scale */
--space-0: 0;
--space-1: 0.25rem;     /* 4px */
--space-2: 0.5rem;      /* 8px */
--space-3: 0.75rem;     /* 12px */
--space-4: 1rem;        /* 16px */
--space-5: 1.25rem;     /* 20px */
--space-6: 1.5rem;      /* 24px */
--space-8: 2rem;        /* 32px */
--space-10: 2.5rem;     /* 40px */
--space-12: 3rem;       /* 48px */
--space-16: 4rem;       /* 64px */
--space-20: 5rem;       /* 80px */
--space-24: 6rem;       /* 96px */
```

### Component Library

#### Button Components
```css
/* Primary Button */
.btn-primary {
  background: linear-gradient(135deg, var(--aureus-gold), var(--aureus-gold-dark));
  color: var(--background-dark);
  padding: var(--space-3) var(--space-6);
  border-radius: 8px;
  font-weight: var(--font-semibold);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

/* Secondary Button */
.btn-secondary {
  background: transparent;
  color: var(--aureus-gold);
  border: 2px solid var(--aureus-gold);
  padding: var(--space-3) var(--space-6);
  border-radius: 8px;
  font-weight: var(--font-semibold);
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--aureus-gold);
  color: var(--background-dark);
}
```

#### Card Components
```css
.card {
  background: var(--background-card);
  border: 1px solid var(--border-subtle);
  border-radius: 12px;
  padding: var(--space-6);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.card:hover {
  border-color: var(--border-strong);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.card-header {
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-subtle);
  margin-bottom: var(--space-4);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}
```

#### Form Components
```css
.form-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-subtle);
  border-radius: 8px;
  padding: var(--space-3) var(--space-4);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--aureus-gold);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-label {
  display: block;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  margin-bottom: var(--space-2);
}
```

## User Journey Maps

### Journey 1: New User Registration & First Purchase

#### User Journey Steps:
1. **Landing Page Visit**
   - User discovers Aureus Alliance website
   - Views investment calculator and mining information
   - Decides to invest in shares

2. **Registration Process**
   - Clicks "Login with Telegram" button
   - Completes Telegram OAuth authentication
   - System creates account or links existing bot user

3. **Onboarding Flow**
   - Selects country of residence (SA vs International)
   - Reviews and accepts Terms & Conditions
   - Receives welcome message and dashboard tour

4. **Dashboard Exploration**
   - Views current share phase information
   - Explores investment calculator
   - Reviews available features and documentation

5. **Share Purchase**
   - Selects number of shares to purchase
   - Chooses payment method based on country
   - Completes payment process (bank transfer or USDT)
   - Uploads proof of payment
   - Awaits admin approval

6. **Post-Purchase**
   - Receives approval notification
   - Views updated portfolio
   - Explores referral program
   - Plans future investments

#### Pain Points & Solutions:
- **Pain Point**: Complex payment process for crypto newcomers
- **Solution**: Step-by-step payment guides with visual instructions

- **Pain Point**: Waiting for admin approval uncertainty
- **Solution**: Clear status tracking with estimated approval times

- **Pain Point**: Understanding share value and returns
- **Solution**: Interactive calculator with projection scenarios

### Journey 2: Existing Bot User Migration

#### User Journey Steps:
1. **Discovery**
   - Receives notification about web dashboard
   - Clicks link from Telegram bot
   - Lands on login page

2. **Seamless Login**
   - Uses same Telegram account to login
   - System recognizes existing user
   - Automatically imports bot data

3. **Enhanced Experience**
   - Discovers improved document upload
   - Uses better referral management tools
   - Enjoys responsive mobile experience

4. **Continued Usage**
   - Uses both bot and web platform
   - Prefers web for complex tasks
   - Appreciates data synchronization

## Responsive Design Strategy

### Breakpoint System
```css
/* Mobile First Approach */
/* Base: 320px+ (Mobile) */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
}

/* 768px+ (Tablet) */
@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
}

/* 1024px+ (Desktop) */
@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-8);
  }
}

/* 1200px+ (Large Desktop) */
@media (min-width: 1200px) {
  .dashboard-grid {
    max-width: 1400px;
    margin: 0 auto;
  }
}
```

### Mobile Optimization
- **Touch Targets**: Minimum 44px for all interactive elements
- **Font Sizes**: Scalable typography with clamp() functions
- **Navigation**: Collapsible hamburger menu for mobile
- **Tables**: Horizontal scroll with sticky headers
- **Forms**: Large input fields with proper spacing

## Accessibility Compliance

### WCAG 2.1 AA Requirements
1. **Color Contrast**: 
   - Normal text: 4.5:1 minimum contrast ratio
   - Large text: 3:1 minimum contrast ratio
   - Gold on dark backgrounds meets AA standards

2. **Keyboard Navigation**:
   - All interactive elements accessible via keyboard
   - Visible focus indicators on all controls
   - Logical tab order throughout interface

3. **Screen Reader Support**:
   - Semantic HTML structure
   - ARIA labels for complex components
   - Alt text for all images and icons
   - Form labels properly associated

4. **Motion & Animation**:
   - Respect prefers-reduced-motion settings
   - No auto-playing videos or animations
   - Optional animation controls

## Usability Testing Plan

### Testing Scenarios
1. **New User Registration**
   - Complete registration from start to first purchase
   - Identify friction points and confusion areas
   - Measure completion rates and time to value

2. **Document Upload**
   - Test KYC document upload process
   - Evaluate error handling and feedback
   - Assess mobile vs desktop experience

3. **Payment Process**
   - Test both USDT and ZAR payment flows
   - Evaluate clarity of instructions
   - Measure success rates and abandonment points

4. **Navigation & Discovery**
   - Test ability to find and use key features
   - Evaluate information architecture
   - Assess dashboard organization

### Success Metrics
- **Task Completion Rate**: >95% for core functions
- **Time to Complete**: <5 minutes for share purchase
- **User Satisfaction**: >4.5/5 rating on SUS scale
- **Error Rate**: <5% for critical user flows
- **Mobile Usability**: Equal performance across devices

## Implementation Guidelines

### Design Token System
```typescript
// Design tokens for consistent implementation
export const tokens = {
  colors: {
    primary: {
      gold: '#d4af37',
      goldDark: '#b8860b',
      goldLight: '#f4e4a6'
    },
    neutral: {
      white: '#ffffff',
      gray100: '#f7fafc',
      gray900: '#1a202c',
      black: '#000000'
    }
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '3rem'
  },
  typography: {
    sizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem'
    },
    weights: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    }
  }
};
```

### Component Architecture
```typescript
// Consistent component structure
interface ComponentProps {
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  className?: string;
}

// Example: Button component
export const Button: React.FC<ComponentProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  children,
  className = '',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200';
  const variantClasses = getVariantClasses(variant);
  const sizeClasses = getSizeClasses(size);
  
  return (
    <button
      className={`${baseClasses} ${variantClasses} ${sizeClasses} ${className}`}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <Spinner />}
      {children}
    </button>
  );
};
```

## Next Steps

### Design Implementation Phase
1. **Create High-Fidelity Mockups**: Detailed visual designs for all screens
2. **Build Component Library**: Implement reusable UI components
3. **Prototype Interactive Flows**: Create clickable prototypes for testing
4. **Conduct User Testing**: Validate designs with target users
5. **Iterate Based on Feedback**: Refine designs based on user insights

### Development Handoff
1. **Design System Documentation**: Complete component specifications
2. **Asset Preparation**: Export all icons, images, and design assets
3. **Developer Guidelines**: Provide implementation best practices
4. **Quality Assurance**: Define visual acceptance criteria

---

**Design Status**: WIREFRAMES COMPLETE
**Next Phase**: High-fidelity mockups and interactive prototypes
**Design System**: Professional investment platform aesthetic established
**User Experience**: Optimized for trust, clarity, and conversion

*This design foundation ensures a professional, accessible, and user-friendly web dashboard that maintains brand consistency while providing superior user experience for investment management.*
