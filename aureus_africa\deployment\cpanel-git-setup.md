# 🚀 Clean cPanel Git Setup - Main Domain

## ✨ **Your Website on Main Domain with Auto-Updates!**

This puts your website directly on your main domain (yourdomain.com) and automatically updates when you push to GitHub.

---

## 📋 **Clean Setup Steps**

### **Step 1: Clear public_html and Set Up Git**

1. **Go to cPanel File Manager**
2. **Delete everything in public_html** (backup first if needed)
3. **Go to "Git™ Version Control"**
4. **Click "Create" button**
5. **Fill in these details:**
   - **Clone URL:** `https://github.com/JPRademeyer84/aureus_africa.git`
   - **Repository Path:** `public_html`
   - **Repository Name:** `Aureus Alliance Main Site`
6. **Click "Create"**

### **Step 2: Install Node.js and Dependencies**

**Go to cPanel Terminal** and run these commands:

```bash
# Go to your main website directory
cd ~/public_html

# Install Node.js (if not already installed)
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
sudo apt-get install -y nodejs

# Install PM2 for managing your app
npm install -g pm2

# Install website dependencies
npm install

# Build your website (this creates the dist folder)
npm run build

# Copy dist contents to public_html root
cp -r dist/* ./
```

### **Step 3: Create Environment Configuration**

```bash
# Create your environment file
nano .env
```

**Copy this into the file** (replace with your actual values):
```
NODE_ENV=production
PORT=8002
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_APP_NAME=Aureus Alliance Holdings
RESEND_API_KEY=your_resend_api_key_here
RESEND_FROM_EMAIL=<EMAIL>
```

**Save:** Press `Ctrl+X`, then `Y`, then `Enter`

### **Step 4: Start Your Website**

```bash
# Start your website with PM2
pm2 start server.js --name aureus-alliance

# Save PM2 configuration
pm2 save

# Set PM2 to start on boot
pm2 startup

# Check if it's running
pm2 status
```

### **Step 5: Set Up Auto-Deploy Hook**

**In cPanel Git Version Control:**

1. **Click on your repository name**
2. **Go to "Deploy" tab**
3. **Enable "Pull on Deploy"**
4. **In "Deploy Script" field, enter:**
   ```bash
   cd /home/<USER>/public_html && npm install && npm run build && cp -r dist/* ./ && pm2 restart aureus-alliance
   ```
5. **Click "Save"**

### **Step 6: Create .htaccess for React Router**

```bash
# Create .htaccess in public_html root
cd ~/public_html
nano .htaccess
```

**Copy this into the file:**
```apache
RewriteEngine On

# Handle API routes - proxy to Node.js server
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^api/(.*)$ http://localhost:8002/api/$1 [P,L]

# Handle React Router - serve index.html for all other routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

---

## 🎉 **How It Works**

### **Normal Development:**
1. **Make changes** to your code locally
2. **Push to GitHub:** `git push origin main`
3. **cPanel automatically:**
   - Pulls the latest code
   - Installs dependencies
   - Builds the website
   - Restarts the server
4. **Your website is updated!** 🚀

### **Manual Deploy (if needed):**
```bash
# Go to your repository
cd ~/repositories/aureus-alliance

# Run the deploy script
./deployment/deploy.sh
```

---

## 🔧 **Useful Commands**

```bash
# Check your website status
pm2 status

# View website logs
pm2 logs aureus-alliance

# Restart your website
pm2 restart aureus-alliance

# Check Git status
cd ~/repositories/aureus-alliance
git status
git log --oneline -5
```

---

## 🆘 **Troubleshooting**

### **If auto-deploy doesn't work:**
1. Check cPanel Git logs
2. Run deploy script manually: `./deployment/deploy.sh`
3. Check PM2 status: `pm2 status`

### **If website doesn't load:**
1. Check PM2 logs: `pm2 logs aureus-alliance`
2. Verify environment file: `cat .env`
3. Check if port 8002 is running: `netstat -tlnp | grep :8002`

---

## ✅ **Final Result**

- **Push to GitHub** → **Website automatically updates**
- **Professional deployment** with zero downtime
- **Easy rollback** if something goes wrong
- **Logs and monitoring** with PM2

**Your website will be available at:** `http://yourdomain.com` (your main domain)

🎉 **That's it! You now have professional auto-deploy setup!**
