import React, { useState, useEffect } from 'react'
import { supabase, getServiceRoleClient } from '../../lib/supabase'
import { UserEditModal } from './UserEditModal'
import { UserFinancialModal } from './UserFinancialModal'
import { FinancialActionsModal } from './FinancialActionsModal'
import { CommunicationModal } from './CommunicationModal'
import { BulkActionsModal } from './BulkActionsModal'
import { useConfirmationDialog } from './ConfirmationDialog'
import { withAdminAuth, logAdminAction } from '../../lib/adminAuth'

interface SharePurchase {
  id: string
  package_name: string
  shares_purchased: number
  total_amount: number
  commission_used: number
  remaining_payment: number
  payment_method: string
  status: string
  created_at: string
  updated_at: string
}

interface PaymentTransaction {
  id: string
  amount: number
  currency: string
  network: string
  status: string
  created_at: string
  payment_method?: string
}

interface CommissionBalance {
  usdt_balance: number
  share_balance: number
  total_earned_usdt: number
  total_earned_shares: number
  total_withdrawn_usdt: number
}

interface ReferralData {
  id: string
  referral_code: string
  commission_rate: number
  total_commission: number
  status: string
  created_at: string
  referrer?: {
    id: number
    username: string
    full_name: string | null
  }
  referred?: {
    id: number
    username: string
    full_name: string | null
  }
}

interface KYCInformation {
  id: string
  first_name: string
  last_name: string
  full_legal_name: string
  id_type: string
  phone_number: string
  email_address: string
  street_address: string
  city: string
  postal_code: string
  country_code: string
  country_name: string
  data_consent_given: boolean
  privacy_policy_accepted: boolean
  kyc_completed_at: string
  kyc_status: string
  certificate_requested: boolean
  certificate_generated_at: string | null
  certificate_sent_at: string | null
  created_at: string
}

interface User {
  id: number
  username: string
  email: string
  password_hash: string
  full_name: string | null
  phone: string | null
  address: string | null
  is_active: boolean
  is_verified: boolean
  telegram_id: number | null
  country_of_residence: string | null
  role: string
  is_admin: boolean
  created_at: string
  updated_at: string
  telegram_users?: {
    id: string
    telegram_id: number
    username: string
    first_name: string
    last_name: string
  }[]
  // Financial data
  share_purchases?: SharePurchase[]
  payment_transactions?: PaymentTransaction[]
  commission_balances?: CommissionBalance
  // Referral data
  referrals_made?: ReferralData[]
  referred_by?: ReferralData
  // KYC data
  kyc_information?: KYCInformation
  // Calculated fields
  total_shares?: number
  total_invested?: number
  total_commissions?: number
  total_referrals?: number
  // Activity indicators
  last_activity?: string
  activity_score?: number
}

interface UserManagerProps {
  currentUser?: any
  adminUser?: any
  permissions?: any
}

const UserManagerComponent: React.FC<UserManagerProps> = ({ currentUser, adminUser, permissions }) => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog()
  const [searchTerm, setSearchTerm] = useState('')
  const [filterActive, setFilterActive] = useState<'all' | 'active' | 'inactive'>('all')
  const [filterRole, setFilterRole] = useState<'all' | 'user' | 'admin'>('all')
  const [filterKYC, setFilterKYC] = useState<'all' | 'completed' | 'pending' | 'none'>('all')
  const [filterInvestment, setFilterInvestment] = useState<'all' | 'investors' | 'non-investors'>('all')
  const [filterReferrals, setFilterReferrals] = useState<'all' | 'has-referrals' | 'no-referrals'>('all')
  const [filterActivity, setFilterActivity] = useState<'all' | 'high' | 'medium' | 'low'>('all')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showFinancialModal, setShowFinancialModal] = useState(false)
  const [showFinancialActionsModal, setShowFinancialActionsModal] = useState(false)
  const [showCommunicationModal, setShowCommunicationModal] = useState(false)
  const [showBulkActionsModal, setShowBulkActionsModal] = useState(false)
  const [selectedUserIds, setSelectedUserIds] = useState<Set<number>>(new Set())
  const [currentPage, setCurrentPage] = useState(1)
  const [usersPerPage] = useState(10)
  const [sortField, setSortField] = useState<'username' | 'created_at' | 'total_shares' | 'total_invested' | 'total_commissions' | 'total_referrals'>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    try {
      setLoading(true)
      setError('')

      // Load users with all related financial data
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select(`
          *,
          telegram_users(
            id,
            telegram_id,
            username,
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false })

      if (usersError) {
        throw usersError
      }

      // Get all user IDs for batch queries
      const userIds = (usersData || []).map(user => user.id)

      // Load all financial data in batch queries using service role client
      const serviceClient = getServiceRoleClient()
      const [
        { data: allSharePurchases },
        { data: allPaymentTransactions },
        { data: allCommissionBalances },
        { data: allReferrals },
        { data: allKycInformation }
      ] = await Promise.all([
        serviceClient
          .from('aureus_share_purchases')
          .select('*')
          .in('user_id', userIds)
          .order('created_at', { ascending: false }),
        serviceClient
          .from('crypto_payment_transactions')
          .select('*')
          .in('user_id', userIds)
          .order('created_at', { ascending: false }),
        serviceClient
          .from('commission_balances')
          .select('*')
          .in('user_id', userIds),
        serviceClient
          .from('referrals')
          .select('*')
          .or(`referrer_id.in.(${userIds.join(',')}),referred_id.in.(${userIds.join(',')})`),
        serviceClient
          .from('kyc_information')
          .select('*')
          .in('user_id', userIds)
      ])

      // Process users with their financial data
      const usersWithFinancialData = (usersData || []).map((user) => {
        try {
          // Get user's data from batch queries
          const sharePurchases = (allSharePurchases || []).filter(sp => sp.user_id === user.id)
          const paymentTransactions = (allPaymentTransactions || []).filter(pt => pt.user_id === user.id)
          const commissionBalances = (allCommissionBalances || []).find(cb => cb.user_id === user.id)
          const referralsMade = (allReferrals || []).filter(r => r.referrer_id === user.id)
          const referredBy = (allReferrals || []).find(r => r.referred_id === user.id)
          const kycInformation = (allKycInformation || []).find(kyc => kyc.user_id === user.id)

          // Calculate totals
          const totalShares = sharePurchases?.reduce((sum, purchase) =>
            sum + (purchase.shares_purchased || 0), 0) || 0

          const totalInvested = sharePurchases?.reduce((sum, purchase) =>
            sum + (purchase.total_amount || 0), 0) || 0

          const totalCommissions = commissionBalances?.total_earned_usdt || 0
          const totalReferrals = referralsMade?.length || 0

          // Calculate activity indicators
          const activities = [
            ...(sharePurchases || []).map(p => new Date(p.created_at)),
            ...(paymentTransactions || []).map(p => new Date(p.created_at)),
            ...(referralsMade || []).map(r => new Date(r.created_at)),
            kycInformation ? new Date(kycInformation.created_at) : null
          ].filter(Boolean).sort((a, b) => b.getTime() - a.getTime())

          const lastActivity = activities.length > 0 ? activities[0].toISOString() : user.created_at
          const activityScore = Math.min(100, (activities.length * 10) + (totalShares > 0 ? 20 : 0) + (totalReferrals > 0 ? 15 : 0) + (kycInformation ? 25 : 0))

          return {
            ...user,
            share_purchases: sharePurchases || [],
            payment_transactions: paymentTransactions || [],
            commission_balances: commissionBalances,
            referrals_made: referralsMade || [],
            referred_by: referredBy,
            kyc_information: kycInformation,
            total_shares: totalShares,
            total_invested: totalInvested,
            total_commissions: totalCommissions,
            total_referrals: totalReferrals,
            last_activity: lastActivity,
            activity_score: activityScore
          }
          } catch (err) {
            console.error(`Error loading financial data for user ${user.id}:`, err)
            return {
              ...user,
              share_purchases: [],
              payment_transactions: [],
              commission_balances: null,
              referrals_made: [],
              referred_by: null,
              kyc_information: null,
              total_shares: 0,
              total_invested: 0,
              total_commissions: 0,
              total_referrals: 0,
              last_activity: user.created_at,
              activity_score: 0
            }
          }
        })

      setUsers(usersWithFinancialData)
    } catch (err: any) {
      console.error('Error loading users:', err)
      setError(err.message || 'Failed to load users')
    } finally {
      setLoading(false)
    }
  }

  // Filter and search users
  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.full_name && user.full_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.kyc_information?.full_legal_name && user.kyc_information.full_legal_name.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesActiveFilter =
      filterActive === 'all' ||
      (filterActive === 'active' && user.is_active) ||
      (filterActive === 'inactive' && !user.is_active)

    const matchesRoleFilter =
      filterRole === 'all' ||
      (filterRole === 'admin' && user.is_admin) ||
      (filterRole === 'user' && !user.is_admin)

    const matchesKYCFilter =
      filterKYC === 'all' ||
      (filterKYC === 'completed' && user.kyc_information?.kyc_status === 'completed') ||
      (filterKYC === 'pending' && user.kyc_information?.kyc_status === 'pending') ||
      (filterKYC === 'none' && !user.kyc_information)

    const matchesInvestmentFilter =
      filterInvestment === 'all' ||
      (filterInvestment === 'investors' && (user.total_invested || 0) > 0) ||
      (filterInvestment === 'non-investors' && (user.total_invested || 0) === 0)

    const matchesReferralFilter =
      filterReferrals === 'all' ||
      (filterReferrals === 'has-referrals' && (user.total_referrals || 0) > 0) ||
      (filterReferrals === 'no-referrals' && (user.total_referrals || 0) === 0)

    const matchesActivityFilter =
      filterActivity === 'all' ||
      (filterActivity === 'high' && (user.activity_score || 0) >= 70) ||
      (filterActivity === 'medium' && (user.activity_score || 0) >= 30 && (user.activity_score || 0) < 70) ||
      (filterActivity === 'low' && (user.activity_score || 0) < 30)

    return matchesSearch && matchesActiveFilter && matchesRoleFilter && matchesKYCFilter && matchesInvestmentFilter && matchesReferralFilter && matchesActivityFilter
  }).sort((a, b) => {
    let aValue: any = a[sortField]
    let bValue: any = b[sortField]

    // Handle null/undefined values
    if (aValue == null) aValue = 0
    if (bValue == null) bValue = 0

    // Handle string vs number comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }

    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    } else {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    }
  })

  // Pagination
  const indexOfLastUser = currentPage * usersPerPage
  const indexOfFirstUser = indexOfLastUser - usersPerPage
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser)
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage)

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setShowEditModal(true)
  }

  const handleViewFinancials = (user: User) => {
    setSelectedUser(user)
    setShowFinancialModal(true)
  }

  const handleFinancialActions = (user: User) => {
    setSelectedUser(user)
    setShowFinancialActionsModal(true)
  }

  const handleCommunication = (user: User) => {
    setSelectedUser(user)
    setShowCommunicationModal(true)
  }

  const handleUserSelection = (userId: number, selected: boolean) => {
    setSelectedUserIds(prev => {
      const newSet = new Set(prev)
      if (selected) {
        newSet.add(userId)
      } else {
        newSet.delete(userId)
      }
      return newSet
    })
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedUserIds(new Set(filteredUsers.map(u => u.id)))
    } else {
      setSelectedUserIds(new Set())
    }
  }

  const handleBulkActions = () => {
    if (selectedUserIds.size === 0) {
      alert('Please select users first')
      return
    }
    setShowBulkActionsModal(true)
  }

  const clearSelection = () => {
    setSelectedUserIds(new Set())
  }

  const selectedUsers = users.filter(u => selectedUserIds.has(u.id))

  const handleSort = (field: typeof sortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const getSortIcon = (field: typeof sortField) => {
    if (sortField !== field) return '↕️'
    return sortDirection === 'asc' ? '↑' : '↓'
  }

  const handleDeleteUser = async (userId: number) => {
    const user = users.find(u => u.id === userId)
    if (!user) return

    showConfirmation({
      title: 'Delete User',
      message: `Are you sure you want to delete user "${user.username}"? This action cannot be undone and will permanently remove all user data.`,
      type: 'danger',
      confirmText: 'Delete User',
      onConfirm: async () => {
        const { error } = await supabase
          .from('users')
          .delete()
          .eq('id', userId)

        if (error) throw error

        // Log admin action
        await logAdminAction(
          adminUser?.email || 'unknown',
          'DELETE_USER',
          'user',
          userId.toString(),
          { reason: 'Admin deletion', username: user.username }
        )

        await loadUsers()
        alert('User deleted successfully')
      }
    })
  }

  const toggleUserStatus = async (userId: number, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: !currentStatus })
        .eq('id', userId)

      if (error) throw error

      // Log admin action
      await logAdminAction(
        adminUser?.email || 'unknown',
        currentStatus ? 'DEACTIVATE_USER' : 'ACTIVATE_USER',
        'user',
        userId.toString(),
        { previous_status: currentStatus, new_status: !currentStatus }
      )

      await loadUsers()
    } catch (err: any) {
      console.error('Error updating user status:', err)
      alert('Failed to update user status: ' + err.message)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
        <span className="ml-3 text-gray-300">Loading users...</span>
      </div>
    )
  }

  // Calculate summary statistics
  const totalUsers = users.length
  const activeUsers = users.filter(u => u.is_active).length
  const totalSharesAllUsers = users.reduce((sum, user) => sum + (user.total_shares || 0), 0)
  const totalInvestedAllUsers = users.reduce((sum, user) => sum + (user.total_invested || 0), 0)
  const totalCommissionsAllUsers = users.reduce((sum, user) => sum + (user.total_commissions || 0), 0)
  const totalReferralsAllUsers = users.reduce((sum, user) => sum + (user.total_referrals || 0), 0)
  const usersWithInvestments = users.filter(u => (u.total_invested || 0) > 0).length
  const usersWithReferrals = users.filter(u => (u.total_referrals || 0) > 0).length
  const usersWithKYC = users.filter(u => u.kyc_information).length
  const kycCompleted = users.filter(u => u.kyc_information?.kyc_status === 'completed').length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">👥 User Management</h2>
          <p className="text-gray-400 mt-1">
            Manage user accounts, permissions, and profile information
          </p>
        </div>
        <div className="text-sm text-gray-400">
          Total Users: {users.length} | Filtered: {filteredUsers.length}
        </div>
      </div>

      {/* Enhanced Financial Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <div className="glass-card p-6 border border-gray-700/50 hover:border-gray-600/50 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Users</p>
              <p className="text-3xl font-bold text-white mt-2">{totalUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                <p className="text-sm text-green-400 font-medium">{activeUsers} active</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">👥</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Shares</p>
              <p className="text-3xl font-bold text-yellow-400 mt-2">{totalSharesAllUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                <p className="text-sm text-yellow-400 font-medium">{usersWithInvestments} investors</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">📈</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-green-500/20 hover:border-green-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Invested</p>
              <p className="text-3xl font-bold text-green-400 mt-2">${totalInvestedAllUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                <p className="text-sm text-green-400 font-medium">All users combined</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">💰</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-blue-500/20 hover:border-blue-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Commissions</p>
              <p className="text-3xl font-bold text-blue-400 mt-2">${totalCommissionsAllUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                <p className="text-sm text-blue-400 font-medium">Earned by users</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">🤝</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">Total Referrals</p>
              <p className="text-3xl font-bold text-purple-400 mt-2">{totalReferralsAllUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                <p className="text-sm text-purple-400 font-medium">{usersWithReferrals} active referrers</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">👥</div>
          </div>
        </div>

        <div className="glass-card p-6 border border-orange-500/20 hover:border-orange-500/40 transition-all duration-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400 uppercase tracking-wider">KYC Status</p>
              <p className="text-3xl font-bold text-orange-400 mt-2">{kycCompleted}/{usersWithKYC}</p>
              <div className="flex items-center mt-2">
                <div className="w-2 h-2 bg-orange-400 rounded-full mr-2"></div>
                <p className="text-sm text-orange-400 font-medium">Completed/Total</p>
              </div>
            </div>
            <div className="text-4xl opacity-80">🆔</div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <p className="text-red-400">❌ {error}</p>
        </div>
      )}

      {/* Bulk Actions Bar */}
      {selectedUserIds.size > 0 && (
        <div className="glass-card p-4 bg-blue-900/20 border border-blue-500/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-blue-400 font-medium">
                {selectedUserIds.size} users selected
              </span>
              <button
                onClick={clearSelection}
                className="text-gray-400 hover:text-white text-sm"
              >
                Clear Selection
              </button>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleBulkActions}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
              >
                🔧 Bulk Actions
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className="glass-card p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-7 gap-4">
          {/* Search */}
          <div className="md:col-span-2 xl:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              🔍 Search Users
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by username, email, or name..."
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-500"
            />
          </div>

          {/* Active Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              📊 Status
            </label>
            <select
              value={filterActive}
              onChange={(e) => setFilterActive(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Users</option>
              <option value="active">Active Only</option>
              <option value="inactive">Inactive Only</option>
            </select>
          </div>

          {/* Role Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              👑 Role
            </label>
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Roles</option>
              <option value="user">Users Only</option>
              <option value="admin">Admins Only</option>
            </select>
          </div>

          {/* KYC Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              🆔 KYC Status
            </label>
            <select
              value={filterKYC}
              onChange={(e) => setFilterKYC(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All KYC</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="none">No KYC</option>
            </select>
          </div>

          {/* Investment Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              💰 Investment
            </label>
            <select
              value={filterInvestment}
              onChange={(e) => setFilterInvestment(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Users</option>
              <option value="investors">Investors</option>
              <option value="non-investors">Non-Investors</option>
            </select>
          </div>

          {/* Referrals Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              👥 Referrals
            </label>
            <select
              value={filterReferrals}
              onChange={(e) => setFilterReferrals(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Users</option>
              <option value="has-referrals">Has Referrals</option>
              <option value="no-referrals">No Referrals</option>
            </select>
          </div>

          {/* Activity Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              📊 Activity
            </label>
            <select
              value={filterActivity}
              onChange={(e) => setFilterActivity(e.target.value as any)}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
            >
              <option value="all">All Levels</option>
              <option value="high">High (70%+)</option>
              <option value="medium">Medium (30-70%)</option>
              <option value="low">Low (&lt;30%)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Users Table - Desktop */}
      <div className="glass-card overflow-hidden hidden md:block">
        <div className="overflow-x-auto bg-gray-900/50 rounded-lg border border-gray-700">
          <table className="w-full min-w-[1400px]">
            <thead className="bg-gray-800/70 border-b border-gray-600">
              <tr>
                <th className="px-4 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider w-16">
                  <input
                    type="checkbox"
                    checked={selectedUserIds.size === filteredUsers.length && filteredUsers.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-600 bg-gray-800 text-yellow-500 focus:ring-yellow-500"
                  />
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-64 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('username')}
                >
                  <div className="flex items-center space-x-1">
                    <span>👤 User</span>
                    <span className="text-yellow-400">{getSortIcon('username')}</span>
                  </div>
                </th>
                <th className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-28">
                  <div className="flex items-center space-x-1">
                    <span>📊 Status</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-28 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('total_shares')}
                >
                  <div className="flex items-center space-x-1">
                    <span>📈 Shares</span>
                    <span className="text-yellow-400">{getSortIcon('total_shares')}</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-32 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('total_invested')}
                >
                  <div className="flex items-center space-x-1">
                    <span>💰 Invested</span>
                    <span className="text-yellow-400">{getSortIcon('total_invested')}</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-32 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('total_commissions')}
                >
                  <div className="flex items-center space-x-1">
                    <span>🤝 Commissions</span>
                    <span className="text-yellow-400">{getSortIcon('total_commissions')}</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-28 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('total_referrals')}
                >
                  <div className="flex items-center space-x-1">
                    <span>👥 Referrals</span>
                    <span className="text-yellow-400">{getSortIcon('total_referrals')}</span>
                  </div>
                </th>
                <th className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-24">
                  <div className="flex items-center space-x-1">
                    <span>🆔 KYC</span>
                  </div>
                </th>
                <th
                  className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-28 cursor-pointer hover:bg-gray-700/50 transition-colors"
                  onClick={() => handleSort('created_at')}
                >
                  <div className="flex items-center space-x-1">
                    <span>📅 Created</span>
                    <span className="text-yellow-400">{getSortIcon('created_at')}</span>
                  </div>
                </th>
                <th className="px-4 py-4 text-left text-sm font-semibold text-gray-200 uppercase tracking-wider w-48">
                  <div className="flex items-center space-x-1">
                    <span>⚡ Actions</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700/50 bg-gray-900/30">
              {currentUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-800/40 transition-all duration-200 border-b border-gray-700/30">
                  <td className="px-4 py-5 w-16">
                    <input
                      type="checkbox"
                      checked={selectedUserIds.has(user.id)}
                      onChange={(e) => handleUserSelection(user.id, e.target.checked)}
                      className="rounded border-gray-600 bg-gray-800 text-yellow-500 focus:ring-yellow-500 w-4 h-4"
                    />
                  </td>
                  <td className="px-4 py-5 w-64">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className="text-base font-semibold text-white truncate max-w-[180px]">
                          {user.full_name || user.username}
                        </div>
                        {user.is_admin && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-500/20 text-yellow-400">
                            👑 Admin
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-400">@{user.username}</div>
                      <div className="text-xs text-gray-500">ID: {user.id}</div>
                      <div className="text-sm text-gray-300 truncate max-w-[200px]">{user.email}</div>
                      {user.phone && (
                        <div className="text-xs text-gray-400 truncate">{user.phone}</div>
                      )}
                      {user.telegram_users && user.telegram_users.length > 0 ? (
                        <div className="text-xs text-blue-400 truncate max-w-[200px]">
                          📱 @{user.telegram_users[0].username} ({user.telegram_users[0].first_name} {user.telegram_users[0].last_name})
                        </div>
                      ) : (
                        <div className="text-xs text-gray-500">📱 No Telegram</div>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-5 w-28">
                    <div className="flex flex-col space-y-2">
                      <span className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-full w-fit ${
                        user.is_active
                          ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                          : 'bg-red-500/20 text-red-400 border border-red-500/30'
                      }`}>
                        <span className="w-2 h-2 rounded-full mr-2 ${user.is_active ? 'bg-green-400' : 'bg-red-400'}"></span>
                        {user.is_active ? 'Active' : 'Inactive'}
                      </span>
                      {user.is_verified && (
                        <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-500/20 text-blue-400 border border-blue-500/30 w-fit">
                          ✓ Verified
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-5 w-28">
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-400">
                        {(user.total_shares || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        📈 {user.share_purchases?.length || 0} purchases
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-32">
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-400">
                        ${(user.total_invested || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        💰 {user.payment_transactions?.length || 0} payments
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-32">
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-400">
                        ${(user.total_commissions || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        🤝 {user.commission_balances ? 'Active' : 'None'}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-28">
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-400">
                        {(user.total_referrals || 0).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        👥 {user.referred_by ? 'Referred' : 'Direct'}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-24">
                    <div className="text-center">
                      {user.kyc_information ? (
                        <span className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-full border ${
                          user.kyc_information.kyc_status === 'completed'
                            ? 'bg-green-500/20 text-green-400 border-green-500/30'
                            : user.kyc_information.kyc_status === 'pending'
                            ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
                            : 'bg-red-500/20 text-red-400 border-red-500/30'
                        }`}>
                          {user.kyc_information.kyc_status === 'completed' ? '✅' :
                           user.kyc_information.kyc_status === 'pending' ? '⏳' : '❌'}
                          <span className="ml-1 capitalize">{user.kyc_information.kyc_status}</span>
                        </span>
                      ) : (
                        <span className="text-sm text-gray-500 bg-gray-700/30 px-3 py-1.5 rounded-full border border-gray-600/30">
                          🆔 None
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-5 text-sm text-gray-300 w-28">
                    <div className="text-center">
                      <div className="font-medium">
                        {new Date(user.created_at).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(user.created_at).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-5 w-48">
                    <div className="flex flex-wrap gap-2">
                      <button
                        onClick={() => handleViewFinancials(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 hover:bg-yellow-500/30 transition-colors"
                      >
                        💰 Financials
                      </button>
                      <button
                        onClick={() => handleFinancialActions(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30 transition-colors"
                      >
                        ⚙️ Manage
                      </button>
                      <button
                        onClick={() => handleCommunication(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-purple-500/20 text-purple-400 border border-purple-500/30 hover:bg-purple-500/30 transition-colors"
                      >
                        📨 Message
                      </button>
                      <button
                        onClick={() => handleEditUser(user)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30 transition-colors"
                      >
                        ✏️ Edit
                      </button>
                      <button
                        onClick={() => toggleUserStatus(user.id, user.is_active)}
                        className={`inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg border transition-colors ${
                          user.is_active
                            ? 'bg-red-500/20 text-red-400 border-red-500/30 hover:bg-red-500/30'
                            : 'bg-green-500/20 text-green-400 border-green-500/30 hover:bg-green-500/30'
                        }`}
                      >
                        {user.is_active ? '🔴 Deactivate' : '🟢 Activate'}
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user.id)}
                        className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg bg-red-600/20 text-red-400 border border-red-600/30 hover:bg-red-600/30 transition-colors"
                      >
                        🗑️ Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Enhanced Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 bg-gray-800/30 border-t border-gray-600 flex items-center justify-between rounded-b-lg">
            <div className="text-sm text-gray-300 font-medium">
              📊 Showing <span className="text-yellow-400 font-bold">{indexOfFirstUser + 1}</span> to <span className="text-yellow-400 font-bold">{Math.min(indexOfLastUser, filteredUsers.length)}</span> of <span className="text-yellow-400 font-bold">{filteredUsers.length}</span> users
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="inline-flex items-center px-4 py-2 text-sm font-medium bg-gray-700 text-gray-300 rounded-lg border border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 hover:border-gray-500 transition-colors"
              >
                ← Previous
              </button>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400">Page</span>
                <span className="px-3 py-1 text-sm font-bold text-yellow-400 bg-gray-800 rounded-lg border border-gray-600">
                  {currentPage}
                </span>
                <span className="text-sm text-gray-400">of</span>
                <span className="px-3 py-1 text-sm font-bold text-gray-300 bg-gray-800 rounded-lg border border-gray-600">
                  {totalPages}
                </span>
              </div>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="inline-flex items-center px-4 py-2 text-sm font-medium bg-gray-700 text-gray-300 rounded-lg border border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600 hover:border-gray-500 transition-colors"
              >
                Next →
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Users Cards - Mobile/Tablet */}
      <div className="md:hidden space-y-4">
        {currentUsers.map((user) => (
          <div key={user.id} className="glass-card p-4">
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <h3 className="text-lg font-medium text-white">
                  {user.full_name || user.username}
                </h3>
                <p className="text-sm text-gray-400">@{user.username}</p>
                <p className="text-xs text-gray-500">ID: {user.id}</p>
              </div>
              <div className="flex flex-col space-y-1">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full w-fit ${
                  user.is_active
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {user.is_active ? 'Active' : 'Inactive'}
                </span>
                {user.is_admin && (
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-500/20 text-yellow-400 w-fit">
                    Admin
                  </span>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
              <div>
                <p className="text-xs text-gray-400 uppercase tracking-wider">Contact</p>
                <p className="text-sm text-gray-300">{user.email}</p>
                {user.phone && (
                  <p className="text-sm text-gray-400">{user.phone}</p>
                )}
              </div>

              <div>
                <p className="text-xs text-gray-400 uppercase tracking-wider">Telegram</p>
                {user.telegram_users && user.telegram_users.length > 0 ? (
                  <div className="text-sm">
                    <p className="text-blue-400">@{user.telegram_users[0].username}</p>
                    <p className="text-gray-400 text-xs">
                      {user.telegram_users[0].first_name} {user.telegram_users[0].last_name}
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">Not connected</p>
                )}
              </div>
            </div>

            {/* Financial Information */}
            <div className="grid grid-cols-2 gap-3 mb-4 p-3 bg-gray-800/30 rounded-lg">
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wider">Shares</p>
                <p className="text-lg font-bold text-yellow-400">{(user.total_shares || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">{user.share_purchases?.length || 0} purchases</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wider">Invested</p>
                <p className="text-lg font-bold text-green-400">${(user.total_invested || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">{user.payment_transactions?.length || 0} payments</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wider">Commissions</p>
                <p className="text-lg font-bold text-blue-400">${(user.total_commissions || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">{user.commission_balances ? 'Active' : 'None'}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-400 uppercase tracking-wider">Referrals</p>
                <p className="text-lg font-bold text-purple-400">{(user.total_referrals || 0).toLocaleString()}</p>
                <p className="text-xs text-gray-500">{user.referred_by ? 'Referred' : 'Direct'}</p>
              </div>
            </div>

            {/* Referral Information */}
            {(user.referred_by || (user.total_referrals || 0) > 0) && (
              <div className="mb-4 p-3 bg-purple-900/20 rounded-lg border border-purple-500/20">
                <p className="text-xs text-purple-400 uppercase tracking-wider mb-2">Referral Network</p>
                {user.referred_by && (
                  <div className="mb-2">
                    <p className="text-xs text-gray-400">Referred by:</p>
                    <p className="text-sm text-purple-300">
                      {user.referred_by.referrer?.full_name || user.referred_by.referrer?.username}
                    </p>
                  </div>
                )}
                {(user.total_referrals || 0) > 0 && (
                  <div>
                    <p className="text-xs text-gray-400">Has referred {user.total_referrals} users</p>
                  </div>
                )}
              </div>
            )}

            {/* KYC Information */}
            {user.kyc_information && (
              <div className="mb-4 p-3 bg-orange-900/20 rounded-lg border border-orange-500/20">
                <p className="text-xs text-orange-400 uppercase tracking-wider mb-2">KYC Status</p>
                <div className="flex justify-between items-center">
                  <div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.kyc_information.kyc_status === 'completed'
                        ? 'bg-green-500/20 text-green-400'
                        : user.kyc_information.kyc_status === 'pending'
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {user.kyc_information.kyc_status}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-400">
                      {user.kyc_information.full_legal_name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {user.kyc_information.country_name}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center pt-3 border-t border-gray-700">
              <div>
                <p className="text-xs text-gray-400">
                  Created: {new Date(user.created_at).toLocaleDateString()}
                </p>
                <p className="text-xs text-gray-500">
                  Last Activity: {new Date(user.last_activity || user.created_at).toLocaleDateString()}
                </p>
                <div className="flex items-center mt-1">
                  <span className="text-xs text-gray-400 mr-2">Activity:</span>
                  <div className="w-16 h-2 bg-gray-700 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 rounded-full"
                      style={{ width: `${user.activity_score || 0}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-400 ml-2">{user.activity_score || 0}%</span>
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleViewFinancials(user)}
                  className="text-yellow-400 hover:text-yellow-300 text-sm font-medium"
                >
                  💰 Financials
                </button>
                <button
                  onClick={() => handleFinancialActions(user)}
                  className="text-green-400 hover:text-green-300 text-sm font-medium"
                >
                  ⚙️ Manage
                </button>
                <button
                  onClick={() => handleCommunication(user)}
                  className="text-purple-400 hover:text-purple-300 text-sm font-medium"
                >
                  📨 Message
                </button>
                <button
                  onClick={() => handleEditUser(user)}
                  className="text-blue-400 hover:text-blue-300 text-sm font-medium"
                >
                  Edit
                </button>
                <button
                  onClick={() => toggleUserStatus(user.id, user.is_active)}
                  className={`text-sm font-medium ${
                    user.is_active
                      ? 'text-red-400 hover:text-red-300'
                      : 'text-green-400 hover:text-green-300'
                  }`}
                >
                  {user.is_active ? 'Deactivate' : 'Activate'}
                </button>
                <button
                  onClick={() => handleDeleteUser(user.id)}
                  className="text-red-400 hover:text-red-300 text-sm font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}

        {/* Mobile Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between pt-4">
            <div className="text-sm text-gray-400">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Edit Modal */}
      {showEditModal && selectedUser && (
        <UserEditModal
          user={selectedUser}
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false)
            setSelectedUser(null)
          }}
          onSave={() => {
            loadUsers()
            setShowEditModal(false)
            setSelectedUser(null)
          }}
          adminUser={adminUser}
        />
      )}

      {/* Financial Modal */}
      {showFinancialModal && selectedUser && (
        <UserFinancialModal
          user={selectedUser}
          isOpen={showFinancialModal}
          onClose={() => {
            setShowFinancialModal(false)
            setSelectedUser(null)
          }}
        />
      )}

      {/* Financial Actions Modal */}
      {showFinancialActionsModal && selectedUser && (
        <FinancialActionsModal
          user={selectedUser}
          isOpen={showFinancialActionsModal}
          onClose={() => {
            setShowFinancialActionsModal(false)
            setSelectedUser(null)
          }}
          adminUser={adminUser}
          onUpdate={() => {
            loadUsers()
          }}
        />
      )}

      {/* Communication Modal */}
      {showCommunicationModal && selectedUser && (
        <CommunicationModal
          user={selectedUser}
          isOpen={showCommunicationModal}
          onClose={() => {
            setShowCommunicationModal(false)
            setSelectedUser(null)
          }}
          adminUser={adminUser}
          onUpdate={() => {
            loadUsers()
          }}
        />
      )}

      {/* Bulk Actions Modal */}
      {showBulkActionsModal && (
        <BulkActionsModal
          isOpen={showBulkActionsModal}
          onClose={() => {
            setShowBulkActionsModal(false)
          }}
          selectedUsers={selectedUsers}
          adminUser={adminUser}
          onUpdate={() => {
            loadUsers()
          }}
          onClearSelection={clearSelection}
        />
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog />
    </div>
  )
}

// Export with admin authentication protection
export const UserManager = withAdminAuth(UserManagerComponent, 'canManageUsers')
