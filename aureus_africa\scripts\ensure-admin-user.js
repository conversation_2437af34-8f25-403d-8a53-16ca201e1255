#!/usr/bin/env node

/**
 * Ensure Admin User Script
 * 
 * This script ensures that the admin user exists in the admin_users table
 * for proper admin authentication.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function ensureAdminUser() {
  const adminEmail = '<EMAIL>';
  
  try {
    console.log('🔍 Checking if admin user exists in admin_users table...');
    
    // Check if admin user exists in admin_users table
    const { data: existingAdmin, error: checkError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', adminEmail)
      .single();
    
    if (checkError && checkError.code !== 'PGRST116') {
      console.error('❌ Error checking admin user:', checkError);
      return;
    }
    
    if (existingAdmin) {
      console.log('✅ Admin user already exists in admin_users table:', existingAdmin);
      return;
    }
    
    console.log('📝 Adding admin user to admin_users table...');
    
    // Add admin user to admin_users table
    const { data: newAdmin, error: insertError } = await supabase
      .from('admin_users')
      .insert({
        email: adminEmail,
        role: 'super_admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (insertError) {
      console.error('❌ Error adding admin user to admin_users table:', insertError);
      return;
    }
    
    console.log('✅ Admin user added to admin_users table:', newAdmin);
    console.log('');
    console.log('🎉 Admin setup complete!');
    console.log(`📧 Admin Email: ${adminEmail}`);
    console.log('🔑 Use your existing password to login to /admin');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

ensureAdminUser();
