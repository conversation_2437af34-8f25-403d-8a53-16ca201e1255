-- Comprehensive Notification System Database Schema
-- This schema creates tables for managing user notifications across the platform

-- Main notifications table
CREATE TABLE IF NOT EXISTS user_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  notification_type VARCHAR(50) NOT NULL, -- 'payment_approved', 'payment_rejected', 'commission_earned', 'system', 'referral', 'withdrawal'
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  
  -- Metadata for different notification types
  metadata JSONB DEFAULT '{}', -- Store type-specific data (payment_id, amount, commission_details, etc.)
  
  -- Status tracking
  is_read BOOLEAN DEFAULT FALSE,
  is_archived BOOLEAN DEFAULT FALSE,
  priority VARCHAR(20) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
  
  -- Related entities (optional foreign keys)
  payment_id UUID REFERENCES crypto_payment_transactions(id) ON DELETE SET NULL,
  commission_id UUID REFERENCES commission_transactions(id) ON DELETE SET NULL,
  referral_id UUID REFERENCES referrals(id) ON DELETE SET NULL,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE,
  archived_at TIMESTAMP WITH TIME ZONE,
  
  -- Constraints
  CONSTRAINT valid_notification_type CHECK (
    notification_type IN (
      'payment_approved', 'payment_rejected', 'commission_earned', 
      'system', 'referral', 'withdrawal', 'share_purchase', 
      'phase_change', 'account_update', 'security_alert'
    )
  ),
  CONSTRAINT valid_priority CHECK (priority IN ('low', 'normal', 'high', 'urgent'))
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_id ON user_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_type ON user_notifications(notification_type);
CREATE INDEX IF NOT EXISTS idx_user_notifications_read_status ON user_notifications(user_id, is_read);
CREATE INDEX IF NOT EXISTS idx_user_notifications_created_at ON user_notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_notifications_payment_id ON user_notifications(payment_id);
CREATE INDEX IF NOT EXISTS idx_user_notifications_commission_id ON user_notifications(commission_id);

-- Notification templates table for consistent messaging
CREATE TABLE IF NOT EXISTS notification_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_key VARCHAR(100) UNIQUE NOT NULL,
  notification_type VARCHAR(50) NOT NULL,
  title_template VARCHAR(255) NOT NULL,
  message_template TEXT NOT NULL,
  
  -- Template variables (for documentation)
  variables JSONB DEFAULT '[]', -- Array of variable names used in template
  
  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default notification templates
INSERT INTO notification_templates (template_key, notification_type, title_template, message_template, variables) VALUES
('payment_approved', 'payment_approved', 
 '✅ Payment Approved - {{amount}} USDT', 
 'Great news! Your payment of {{amount}} USDT has been approved and processed.\n\n📊 Share Allocation:\n• Shares Purchased: {{shares}} shares\n• Price per Share: ${{price_per_share}}\n• Total Investment: {{amount}} USDT\n\n💰 Your shares are now active and earning dividends!\n\nTransaction ID: {{payment_id}}\nProcessed: {{processed_date}}',
 '["amount", "shares", "price_per_share", "payment_id", "processed_date"]'),

('payment_rejected', 'payment_rejected',
 '❌ Payment Rejected - {{amount}} USDT',
 'Unfortunately, your payment of {{amount}} USDT has been rejected.\n\n📋 Rejection Details:\n• Amount: {{amount}} USDT\n• Network: {{network}}\n• Transaction Hash: {{tx_hash}}\n\n❌ Reason for Rejection:\n{{rejection_reason}}\n\n🔄 Next Steps:\nPlease review the rejection reason and submit a new payment with the correct information. If you need assistance, contact our support team.\n\nTransaction ID: {{payment_id}}\nRejected: {{rejected_date}}',
 '["amount", "network", "tx_hash", "rejection_reason", "payment_id", "rejected_date"]'),

('commission_earned', 'commission_earned',
 '💰 Commission Earned - {{total_commission}} USDT + {{share_commission}} Shares',
 'Congratulations! You''ve earned a commission from your referral''s investment.\n\n👤 Referral Details:\n• Referred User: {{referred_username}}\n• Investment Amount: {{investment_amount}} USDT\n• Shares Purchased: {{shares_purchased}} shares\n\n💰 Your Commission (15%):\n• USDT Commission: {{usdt_commission}} USDT\n• Share Commission: {{share_commission}} shares\n• Total Value: {{total_commission}} USDT\n\n📊 Updated Balances:\n• Total USDT Balance: {{total_usdt_balance}} USDT\n• Total Share Balance: {{total_share_balance}} shares\n\nCommission ID: {{commission_id}}\nEarned: {{earned_date}}',
 '["total_commission", "share_commission", "referred_username", "investment_amount", "shares_purchased", "usdt_commission", "total_usdt_balance", "total_share_balance", "commission_id", "earned_date"]'),

('referral_signup', 'referral',
 '🎉 New Referral Signup - {{referred_username}}',
 'Great news! Someone has joined Aureus Alliance Holdings using your referral link.\n\n👤 New Member:\n• Username: {{referred_username}}\n• Email: {{referred_email}}\n• Joined: {{signup_date}}\n\n💰 Earning Potential:\nYou will earn 15% commission (USDT + Shares) on all their investments!\n\n📊 Your Referral Stats:\n• Total Referrals: {{total_referrals}}\n• Active Referrals: {{active_referrals}}\n• Total Commissions Earned: {{total_commissions}} USDT',
 '["referred_username", "referred_email", "signup_date", "total_referrals", "active_referrals", "total_commissions"]'),

('system_announcement', 'system',
 '📢 System Announcement',
 '{{announcement_message}}',
 '["announcement_message"]'),

('phase_change', 'phase_change',
 '📈 Investment Phase Updated - Phase {{new_phase}}',
 'The investment phase has been updated!\n\n📊 New Phase Details:\n• Phase: {{new_phase}}\n• New Price per Share: ${{new_price}}\n• Previous Price: ${{old_price}}\n• Price Change: {{price_change}}%\n\n⏰ This is a great time to consider increasing your investment before the next phase!\n\nPhase Updated: {{phase_date}}',
 '["new_phase", "new_price", "old_price", "price_change", "phase_date"]');

-- Notification preferences table (for future use)
CREATE TABLE IF NOT EXISTS notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Notification type preferences
  payment_notifications BOOLEAN DEFAULT TRUE,
  commission_notifications BOOLEAN DEFAULT TRUE,
  system_notifications BOOLEAN DEFAULT TRUE,
  referral_notifications BOOLEAN DEFAULT TRUE,
  marketing_notifications BOOLEAN DEFAULT FALSE,
  
  -- Delivery preferences
  email_notifications BOOLEAN DEFAULT TRUE,
  web_notifications BOOLEAN DEFAULT TRUE,
  telegram_notifications BOOLEAN DEFAULT TRUE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one record per user
  UNIQUE(user_id)
);

-- Create default notification preferences for existing users
INSERT INTO notification_preferences (user_id)
SELECT id FROM users 
WHERE id NOT IN (SELECT user_id FROM notification_preferences);

-- Notification delivery log (for tracking sent notifications)
CREATE TABLE IF NOT EXISTS notification_delivery_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  notification_id UUID NOT NULL REFERENCES user_notifications(id) ON DELETE CASCADE,
  delivery_method VARCHAR(20) NOT NULL, -- 'web', 'email', 'telegram'
  delivery_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
  delivery_details JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  failed_at TIMESTAMP WITH TIME ZONE,
  
  CONSTRAINT valid_delivery_method CHECK (delivery_method IN ('web', 'email', 'telegram')),
  CONSTRAINT valid_delivery_status CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed'))
);

-- Indexes for delivery log
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_notification_id ON notification_delivery_log(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_status ON notification_delivery_log(delivery_status);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_method ON notification_delivery_log(delivery_method);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON user_notifications TO authenticated;
GRANT SELECT ON notification_templates TO authenticated;
GRANT SELECT, INSERT, UPDATE ON notification_preferences TO authenticated;
GRANT SELECT, INSERT ON notification_delivery_log TO authenticated;

-- Create view for user notification summary
CREATE OR REPLACE VIEW user_notification_summary AS
SELECT 
  u.id as user_id,
  u.username,
  COUNT(n.id) as total_notifications,
  COUNT(CASE WHEN n.is_read = FALSE THEN 1 END) as unread_count,
  COUNT(CASE WHEN n.notification_type = 'payment_approved' THEN 1 END) as payment_approved_count,
  COUNT(CASE WHEN n.notification_type = 'payment_rejected' THEN 1 END) as payment_rejected_count,
  COUNT(CASE WHEN n.notification_type = 'commission_earned' THEN 1 END) as commission_count,
  MAX(n.created_at) as last_notification_date
FROM users u
LEFT JOIN user_notifications n ON u.id = n.user_id
GROUP BY u.id, u.username;

GRANT SELECT ON user_notification_summary TO authenticated;
