-- Add missing columns to crypto_payment_transactions table for web admin payment management

-- Add shares_to_purchase column if it doesn't exist
ALTER TABLE crypto_payment_transactions 
ADD COLUMN IF NOT EXISTS shares_to_purchase INTEGER DEFAULT 0;

-- Add rejection_reason column if it doesn't exist (separate from admin_notes)
ALTER TABLE crypto_payment_transactions 
ADD COLUMN IF NOT EXISTS rejection_reason TEXT;

-- Add approved_by_admin_id column if it doesn't exist
ALTER TABLE crypto_payment_transactions 
ADD COLUMN IF NOT EXISTS approved_by_admin_id BIGINT;

-- Add approved_at column if it doesn't exist
ALTER TABLE crypto_payment_transactions 
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP WITH TIME ZONE;

-- Add rejected_by_admin_id column if it doesn't exist
ALTER TABLE crypto_payment_transactions 
ADD COLUMN IF NOT EXISTS rejected_by_admin_id BIGINT;

-- Add rejected_at column if it doesn't exist
ALTER TABLE crypto_payment_transactions 
ADD COLUMN IF NOT EXISTS rejected_at TIMESTAMP WITH TIME ZONE;

-- Add verification_status column if it doesn't exist
ALTER TABLE crypto_payment_transactions 
ADD COLUMN IF NOT EXISTS verification_status VARCHAR(50) DEFAULT 'pending';

-- Update existing records to calculate shares_to_purchase if they're 0
UPDATE crypto_payment_transactions 
SET shares_to_purchase = FLOOR(amount / 5.0)
WHERE shares_to_purchase = 0 OR shares_to_purchase IS NULL;

-- Create index for better performance on status queries
CREATE INDEX IF NOT EXISTS idx_crypto_payments_status ON crypto_payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_crypto_payments_user_id ON crypto_payment_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_crypto_payments_created_at ON crypto_payment_transactions(created_at);

-- Create view for payment management dashboard
CREATE OR REPLACE VIEW payment_management_view AS
SELECT 
  cpt.*,
  u.username,
  u.email,
  u.full_name,
  CASE 
    WHEN cpt.status = 'pending' THEN 'Pending Review'
    WHEN cpt.status = 'approved' THEN 'Approved'
    WHEN cpt.status = 'rejected' THEN 'Rejected'
    ELSE cpt.status
  END as status_display,
  EXTRACT(EPOCH FROM (NOW() - cpt.created_at))/3600 as hours_since_created
FROM crypto_payment_transactions cpt
LEFT JOIN users u ON cpt.user_id = u.id
ORDER BY cpt.created_at DESC;

-- Grant necessary permissions
GRANT SELECT, UPDATE ON crypto_payment_transactions TO authenticated;
GRANT SELECT ON payment_management_view TO authenticated;
