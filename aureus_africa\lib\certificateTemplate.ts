// Certificate Template Configuration for Aureus Africa
// Professional certificate design matching the official gold-bordered certificate

export interface CertificateData {
  certificateNumber: string;
  userFullName: string;
  sharesQuantity: number;
  purchaseAmount: number;
  pricePerShare: number;
  issueDate: string;
  kycVerified: boolean;
  userAddress: string;
  userId: number;
  purchaseId: string;
  verificationUrl: string;
  userIdNumber?: string;
  sunNumber?: string;
}

export interface CertificateTemplate {
  width: number;
  height: number;
  dpi: number;
  backgroundColor: string;
  elements: CertificateElement[];
  securityElements: SecurityElement[];
  qrCode: QRCodeConfig;
  backgroundImage?: string;
}

export interface CertificateElement {
  type: 'text' | 'image' | 'line' | 'rectangle' | 'watermark';
  id: string;
  x: number;
  y: number;
  width?: number;
  height?: number;
  content?: string;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string;
  color?: string;
  alignment?: 'left' | 'center' | 'right';
  dynamic?: boolean;
  dataField?: keyof CertificateData;
  rotation?: number;
  opacity?: number;
}

export interface SecurityElement {
  type: 'watermark' | 'border' | 'pattern' | 'seal';
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  opacity: number;
  pattern?: string;
  color?: string;
}

export interface QRCodeConfig {
  x: number;
  y: number;
  size: number;
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
  margin: number;
}

// Professional Aureus Africa Certificate Template
export const AUREUS_CERTIFICATE_TEMPLATE: CertificateTemplate = {
  width: 1200,
  height: 850,
  dpi: 300,
  backgroundColor: '#FFFFFF',
  
  elements: [
    // Header Border - Gold decorative border
    {
      type: 'rectangle',
      id: 'header_border',
      x: 20,
      y: 20,
      width: 1160,
      height: 810,
      color: '#D4AF37',
    },
    
    // Inner Border
    {
      type: 'rectangle',
      id: 'inner_border',
      x: 40,
      y: 40,
      width: 1120,
      height: 770,
      color: '#F5F5DC',
    },
    
    // Company Logo Area
    {
      type: 'text',
      id: 'company_logo',
      x: 600,
      y: 80,
      content: 'AUREUS AFRICA',
      fontSize: 36,
      fontFamily: 'serif',
      fontWeight: 'bold',
      color: '#8B4513',
      alignment: 'center',
    },
    
    // Certificate Title
    {
      type: 'text',
      id: 'certificate_title',
      x: 600,
      y: 140,
      content: 'SHARE CERTIFICATE',
      fontSize: 42,
      fontFamily: 'serif',
      fontWeight: 'bold',
      color: '#2F4F4F',
      alignment: 'center',
    },
    
    // Subtitle
    {
      type: 'text',
      id: 'subtitle',
      x: 600,
      y: 180,
      content: 'Gold Mining Investment Holdings',
      fontSize: 18,
      fontFamily: 'serif',
      color: '#696969',
      alignment: 'center',
    },
    
    // Certificate Number (Top Right)
    {
      type: 'text',
      id: 'cert_number_label',
      x: 950,
      y: 100,
      content: 'Certificate No:',
      fontSize: 12,
      fontFamily: 'sans-serif',
      color: '#2F4F4F',
      alignment: 'left',
    },
    {
      type: 'text',
      id: 'cert_number',
      x: 950,
      y: 120,
      content: '',
      fontSize: 14,
      fontFamily: 'monospace',
      fontWeight: 'bold',
      color: '#8B0000',
      alignment: 'left',
      dynamic: true,
      dataField: 'certificateNumber',
    },
    
    // Main Certificate Text
    {
      type: 'text',
      id: 'main_text',
      x: 600,
      y: 250,
      content: 'This is to certify that',
      fontSize: 20,
      fontFamily: 'serif',
      color: '#2F4F4F',
      alignment: 'center',
    },
    
    // User Name (Dynamic)
    {
      type: 'text',
      id: 'user_name',
      x: 600,
      y: 300,
      content: '',
      fontSize: 28,
      fontFamily: 'serif',
      fontWeight: 'bold',
      color: '#8B4513',
      alignment: 'center',
      dynamic: true,
      dataField: 'userFullName',
    },
    
    // Ownership Text
    {
      type: 'text',
      id: 'ownership_text',
      x: 600,
      y: 350,
      content: 'is the registered owner of',
      fontSize: 18,
      fontFamily: 'serif',
      color: '#2F4F4F',
      alignment: 'center',
    },
    
    // Shares Quantity (Dynamic)
    {
      type: 'text',
      id: 'shares_quantity',
      x: 600,
      y: 400,
      content: '',
      fontSize: 32,
      fontFamily: 'serif',
      fontWeight: 'bold',
      color: '#DAA520',
      alignment: 'center',
      dynamic: true,
      dataField: 'sharesQuantity',
    },
    
    // Shares Text
    {
      type: 'text',
      id: 'shares_text',
      x: 600,
      y: 440,
      content: 'fully paid ordinary shares',
      fontSize: 18,
      fontFamily: 'serif',
      color: '#2F4F4F',
      alignment: 'center',
    },
    
    // Investment Details Box
    {
      type: 'rectangle',
      id: 'details_box',
      x: 200,
      y: 500,
      width: 800,
      height: 120,
      color: '#F8F8FF',
    },
    
    // Purchase Amount Label
    {
      type: 'text',
      id: 'purchase_amount_label',
      x: 220,
      y: 530,
      content: 'Total Investment:',
      fontSize: 14,
      fontFamily: 'sans-serif',
      fontWeight: 'bold',
      color: '#2F4F4F',
      alignment: 'left',
    },
    
    // Purchase Amount (Dynamic)
    {
      type: 'text',
      id: 'purchase_amount',
      x: 220,
      y: 550,
      content: '',
      fontSize: 16,
      fontFamily: 'monospace',
      fontWeight: 'bold',
      color: '#228B22',
      alignment: 'left',
      dynamic: true,
      dataField: 'purchaseAmount',
    },
    
    // Price Per Share Label
    {
      type: 'text',
      id: 'price_per_share_label',
      x: 500,
      y: 530,
      content: 'Price Per Share:',
      fontSize: 14,
      fontFamily: 'sans-serif',
      fontWeight: 'bold',
      color: '#2F4F4F',
      alignment: 'left',
    },
    
    // Price Per Share (Dynamic)
    {
      type: 'text',
      id: 'price_per_share',
      x: 500,
      y: 550,
      content: '',
      fontSize: 16,
      fontFamily: 'monospace',
      fontWeight: 'bold',
      color: '#228B22',
      alignment: 'left',
      dynamic: true,
      dataField: 'pricePerShare',
    },
    
    // Issue Date Label
    {
      type: 'text',
      id: 'issue_date_label',
      x: 220,
      y: 580,
      content: 'Issue Date:',
      fontSize: 14,
      fontFamily: 'sans-serif',
      fontWeight: 'bold',
      color: '#2F4F4F',
      alignment: 'left',
    },
    
    // Issue Date (Dynamic)
    {
      type: 'text',
      id: 'issue_date',
      x: 220,
      y: 600,
      content: '',
      fontSize: 14,
      fontFamily: 'sans-serif',
      color: '#2F4F4F',
      alignment: 'left',
      dynamic: true,
      dataField: 'issueDate',
    },
    
    // KYC Status Label
    {
      type: 'text',
      id: 'kyc_status_label',
      x: 500,
      y: 580,
      content: 'KYC Verified:',
      fontSize: 14,
      fontFamily: 'sans-serif',
      fontWeight: 'bold',
      color: '#2F4F4F',
      alignment: 'left',
    },
    
    // KYC Status (Dynamic)
    {
      type: 'text',
      id: 'kyc_status',
      x: 500,
      y: 600,
      content: '',
      fontSize: 14,
      fontFamily: 'sans-serif',
      fontWeight: 'bold',
      color: '#228B22',
      alignment: 'left',
      dynamic: true,
      dataField: 'kycVerified',
    },
    
    // Signature Area
    {
      type: 'text',
      id: 'signature_label',
      x: 200,
      y: 700,
      content: 'Authorized Signature:',
      fontSize: 12,
      fontFamily: 'sans-serif',
      color: '#2F4F4F',
      alignment: 'left',
    },
    
    // Signature Line
    {
      type: 'line',
      id: 'signature_line',
      x: 200,
      y: 730,
      width: 300,
      height: 1,
      color: '#2F4F4F',
    },
    
    // Company Seal Area
    {
      type: 'text',
      id: 'seal_label',
      x: 700,
      y: 700,
      content: 'Company Seal',
      fontSize: 12,
      fontFamily: 'sans-serif',
      color: '#2F4F4F',
      alignment: 'center',
    },
    
    // Footer Text
    {
      type: 'text',
      id: 'footer_text',
      x: 600,
      y: 780,
      content: 'This certificate is issued in accordance with the Companies Act and represents legal ownership of shares.',
      fontSize: 10,
      fontFamily: 'sans-serif',
      color: '#696969',
      alignment: 'center',
    },
  ],
  
  securityElements: [
    // Background Watermark
    {
      type: 'watermark',
      id: 'bg_watermark',
      x: 300,
      y: 200,
      width: 600,
      height: 400,
      opacity: 0.05,
      pattern: 'AUREUS AFRICA',
    },
    
    // Security Border Pattern
    {
      type: 'pattern',
      id: 'security_pattern',
      x: 30,
      y: 30,
      width: 1140,
      height: 790,
      opacity: 0.3,
      color: '#D4AF37',
    },
    
    // Company Seal
    {
      type: 'seal',
      id: 'company_seal',
      x: 650,
      y: 720,
      width: 100,
      height: 100,
      opacity: 0.8,
      color: '#8B4513',
    },
  ],
  
  qrCode: {
    x: 1050,
    y: 650,
    size: 120,
    errorCorrectionLevel: 'H',
    margin: 4,
  },
};

// Certificate generation utilities
export class CertificateTemplateEngine {
  static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  }
  
  static formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }
  
  static formatShares(quantity: number): string {
    return `${quantity.toLocaleString()} SHARES`;
  }
  
  static formatKYCStatus(verified: boolean): string {
    return verified ? '✓ VERIFIED' : '✗ PENDING';
  }
  
  static generateVerificationUrl(certificateNumber: string, userId: number): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://aureusafrica.com';
    return `${baseUrl}/verify-certificate/${certificateNumber}?user=${userId}`;
  }
  
  static processTemplateData(data: CertificateData): Record<string, string> {
    return {
      certificateNumber: data.certificateNumber,
      userFullName: data.userFullName.toUpperCase(),
      sharesQuantity: this.formatShares(data.sharesQuantity),
      purchaseAmount: this.formatCurrency(data.purchaseAmount),
      pricePerShare: this.formatCurrency(data.pricePerShare),
      issueDate: this.formatDate(data.issueDate),
      kycVerified: this.formatKYCStatus(data.kycVerified),
      userAddress: data.userAddress,
      verificationUrl: this.generateVerificationUrl(data.certificateNumber, data.userId),
    };
  }
}

export default AUREUS_CERTIFICATE_TEMPLATE;
