const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function createVerificationTable() {
  console.log('🔧 Creating email_verification_codes table...');
  
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS email_verification_codes (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
      email TEXT NOT NULL,
      code_hash TEXT NOT NULL,
      purpose TEXT NOT NULL CHECK (purpose IN ('registration', 'account_update', 'withdrawal', 'password_reset', 'telegram_connection')),
      expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
      attempts INTEGER DEFAULT 0,
      verified_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_email_verification_codes_user_id ON email_verification_codes(user_id);
    CREATE INDEX IF NOT EXISTS idx_email_verification_codes_email ON email_verification_codes(email);
    CREATE INDEX IF NOT EXISTS idx_email_verification_codes_purpose ON email_verification_codes(purpose);
    CREATE INDEX IF NOT EXISTS idx_email_verification_codes_expires_at ON email_verification_codes(expires_at);
  `;
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql_query: createTableSQL });
    
    if (error) {
      console.error('❌ Failed to create table:', error);
    } else {
      console.log('✅ Table created successfully');
      
      // Test the table
      const { data, error: testError } = await supabase
        .from('email_verification_codes')
        .select('*')
        .limit(1);
      
      if (testError) {
        console.error('❌ Table test failed:', testError);
      } else {
        console.log('✅ Table test successful');
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

createVerificationTable();
