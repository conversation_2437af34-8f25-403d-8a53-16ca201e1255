-- ========================================
-- AUTOMATED COMPENSATION PLAN PHASE SYSTEM
-- ========================================
-- This schema supports the automated phase transition system
-- with 15% USDT + 15% Shares commission structure

-- 1. Commission Automation Rules
CREATE TABLE IF NOT EXISTS public.commission_automation_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  rule_name VARCHAR(255) NOT NULL,
  commission_type VARCHAR(50) NOT NULL, -- purchase, referral, phase_transition, bonus
  usdt_percentage DECIMAL(5,2) NOT NULL DEFAULT 15.00,
  shares_percentage DECIMAL(5,2) NOT NULL DEFAULT 15.00,
  level_multipliers DECIMAL(3,2)[] DEFAULT '{1.0, 0.5, 0.3, 0.2, 0.1}',
  max_levels INTEGER DEFAULT 5,
  is_active BOOLEAN DEFAULT TRUE,
  conditions JSONB DEFAULT '{}', -- Additional conditions for rule activation
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_commission_rules_type (commission_type),
  INDEX idx_commission_rules_active (is_active)
);

-- 2. Phase Transition Log
CREATE TABLE IF NOT EXISTS public.phase_transition_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  previous_phase_id INTEGER REFERENCES public.investment_phases(id),
  new_phase_id INTEGER REFERENCES public.investment_phases(id),
  transition_type VARCHAR(20) NOT NULL DEFAULT 'automatic', -- automatic, manual, scheduled
  trigger_reason VARCHAR(100), -- sold_out, time_based, manual_override
  shares_sold_in_previous INTEGER DEFAULT 0,
  affected_users INTEGER DEFAULT 0,
  commissions_processed INTEGER DEFAULT 0,
  total_commission_usdt DECIMAL(12,2) DEFAULT 0.00,
  total_commission_shares INTEGER DEFAULT 0,
  transition_started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  transition_completed_at TIMESTAMP WITH TIME ZONE,
  performed_by_user_id INTEGER REFERENCES public.users(id),
  metadata JSONB DEFAULT '{}',
  
  -- Indexes
  INDEX idx_phase_transition_log_date (transition_completed_at),
  INDEX idx_phase_transition_log_type (transition_type)
);

-- 3. Commission Distributions (Enhanced)
CREATE TABLE IF NOT EXISTS public.commission_distributions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  source_user_id INTEGER REFERENCES public.users(id), -- Who generated the commission
  commission_type VARCHAR(50) NOT NULL, -- purchase, referral, phase_transition, bonus
  usdt_amount DECIMAL(10,2) DEFAULT 0.00,
  shares_amount INTEGER DEFAULT 0,
  level INTEGER DEFAULT 1, -- Referral level (1-5)
  referral_chain INTEGER[] DEFAULT '{}', -- Full referral chain
  source_transaction_id UUID, -- Reference to original transaction
  phase_id INTEGER REFERENCES public.investment_phases(id),
  distribution_status VARCHAR(20) DEFAULT 'pending', -- pending, processed, failed
  processed_at TIMESTAMP WITH TIME ZONE,
  distributed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}',
  
  -- Indexes
  INDEX idx_commission_distributions_user_id (user_id),
  INDEX idx_commission_distributions_type (commission_type),
  INDEX idx_commission_distributions_status (distribution_status),
  INDEX idx_commission_distributions_date (distributed_at)
);

-- 4. User Balances (Enhanced for commission tracking)
CREATE TABLE IF NOT EXISTS public.user_balances (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  balance_type VARCHAR(50) NOT NULL, -- usdt_commission, shares_commission, purchase_balance
  amount DECIMAL(12,2) NOT NULL,
  source VARCHAR(50) NOT NULL, -- phase_transition, referral, purchase, bonus
  source_reference_id UUID, -- Reference to source transaction
  is_locked BOOLEAN DEFAULT FALSE,
  locked_until TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_user_balances_user_id (user_id),
  INDEX idx_user_balances_type (balance_type),
  INDEX idx_user_balances_source (source),
  UNIQUE(user_id, balance_type, source_reference_id)
);

-- 5. Smart Notifications System
CREATE TABLE IF NOT EXISTS public.smart_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL, -- phase_transition, commission_earned, purchase_confirmation, system_alert, marketing_tip
  priority VARCHAR(10) DEFAULT 'medium', -- low, medium, high, urgent
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  action_url TEXT,
  action_text VARCHAR(100),
  is_read BOOLEAN DEFAULT FALSE,
  is_dismissed BOOLEAN DEFAULT FALSE,
  scheduled_for TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_smart_notifications_user_id (user_id),
  INDEX idx_smart_notifications_type (type),
  INDEX idx_smart_notifications_priority (priority),
  INDEX idx_smart_notifications_read (is_read),
  INDEX idx_smart_notifications_scheduled (scheduled_for)
);

-- 6. Notification Preferences
CREATE TABLE IF NOT EXISTS public.notification_preferences (
  user_id INTEGER PRIMARY KEY REFERENCES public.users(id) ON DELETE CASCADE,
  email_enabled BOOLEAN DEFAULT TRUE,
  push_enabled BOOLEAN DEFAULT TRUE,
  telegram_enabled BOOLEAN DEFAULT TRUE,
  phase_transitions BOOLEAN DEFAULT TRUE,
  commission_updates BOOLEAN DEFAULT TRUE,
  marketing_tips BOOLEAN DEFAULT TRUE,
  system_alerts BOOLEAN DEFAULT TRUE,
  quiet_hours_start TIME,
  quiet_hours_end TIME,
  timezone VARCHAR(50) DEFAULT 'UTC',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. System Settings for Automation
CREATE TABLE IF NOT EXISTS public.system_settings (
  key VARCHAR(100) PRIMARY KEY,
  value TEXT NOT NULL,
  description TEXT,
  data_type VARCHAR(20) DEFAULT 'string', -- string, number, boolean, json
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default system settings
INSERT INTO public.system_settings (key, value, description, data_type) VALUES
('commission_automation_enabled', 'true', 'Enable/disable automated commission processing', 'boolean'),
('phase_automation_enabled', 'true', 'Enable/disable automated phase transitions', 'boolean'),
('default_usdt_commission_percentage', '15.0', 'Default USDT commission percentage', 'number'),
('default_shares_commission_percentage', '15.0', 'Default shares commission percentage', 'number'),
('max_referral_levels', '5', 'Maximum referral levels for commission distribution', 'number'),
('phase_transition_notification_delay', '300', 'Delay in seconds before sending phase transition notifications', 'number'),
('commission_processing_batch_size', '100', 'Number of commissions to process in each batch', 'number')
ON CONFLICT (key) DO UPDATE SET
  value = EXCLUDED.value,
  updated_at = NOW();

-- 8. Admin Actions Log
CREATE TABLE IF NOT EXISTS public.admin_actions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_user_id INTEGER REFERENCES public.users(id),
  action_type VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  affected_table VARCHAR(100),
  affected_record_id TEXT,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_admin_actions_admin_id (admin_user_id),
  INDEX idx_admin_actions_type (action_type),
  INDEX idx_admin_actions_date (performed_at)
);

-- ========================================
-- FUNCTIONS AND PROCEDURES
-- ========================================

-- Function to execute phase transition
CREATE OR REPLACE FUNCTION execute_phase_transition(
  current_phase_id INTEGER,
  next_phase_id INTEGER
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  affected_users INTEGER := 0;
  commissions_processed INTEGER := 0;
BEGIN
  -- Start transaction
  BEGIN
    -- Deactivate current phase
    UPDATE investment_phases 
    SET is_active = FALSE, updated_at = NOW()
    WHERE id = current_phase_id;
    
    -- Activate next phase
    UPDATE investment_phases 
    SET is_active = TRUE, start_date = NOW(), updated_at = NOW()
    WHERE id = next_phase_id;
    
    -- Get affected users count (users who made purchases in current phase)
    SELECT COUNT(DISTINCT user_id) INTO affected_users
    FROM aureus_share_purchases 
    WHERE phase_id = current_phase_id AND status = 'active';
    
    -- Process commission distributions will be handled by the service layer
    
    result := jsonb_build_object(
      'success', true,
      'current_phase_id', current_phase_id,
      'next_phase_id', next_phase_id,
      'affected_users', affected_users,
      'transition_time', NOW()
    );
    
    RETURN result;
    
  EXCEPTION WHEN OTHERS THEN
    -- Rollback on error
    RAISE;
  END;
END;
$$ LANGUAGE plpgsql;

-- Function to get phase analytics
CREATE OR REPLACE FUNCTION get_phase_analytics() 
RETURNS TABLE (
  phase_id INTEGER,
  phase_number INTEGER,
  phase_name VARCHAR(255),
  price_per_share DECIMAL(10,2),
  total_shares_available INTEGER,
  shares_sold INTEGER,
  completion_percentage DECIMAL(5,2),
  total_revenue DECIMAL(12,2),
  unique_buyers INTEGER,
  average_purchase_size DECIMAL(10,2),
  days_active INTEGER,
  projected_completion_date TIMESTAMP WITH TIME ZONE,
  velocity_shares_per_day DECIMAL(10,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ip.id,
    ip.phase_number,
    ip.phase_name,
    ip.price_per_share,
    ip.total_shares_available,
    COALESCE(sales.shares_sold, 0)::INTEGER,
    CASE 
      WHEN ip.total_shares_available > 0 THEN 
        (COALESCE(sales.shares_sold, 0) / ip.total_shares_available * 100)::DECIMAL(5,2)
      ELSE 0::DECIMAL(5,2)
    END,
    COALESCE(sales.total_revenue, 0)::DECIMAL(12,2),
    COALESCE(sales.unique_buyers, 0)::INTEGER,
    CASE 
      WHEN COALESCE(sales.unique_buyers, 0) > 0 THEN 
        (COALESCE(sales.total_revenue, 0) / sales.unique_buyers)::DECIMAL(10,2)
      ELSE 0::DECIMAL(10,2)
    END,
    CASE 
      WHEN ip.start_date IS NOT NULL THEN 
        EXTRACT(DAY FROM NOW() - ip.start_date)::INTEGER
      ELSE 0
    END,
    CASE 
      WHEN sales.velocity > 0 AND ip.total_shares_available > COALESCE(sales.shares_sold, 0) THEN
        NOW() + INTERVAL '1 day' * ((ip.total_shares_available - COALESCE(sales.shares_sold, 0)) / sales.velocity)
      ELSE NOW() + INTERVAL '365 days'
    END,
    COALESCE(sales.velocity, 0)::DECIMAL(10,2)
  FROM investment_phases ip
  LEFT JOIN (
    SELECT 
      phase_id,
      SUM(shares_purchased) as shares_sold,
      SUM(amount_usd) as total_revenue,
      COUNT(DISTINCT user_id) as unique_buyers,
      CASE 
        WHEN MIN(created_at) IS NOT NULL AND MIN(created_at) < NOW() THEN
          SUM(shares_purchased) / GREATEST(EXTRACT(DAY FROM NOW() - MIN(created_at)), 1)
        ELSE 0
      END as velocity
    FROM aureus_share_purchases 
    WHERE status = 'active'
    GROUP BY phase_id
  ) sales ON ip.id = sales.phase_id
  ORDER BY ip.phase_number;
END;
$$ LANGUAGE plpgsql;

-- Function to get commission statistics
CREATE OR REPLACE FUNCTION get_commission_statistics()
RETURNS JSONB AS $$
DECLARE
  result JSONB;
  total_distributed DECIMAL(12,2);
  usdt_distributed DECIMAL(12,2);
  shares_distributed INTEGER;
  active_rules INTEGER;
  pending_distributions INTEGER;
  last_distribution TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get total distributed amounts
  SELECT 
    COALESCE(SUM(usdt_amount + (shares_amount * 25)), 0), -- Assuming $25 per share for calculation
    COALESCE(SUM(usdt_amount), 0),
    COALESCE(SUM(shares_amount), 0)
  INTO total_distributed, usdt_distributed, shares_distributed
  FROM commission_distributions
  WHERE distribution_status = 'processed';
  
  -- Get active rules count
  SELECT COUNT(*) INTO active_rules
  FROM commission_automation_rules
  WHERE is_active = TRUE;
  
  -- Get pending distributions count
  SELECT COUNT(*) INTO pending_distributions
  FROM commission_distributions
  WHERE distribution_status = 'pending';
  
  -- Get last distribution time
  SELECT MAX(processed_at) INTO last_distribution
  FROM commission_distributions
  WHERE distribution_status = 'processed';
  
  result := jsonb_build_object(
    'totalDistributed', total_distributed,
    'usdtDistributed', usdt_distributed,
    'sharesDistributed', shares_distributed,
    'activeRules', active_rules,
    'pendingDistributions', pending_distributions,
    'lastDistribution', last_distribution
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to process pending commissions
CREATE OR REPLACE FUNCTION process_pending_commissions()
RETURNS JSONB AS $$
DECLARE
  processed_count INTEGER := 0;
  commission_record RECORD;
BEGIN
  -- Process pending commission distributions
  FOR commission_record IN 
    SELECT * FROM commission_distributions 
    WHERE distribution_status = 'pending'
    ORDER BY distributed_at
    LIMIT 100 -- Process in batches
  LOOP
    BEGIN
      -- Update user balances
      INSERT INTO user_balances (user_id, balance_type, amount, source, source_reference_id)
      VALUES 
        (commission_record.user_id, 'usdt_commission', commission_record.usdt_amount, 'commission', commission_record.id),
        (commission_record.user_id, 'shares_commission', commission_record.shares_amount, 'commission', commission_record.id)
      ON CONFLICT (user_id, balance_type, source_reference_id) DO NOTHING;
      
      -- Mark as processed
      UPDATE commission_distributions 
      SET distribution_status = 'processed', processed_at = NOW()
      WHERE id = commission_record.id;
      
      processed_count := processed_count + 1;
      
    EXCEPTION WHEN OTHERS THEN
      -- Mark as failed
      UPDATE commission_distributions 
      SET distribution_status = 'failed'
      WHERE id = commission_record.id;
    END;
  END LOOP;
  
  RETURN jsonb_build_object('processed_count', processed_count);
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- TRIGGERS AND AUTOMATION
-- ========================================

-- Trigger to automatically create notification preferences for new users
CREATE OR REPLACE FUNCTION create_default_notification_preferences()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO notification_preferences (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_create_notification_preferences
  AFTER INSERT ON users
  FOR EACH ROW
  EXECUTE FUNCTION create_default_notification_preferences();

-- Trigger to update commission automation rules timestamp
CREATE OR REPLACE FUNCTION update_commission_rule_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_commission_rule_timestamp
  BEFORE UPDATE ON commission_automation_rules
  FOR EACH ROW
  EXECUTE FUNCTION update_commission_rule_timestamp();

-- ========================================
-- PERMISSIONS AND SECURITY
-- ========================================

-- Enable Row Level Security
ALTER TABLE public.commission_automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commission_distributions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.smart_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own commission distributions" ON public.commission_distributions
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can view their own balances" ON public.user_balances
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage their own notifications" ON public.smart_notifications
  FOR ALL USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can manage their own notification preferences" ON public.notification_preferences
  FOR ALL USING (auth.uid()::text = user_id::text);

-- Admin policies
CREATE POLICY "Admins can manage commission rules" ON public.commission_automation_rules
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id::text = auth.uid()::text 
      AND role = 'admin'
    )
  );

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.commission_distributions TO authenticated;
GRANT SELECT ON public.user_balances TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.smart_notifications TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.notification_preferences TO authenticated;
GRANT SELECT ON public.system_settings TO authenticated;

-- Admin permissions
GRANT ALL ON public.commission_automation_rules TO authenticated;
GRANT ALL ON public.phase_transition_log TO authenticated;
GRANT ALL ON public.admin_actions TO authenticated;

-- Grant function permissions
GRANT EXECUTE ON FUNCTION execute_phase_transition(INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_phase_analytics() TO authenticated;
GRANT EXECUTE ON FUNCTION get_commission_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION process_pending_commissions() TO authenticated;

-- ========================================
-- INDEXES FOR PERFORMANCE
-- ========================================

-- Additional performance indexes
CREATE INDEX IF NOT EXISTS idx_commission_distributions_composite 
ON public.commission_distributions (user_id, commission_type, distribution_status);

CREATE INDEX IF NOT EXISTS idx_user_balances_composite 
ON public.user_balances (user_id, balance_type, created_at);

CREATE INDEX IF NOT EXISTS idx_smart_notifications_composite 
ON public.smart_notifications (user_id, is_read, priority, created_at);

-- ========================================
-- COMMENTS AND DOCUMENTATION
-- ========================================

COMMENT ON TABLE public.commission_automation_rules IS 'Automated commission distribution rules with 15% USDT + 15% Shares structure';
COMMENT ON TABLE public.phase_transition_log IS 'Complete audit log of all phase transitions';
COMMENT ON TABLE public.commission_distributions IS 'Individual commission distribution records';
COMMENT ON TABLE public.user_balances IS 'User balance tracking for USDT and shares commissions';
COMMENT ON TABLE public.smart_notifications IS 'Intelligent notification system with priority and scheduling';
COMMENT ON TABLE public.notification_preferences IS 'User preferences for notification delivery';
COMMENT ON TABLE public.system_settings IS 'System-wide configuration settings for automation';
COMMENT ON TABLE public.admin_actions IS 'Audit log of all administrative actions';

SELECT '🎉 AUTOMATED PHASE SYSTEM SCHEMA CREATED SUCCESSFULLY! 🎉' as status;
