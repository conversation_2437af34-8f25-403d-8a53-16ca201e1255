#!/usr/bin/env node

/**
 * CHECK CURRENT DATABASE STATE FOR TELEGRAM ID 1270124602
 * 
 * This will show exactly what data exists and where
 */

import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const checkDatabaseState = async () => {
  try {
    console.log('🔍 CHECKING CURRENT DATABASE STATE FOR TELEGRAM ID 1270124602\n');

    const telegramId = 1270124602;
    const testPassword = 'Gunst0n5o0!@#';

    // Check telegram_users table
    console.log('📋 1. TELEGRAM_USERS TABLE:');
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (telegramError) {
      console.log('❌ Error:', telegramError.message);
    } else if (telegramUser) {
      console.log('✅ Found telegram user:');
      console.log(`   ID: ${telegramUser.id}`);
      console.log(`   Telegram ID: ${telegramUser.telegram_id}`);
      console.log(`   Username: ${telegramUser.username}`);
      console.log(`   First Name: ${telegramUser.first_name}`);
      console.log(`   Last Name: ${telegramUser.last_name}`);
      console.log(`   Email: ${telegramUser.email || 'NOT SET'}`);
      console.log(`   Password Hash: ${telegramUser.password_hash ? 'EXISTS' : 'NOT SET'}`);
      console.log(`   User ID (link): ${telegramUser.user_id || 'NOT SET'}`);
      console.log(`   Is Registered: ${telegramUser.is_registered}`);
      
      // Test password if hash exists
      if (telegramUser.password_hash) {
        try {
          const isValid = await bcrypt.compare(testPassword, telegramUser.password_hash);
          console.log(`   Password Test: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
        } catch (error) {
          console.log(`   Password Test: ❌ ERROR - ${error.message}`);
        }
      }
    } else {
      console.log('❌ No telegram user found');
    }

    // Check users table by telegram_id
    console.log('\n📋 2. USERS TABLE (by telegram_id):');
    const { data: userByTelegramId, error: userByTelegramIdError } = await supabase
      .from('users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (userByTelegramIdError) {
      console.log('❌ Error:', userByTelegramIdError.message);
    } else if (userByTelegramId) {
      console.log('✅ Found user by telegram_id:');
      console.log(`   ID: ${userByTelegramId.id}`);
      console.log(`   Username: ${userByTelegramId.username}`);
      console.log(`   Email: ${userByTelegramId.email}`);
      console.log(`   Telegram ID: ${userByTelegramId.telegram_id}`);
      console.log(`   Password Hash: ${userByTelegramId.password_hash ? 'EXISTS' : 'NOT SET'}`);
      console.log(`   Is Active: ${userByTelegramId.is_active}`);
      console.log(`   Is Verified: ${userByTelegramId.is_verified}`);
      
      // Test password if hash exists
      if (userByTelegramId.password_hash) {
        try {
          const isValid = await bcrypt.compare(testPassword, userByTelegramId.password_hash);
          console.log(`   Password Test: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
        } catch (error) {
          console.log(`   Password Test: ❌ ERROR - ${error.message}`);
        }
      }
    } else {
      console.log('❌ No user found by telegram_id');
    }

    // Check users table by linked user_id (if telegram user exists)
    if (telegramUser && telegramUser.user_id) {
      console.log('\n📋 3. USERS TABLE (by linked user_id):');
      const { data: linkedUser, error: linkedUserError } = await supabase
        .from('users')
        .select('*')
        .eq('id', telegramUser.user_id)
        .single();

      if (linkedUserError) {
        console.log('❌ Error:', linkedUserError.message);
      } else if (linkedUser) {
        console.log('✅ Found linked user:');
        console.log(`   ID: ${linkedUser.id}`);
        console.log(`   Username: ${linkedUser.username}`);
        console.log(`   Email: ${linkedUser.email}`);
        console.log(`   Telegram ID: ${linkedUser.telegram_id || 'NOT SET'}`);
        console.log(`   Password Hash: ${linkedUser.password_hash ? 'EXISTS' : 'NOT SET'}`);
        console.log(`   Is Active: ${linkedUser.is_active}`);
        console.log(`   Is Verified: ${linkedUser.is_verified}`);
        
        // Test password if hash exists
        if (linkedUser.password_hash) {
          try {
            const isValid = await bcrypt.compare(testPassword, linkedUser.password_hash);
            console.log(`   Password Test: ${isValid ? '✅ VALID' : '❌ INVALID'}`);
          } catch (error) {
            console.log(`   Password Test: ❌ ERROR - ${error.message}`);
          }
        }
      } else {
        console.log('❌ No linked user found');
      }
    }

    console.log('\n📋 4. ANALYSIS:');
    console.log('   The original working login system expected:');
    console.log('   - Telegram user to exist in telegram_users table');
    console.log('   - Password hash to be in telegram_users.password_hash');
    console.log('   - Email to be in telegram_users.email');
    console.log('\n   Current state shows where the data actually is.');

  } catch (error) {
    console.error('❌ Check failed:', error);
  }
};

checkDatabaseState();
