import React, { useState } from 'react';
import { ImageUploader } from './ImageUploader';
import { CategoryManager } from './CategoryManager';
import { GalleryImageManager } from './GalleryImageManager';
import type { GalleryImage, GalleryCategory } from '../../types/gallery';

export const GalleryManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'images' | 'categories' | 'upload'>('images');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const tabs = [
    { id: 'images' as const, name: 'Manage Images', icon: '🖼️' },
    { id: 'categories' as const, name: 'Categories', icon: '📁' },
    { id: 'upload' as const, name: 'Upload Images', icon: '📤' }
  ];

  const handleUploadComplete = (images: GalleryImage[]) => {
    setRefreshTrigger(prev => prev + 1);
    setActiveTab('images');
    // Show success message
    alert(`Successfully uploaded ${images.length} image(s)!`);
  };

  const handleCategorySelect = (category: GalleryCategory) => {
    setSelectedCategory(category.id);
    setActiveTab('images');
  };

  const triggerRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Gallery Management</h2>
          <p className="text-gray-400 mt-1">
            Manage gallery images, categories, and upload new content
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={triggerRefresh}
            className="inline-flex items-center px-3 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h5M20 20v-5h-5M4 4l1.5 1.5A9 9 0 0120.5 10M20 20l-1.5-1.5A9 9 0 003.5 14" />
            </svg>
            Refresh
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeTab === tab.id
                  ? 'border-amber-400 text-amber-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                }
              `}
            >
              <span className="text-lg">{tab.icon}</span>
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {activeTab === 'images' && (
          <GalleryImageManager
            key={refreshTrigger}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            onRefresh={triggerRefresh}
          />
        )}

        {activeTab === 'categories' && (
          <CategoryManager
            onCategorySelect={handleCategorySelect}
            showCreateForm={true}
            allowEdit={true}
          />
        )}

        {activeTab === 'upload' && (
          <div className="space-y-6">
            <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                    Upload Guidelines
                  </h3>
                  <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                    <ul className="list-disc list-inside space-y-1">
                      <li>Maximum file size: 10MB per image</li>
                      <li>Supported formats: JPEG, PNG, WebP</li>
                      <li>Recommended resolution: 1920x1080 or higher</li>
                      <li>Images will be automatically optimized for web display</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <ImageUploader
              onUploadComplete={handleUploadComplete}
              allowMultiple={true}
              maxFileSize={10 * 1024 * 1024}
              acceptedTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/webp']}
              defaultCategory={selectedCategory}
            />
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-gray-700">
        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total Images</p>
              <p className="text-2xl font-semibold text-white">-</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Categories</p>
              <p className="text-2xl font-semibold text-white">-</p>
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Featured Images</p>
              <p className="text-2xl font-semibold text-white">-</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
