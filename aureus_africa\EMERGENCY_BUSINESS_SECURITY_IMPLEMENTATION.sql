-- ========================================
-- EMERGENCY BUSINESS SECURITY IMPLEMENTATION
-- ========================================
-- 
-- CRITICAL: This script implements EMERGENCY security measures
-- to prevent BUSINESS-DESTROYING financial data manipulation
--
-- ⚠️  DO NOT RUN IN PRODUCTION WITHOUT BACKUP
-- ⚠️  THESE CHANGES WILL RESTRICT DATABASE ACCESS IMMEDIATELY
--

-- ========================================
-- 1. ENABLE ROW LEVEL SECURITY ON ALL FINANCIAL TABLES
-- ========================================

-- Enable RLS on critical financial tables
ALTER TABLE public.aureus_share_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commission_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commission_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crypto_payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.investment_phases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.company_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- ========================================
-- 2. CREATE SECURE RLS POLICIES FOR FINANCIAL DATA
-- ========================================

-- Users can only view their own share purchases
CREATE POLICY "users_own_share_purchases" ON public.aureus_share_purchases
  FOR ALL USING (
    auth.uid()::text = user_id::text OR
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role IN ('super_admin', 'admin')
    )
  );

-- Users can only view their own commission balances
CREATE POLICY "users_own_commission_balances" ON public.commission_balances
  FOR ALL USING (
    auth.uid()::text = user_id::text OR
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role IN ('super_admin', 'admin')
    )
  );

-- Users can only view their own commission transactions
CREATE POLICY "users_own_commission_transactions" ON public.commission_transactions
  FOR ALL USING (
    auth.uid()::text = referrer_id::text OR 
    auth.uid()::text = referred_id::text OR
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role IN ('super_admin', 'admin')
    )
  );

-- Users can only view their own payment transactions
CREATE POLICY "users_own_payment_transactions" ON public.crypto_payment_transactions
  FOR ALL USING (
    auth.uid()::text = user_id::text OR
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role IN ('super_admin', 'admin')
    )
  );

-- Only admins can view investment phases (read-only for users)
CREATE POLICY "investment_phases_admin_only" ON public.investment_phases
  FOR SELECT USING (true);

CREATE POLICY "investment_phases_admin_modify" ON public.investment_phases
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role = 'super_admin'
    )
  );

CREATE POLICY "investment_phases_admin_update" ON public.investment_phases
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role = 'super_admin'
    )
  );

-- Only super admins can access company wallets
CREATE POLICY "company_wallets_super_admin_only" ON public.company_wallets
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role = 'super_admin'
    )
  );

-- Users can only view their own data
CREATE POLICY "users_own_data" ON public.users
  FOR ALL USING (
    auth.uid()::text = id::text OR
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role IN ('super_admin', 'admin')
    )
  );

-- Users can view referrals they're involved in
CREATE POLICY "users_own_referrals" ON public.referrals
  FOR ALL USING (
    auth.uid()::text = referrer_id::text OR 
    auth.uid()::text = referred_id::text OR
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role IN ('super_admin', 'admin')
    )
  );

-- Only admins can view audit logs
CREATE POLICY "admin_audit_logs_admin_only" ON public.admin_audit_logs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.admin_users 
      WHERE user_id = auth.uid()::integer 
      AND role IN ('super_admin', 'admin')
    )
  );

-- ========================================
-- 3. CREATE FINANCIAL SECURITY FUNCTIONS
-- ========================================

-- Function to validate financial transactions
CREATE OR REPLACE FUNCTION validate_financial_transaction(
  p_user_id INTEGER,
  p_amount DECIMAL(15,2),
  p_transaction_type TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  user_exists BOOLEAN;
  amount_valid BOOLEAN;
BEGIN
  -- Check if user exists
  SELECT EXISTS(SELECT 1 FROM users WHERE id = p_user_id) INTO user_exists;
  
  -- Validate amount
  amount_valid := p_amount > 0 AND p_amount <= 1000000; -- Max $1M per transaction
  
  -- Log validation attempt
  INSERT INTO admin_audit_logs (
    admin_email,
    action,
    target_type,
    target_id,
    metadata,
    created_at
  ) VALUES (
    'system',
    'FINANCIAL_VALIDATION',
    p_transaction_type,
    p_user_id::text,
    jsonb_build_object(
      'amount', p_amount,
      'user_exists', user_exists,
      'amount_valid', amount_valid
    ),
    NOW()
  );
  
  RETURN user_exists AND amount_valid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to secure commission updates
CREATE OR REPLACE FUNCTION secure_commission_update(
  p_user_id INTEGER,
  p_usdt_amount DECIMAL(15,2),
  p_share_amount DECIMAL(15,2),
  p_admin_id INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
  admin_authorized BOOLEAN;
  current_balance RECORD;
BEGIN
  -- Check admin authorization
  SELECT EXISTS(
    SELECT 1 FROM admin_users 
    WHERE user_id = p_admin_id 
    AND role IN ('super_admin', 'admin')
    AND is_active = true
  ) INTO admin_authorized;
  
  IF NOT admin_authorized THEN
    RAISE EXCEPTION 'Unauthorized commission update attempt';
  END IF;
  
  -- Get current balance
  SELECT * FROM commission_balances WHERE user_id = p_user_id INTO current_balance;
  
  -- Log the update
  INSERT INTO admin_audit_logs (
    admin_email,
    action,
    target_type,
    target_id,
    metadata,
    created_at
  ) VALUES (
    (SELECT email FROM admin_users WHERE user_id = p_admin_id),
    'COMMISSION_UPDATE',
    'commission_balances',
    p_user_id::text,
    jsonb_build_object(
      'previous_usdt', COALESCE(current_balance.usdt_balance, 0),
      'previous_shares', COALESCE(current_balance.share_balance, 0),
      'new_usdt', p_usdt_amount,
      'new_shares', p_share_amount,
      'admin_id', p_admin_id
    ),
    NOW()
  );
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to secure share purchase creation
CREATE OR REPLACE FUNCTION secure_share_purchase(
  p_user_id INTEGER,
  p_shares_purchased DECIMAL(15,2),
  p_total_amount DECIMAL(15,2),
  p_payment_id TEXT
) RETURNS UUID AS $$
DECLARE
  purchase_id UUID;
  payment_verified BOOLEAN;
BEGIN
  -- Generate secure purchase ID
  purchase_id := gen_random_uuid();
  
  -- Verify payment exists and is approved
  SELECT EXISTS(
    SELECT 1 FROM crypto_payment_transactions 
    WHERE id = p_payment_id::uuid 
    AND user_id = p_user_id 
    AND status = 'approved'
    AND amount = p_total_amount
  ) INTO payment_verified;
  
  IF NOT payment_verified THEN
    RAISE EXCEPTION 'Invalid or unverified payment for share purchase';
  END IF;
  
  -- Create share purchase with validation
  INSERT INTO aureus_share_purchases (
    id,
    user_id,
    shares_purchased,
    total_amount,
    status,
    created_at
  ) VALUES (
    purchase_id,
    p_user_id,
    p_shares_purchased,
    p_total_amount,
    'active',
    NOW()
  );
  
  -- Log the purchase
  INSERT INTO admin_audit_logs (
    admin_email,
    action,
    target_type,
    target_id,
    metadata,
    created_at
  ) VALUES (
    'system',
    'SHARE_PURCHASE_CREATED',
    'aureus_share_purchases',
    purchase_id::text,
    jsonb_build_object(
      'user_id', p_user_id,
      'shares', p_shares_purchased,
      'amount', p_total_amount,
      'payment_id', p_payment_id
    ),
    NOW()
  );
  
  RETURN purchase_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 4. CREATE FINANCIAL AUDIT TRIGGERS
-- ========================================

-- Trigger function for financial data changes
CREATE OR REPLACE FUNCTION audit_financial_changes() RETURNS TRIGGER AS $$
BEGIN
  -- Log all financial data modifications
  INSERT INTO admin_audit_logs (
    admin_email,
    action,
    target_type,
    target_id,
    metadata,
    created_at
  ) VALUES (
    COALESCE(current_setting('app.current_user_email', true), 'unknown'),
    TG_OP,
    TG_TABLE_NAME,
    COALESCE(NEW.id::text, OLD.id::text),
    jsonb_build_object(
      'old_data', to_jsonb(OLD),
      'new_data', to_jsonb(NEW),
      'table', TG_TABLE_NAME,
      'operation', TG_OP
    ),
    NOW()
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers on all financial tables
CREATE TRIGGER audit_share_purchases
  AFTER INSERT OR UPDATE OR DELETE ON aureus_share_purchases
  FOR EACH ROW EXECUTE FUNCTION audit_financial_changes();

CREATE TRIGGER audit_commission_balances
  AFTER INSERT OR UPDATE OR DELETE ON commission_balances
  FOR EACH ROW EXECUTE FUNCTION audit_financial_changes();

CREATE TRIGGER audit_commission_transactions
  AFTER INSERT OR UPDATE OR DELETE ON commission_transactions
  FOR EACH ROW EXECUTE FUNCTION audit_financial_changes();

CREATE TRIGGER audit_payment_transactions
  AFTER INSERT OR UPDATE OR DELETE ON crypto_payment_transactions
  FOR EACH ROW EXECUTE FUNCTION audit_financial_changes();

-- ========================================
-- 5. GRANT MINIMAL NECESSARY PERMISSIONS
-- ========================================

-- Revoke all permissions and grant minimal access
REVOKE ALL ON ALL TABLES IN SCHEMA public FROM anon, authenticated;

-- Grant basic read access to authenticated users (filtered by RLS)
GRANT SELECT ON public.users TO authenticated;
GRANT SELECT ON public.aureus_share_purchases TO authenticated;
GRANT SELECT ON public.commission_balances TO authenticated;
GRANT SELECT ON public.commission_transactions TO authenticated;
GRANT SELECT ON public.crypto_payment_transactions TO authenticated;
GRANT SELECT ON public.investment_phases TO authenticated;
GRANT SELECT ON public.referrals TO authenticated;

-- Grant insert permissions for user actions (still filtered by RLS)
GRANT INSERT ON public.crypto_payment_transactions TO authenticated;

-- Grant execute permissions on security functions
GRANT EXECUTE ON FUNCTION validate_financial_transaction TO authenticated;
GRANT EXECUTE ON FUNCTION secure_commission_update TO authenticated;
GRANT EXECUTE ON FUNCTION secure_share_purchase TO authenticated;

-- ========================================
-- SECURITY IMPLEMENTATION COMPLETE
-- ========================================

-- Log security implementation
INSERT INTO admin_audit_logs (
  admin_email,
  action,
  target_type,
  target_id,
  metadata,
  created_at
) VALUES (
  'system',
  'EMERGENCY_SECURITY_IMPLEMENTED',
  'database_security',
  'all_tables',
  jsonb_build_object(
    'rls_enabled', true,
    'policies_created', true,
    'audit_triggers_created', true,
    'security_functions_created', true,
    'implementation_date', NOW()
  ),
  NOW()
);

-- Display security status
SELECT 
  'EMERGENCY BUSINESS SECURITY IMPLEMENTED' as status,
  'Row Level Security enabled on all financial tables' as rls_status,
  'Audit triggers created for all financial operations' as audit_status,
  'Security functions implemented for financial validation' as validation_status,
  NOW() as implementation_time;
