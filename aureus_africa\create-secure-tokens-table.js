#!/usr/bin/env node

/**
 * CREATE SECURE TOKENS TABLE
 * 
 * This script creates the secure_tokens table for enhanced token management
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createSecureTokensTable() {
  console.log('🔐 Creating secure_tokens table...');
  
  try {
    // Test if table already exists
    const { data: existingTable, error: testError } = await supabase
      .from('secure_tokens')
      .select('id')
      .limit(1);

    if (!testError) {
      console.log('✅ secure_tokens table already exists');
      return true;
    }

    console.log('📝 Table does not exist, manual creation required...');
    console.log('\n🔧 MANUAL SETUP INSTRUCTIONS:');
    console.log('=====================================');
    console.log('1. Go to Supabase Dashboard > SQL Editor');
    console.log('2. Copy and paste the following SQL:');
    console.log('\n-- CREATE SECURE TOKENS TABLE');
    console.log(`
-- Create secure_tokens table for enhanced token management
CREATE TABLE IF NOT EXISTS public.secure_tokens (
    id SERIAL PRIMARY KEY,
    token VARCHAR(80) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    token_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_revoked BOOLEAN DEFAULT false,
    revoked_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    
    -- Foreign key constraint
    CONSTRAINT fk_secure_tokens_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES public.users(id) 
        ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_secure_tokens_token ON public.secure_tokens(token);
CREATE INDEX IF NOT EXISTS idx_secure_tokens_user_id ON public.secure_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_secure_tokens_type ON public.secure_tokens(token_type);
CREATE INDEX IF NOT EXISTS idx_secure_tokens_expires_at ON public.secure_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_secure_tokens_active ON public.secure_tokens(is_revoked) WHERE is_revoked = false;

-- Enable Row Level Security
ALTER TABLE public.secure_tokens ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own tokens
CREATE POLICY "secure_tokens_user_policy" ON public.secure_tokens
    FOR ALL USING (
        user_id = (auth.jwt() ->> 'sub')::integer OR
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE user_id = (auth.jwt() ->> 'sub')::integer 
            AND is_active = true
        ) OR
        current_setting('role') = 'service_role'
    );

-- Create function to automatically clean up expired tokens
CREATE OR REPLACE FUNCTION cleanup_expired_secure_tokens() RETURNS void AS $$
BEGIN
    UPDATE public.secure_tokens 
    SET is_revoked = true, revoked_at = NOW()
    WHERE expires_at < NOW() AND is_revoked = false;
    
    -- Log cleanup activity
    INSERT INTO admin_audit_logs (
        admin_email,
        action,
        target_type,
        target_id,
        metadata,
        created_at
    ) VALUES (
        'token_cleanup',
        'EXPIRED_SECURE_TOKENS_CLEANED',
        'token_maintenance',
        'automatic',
        jsonb_build_object(
            'cleanup_time', NOW(),
            'expired_tokens_count', (
                SELECT COUNT(*) FROM public.secure_tokens 
                WHERE expires_at < NOW() AND is_revoked = true 
                AND revoked_at > NOW() - INTERVAL '1 minute'
            )
        ),
        NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to prevent token reuse
CREATE OR REPLACE FUNCTION prevent_token_reuse() RETURNS TRIGGER AS $$
BEGIN
    -- Check if token already exists
    IF EXISTS (SELECT 1 FROM public.secure_tokens WHERE token = NEW.token AND id != NEW.id) THEN
        RAISE EXCEPTION 'Token already exists - potential security issue';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_prevent_token_reuse
    BEFORE INSERT OR UPDATE ON public.secure_tokens
    FOR EACH ROW
    EXECUTE FUNCTION prevent_token_reuse();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.secure_tokens TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.secure_tokens TO service_role;
GRANT USAGE ON SEQUENCE secure_tokens_id_seq TO authenticated;
GRANT USAGE ON SEQUENCE secure_tokens_id_seq TO service_role;

-- Create view for token statistics (admin only)
CREATE OR REPLACE VIEW token_security_stats AS
SELECT 
    token_type,
    COUNT(*) as total_tokens,
    COUNT(*) FILTER (WHERE is_revoked = false AND expires_at > NOW()) as active_tokens,
    COUNT(*) FILTER (WHERE is_revoked = true) as revoked_tokens,
    COUNT(*) FILTER (WHERE expires_at <= NOW() AND is_revoked = false) as expired_tokens,
    AVG(EXTRACT(EPOCH FROM (expires_at - created_at))/3600) as avg_lifetime_hours,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '24 hours') as tokens_last_24h
FROM public.secure_tokens
WHERE created_at > NOW() - INTERVAL '7 days'
GROUP BY token_type
ORDER BY total_tokens DESC;

-- Grant view access to service role only
GRANT SELECT ON token_security_stats TO service_role;

-- Log table creation
INSERT INTO admin_audit_logs (
    admin_email,
    action,
    target_type,
    target_id,
    metadata,
    created_at
) VALUES (
    'security_system',
    'SECURE_TOKENS_TABLE_CREATED',
    'database_schema',
    'secure_tokens',
    jsonb_build_object(
        'table_name', 'secure_tokens',
        'creation_date', NOW(),
        'security_features', ARRAY['RLS_enabled', 'automatic_cleanup', 'token_uniqueness', 'statistics_view'],
        'purpose', 'enhanced_token_security'
    ),
    NOW()
);

COMMENT ON TABLE public.secure_tokens IS 'Enhanced secure token management with automatic expiration and revocation';
COMMENT ON COLUMN public.secure_tokens.token IS 'Cryptographically secure token (up to 80 chars)';
COMMENT ON COLUMN public.secure_tokens.token_type IS 'Type of token (auth, password_reset, email_verification, etc.)';
COMMENT ON COLUMN public.secure_tokens.expires_at IS 'Token expiration time (varies by type)';
COMMENT ON COLUMN public.secure_tokens.is_revoked IS 'Whether token has been revoked';
COMMENT ON COLUMN public.secure_tokens.metadata IS 'Additional token metadata (JSON)';
    `);

    console.log('\n3. Execute the SQL script');
    console.log('4. Re-run this script to verify creation');
    console.log('\n⚠️ This table is required for enhanced token security functionality');

    return false;

  } catch (error) {
    console.error('❌ Secure tokens table creation failed:', error);
    return false;
  }
}

// Test enhanced token security system
async function testEnhancedTokenSecurity() {
  console.log('\n🧪 Testing enhanced token security system...');
  
  try {
    // Test table access
    const { data: tokenTest, error: tokenError } = await supabase
      .from('secure_tokens')
      .select('count')
      .limit(0);

    if (tokenError) {
      console.log('❌ Cannot access secure_tokens table');
      return false;
    }

    console.log('✅ secure_tokens table accessible');

    // Test token generation
    console.log('   🧪 Testing token generation...');
    const crypto = await import('crypto');
    
    // Test different token lengths
    const token32 = crypto.randomBytes(16).toString('hex'); // 32 chars
    const token64 = crypto.randomBytes(32).toString('hex'); // 64 chars
    const token80 = crypto.randomBytes(40).toString('hex'); // 80 chars

    if (token32.length !== 32 || token64.length !== 64 || token80.length !== 80) {
      console.log('❌ Token generation length test failed');
      return false;
    }

    // Test uniqueness
    const token1 = crypto.randomBytes(32).toString('hex');
    const token2 = crypto.randomBytes(32).toString('hex');
    
    if (token1 === token2) {
      console.log('❌ Token uniqueness test failed');
      return false;
    }

    console.log('✅ Token generation working correctly');

    // Test enhanced token security import
    try {
      const { enhancedTokenSecurity } = await import('./lib/enhancedTokenSecurity.js');
      console.log('✅ Enhanced token security manager imported');
      
      // Test token type configurations
      const tokenTypes = ['auth', 'password_reset', 'email_verification', 'api_key', 'session'];
      console.log(`✅ Token types configured: ${tokenTypes.join(', ')}`);
      
    } catch (importError) {
      console.log('⚠️ Enhanced token security import issue:', importError.message);
    }

    console.log('✅ Enhanced token security system test completed');
    return true;

  } catch (error) {
    console.error('❌ Enhanced token security test failed:', error);
    return false;
  }
}

// Run the setup
console.log('🚀 Starting enhanced token security setup...\n');

createSecureTokensTable()
  .then(success => {
    if (success) {
      return testEnhancedTokenSecurity();
    } else {
      console.log('\n❌ Table creation required before testing');
      return false;
    }
  })
  .then(testSuccess => {
    if (testSuccess) {
      console.log('\n✅ Enhanced token security system ready!');
      console.log('📋 Features available:');
      console.log('   ✅ Cryptographically secure token generation');
      console.log('   ✅ Multiple token types (auth, reset, verification, API)');
      console.log('   ✅ Automatic token expiration');
      console.log('   ✅ Token revocation capabilities');
      console.log('   ✅ Security event logging');
      console.log('   ✅ Token statistics and monitoring');
      process.exit(0);
    } else {
      console.log('\n⚠️ Enhanced token security needs manual setup');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
