import React, { useState, useEffect } from 'react'
import { supabase } from '../../lib/supabase'
import { logAdminAction } from '../../lib/adminAuth'

interface ReferralData {
  id: string
  referrer_id: number
  referred_id: number
  status: string
  created_at: string
  referrer: {
    username: string
    email: string
    full_name?: string
  }
  referred: {
    username: string
    email: string
    full_name?: string
  }
}

interface DownlineStats {
  totalReferrals: number
  activeReferrals: number
  totalCommissions: number
  topReferrers: Array<{
    user_id: number
    username: string
    email: string
    referral_count: number
    total_commissions: number
  }>
}

export const DownlineManager: React.FC = () => {
  const [referrals, setReferrals] = useState<ReferralData[]>([])
  const [stats, setStats] = useState<DownlineStats>({
    totalReferrals: 0,
    activeReferrals: 0,
    totalCommissions: 0,
    topReferrers: []
  })
  const [loading, setLoading] = useState(true)
  const [selectedReferral, setSelectedReferral] = useState<ReferralData | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadDownlineData()
  }, [])

  const loadDownlineData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        loadReferrals(),
        loadStats()
      ])
    } catch (error) {
      console.error('Error loading downline data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadReferrals = async () => {
    try {
      const { data, error } = await supabase
        .from('referrals')
        .select(`
          *,
          referrer:referrer_id (
            username,
            email,
            full_name
          ),
          referred:referred_id (
            username,
            email,
            full_name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(100)

      if (error) throw error
      setReferrals(data || [])
    } catch (error) {
      console.error('Error loading referrals:', error)
    }
  }

  const loadStats = async () => {
    try {
      // Get total referrals count
      const { count: totalCount, error: totalError } = await supabase
        .from('referrals')
        .select('*', { count: 'exact', head: true })

      if (totalError) throw totalError

      // Get active referrals count
      const { count: activeCount, error: activeError } = await supabase
        .from('referrals')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active')

      if (activeError) throw activeError

      // Get total commissions
      const { data: commissions, error: commissionError } = await supabase
        .from('commission_transactions')
        .select('usdt_commission')
        .eq('status', 'approved')

      if (commissionError) throw commissionError

      const totalCommissions = commissions?.reduce((sum, c) => sum + (c.usdt_commission || 0), 0) || 0

      // Get top referrers
      const { data: topReferrersData, error: topReferrersError } = await supabase
        .rpc('get_top_referrers', { limit_count: 10 })

      if (topReferrersError) {
        console.error('Error loading top referrers:', topReferrersError)
      }

      setStats({
        totalReferrals: totalCount || 0,
        activeReferrals: activeCount || 0,
        totalCommissions,
        topReferrers: topReferrersData || []
      })

    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const updateReferralStatus = async (referralId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('referrals')
        .update({ status: newStatus })
        .eq('id', referralId)

      if (error) throw error

      await logAdminAction('update_referral_status', {
        referral_id: referralId,
        new_status: newStatus
      })

      alert('Referral status updated successfully!')
      loadDownlineData()
    } catch (error) {
      console.error('Error updating referral status:', error)
      alert('Error updating referral status: ' + (error as Error).message)
    }
  }

  const deleteReferral = async (referralId: string) => {
    if (!confirm('Are you sure you want to delete this referral? This action cannot be undone.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('referrals')
        .delete()
        .eq('id', referralId)

      if (error) throw error

      await logAdminAction('delete_referral', {
        referral_id: referralId
      })

      alert('Referral deleted successfully!')
      loadDownlineData()
    } catch (error) {
      console.error('Error deleting referral:', error)
      alert('Error deleting referral: ' + (error as Error).message)
    }
  }

  const filteredReferrals = referrals.filter(referral =>
    referral.referrer?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    referral.referrer?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    referral.referred?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    referral.referred?.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white">Loading downline data...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Downline Management</h2>
        <button
          onClick={loadDownlineData}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
        >
          Refresh Data
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-2">Total Referrals</h3>
          <p className="text-3xl font-bold text-blue-400">{stats.totalReferrals}</p>
        </div>
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-2">Active Referrals</h3>
          <p className="text-3xl font-bold text-green-400">{stats.activeReferrals}</p>
        </div>
        <div className="bg-gray-800 p-6 rounded-lg border border-gray-600">
          <h3 className="text-lg font-semibold text-white mb-2">Total Commissions</h3>
          <p className="text-3xl font-bold text-yellow-400">${stats.totalCommissions.toFixed(2)}</p>
        </div>
      </div>

      {/* Search */}
      <div className="bg-gray-800 p-4 rounded-lg border border-gray-600">
        <input
          type="text"
          placeholder="Search by username or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white"
        />
      </div>

      {/* Referrals Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-600">
          <h3 className="text-xl font-semibold text-white">Referral Relationships</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Referrer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Referred User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Created Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-600">
              {filteredReferrals.map((referral) => (
                <tr key={referral.id} className="hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-white">
                        {referral.referrer?.username || 'Unknown'}
                      </div>
                      <div className="text-sm text-gray-400">
                        {referral.referrer?.email}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-white">
                        {referral.referred?.username || 'Unknown'}
                      </div>
                      <div className="text-sm text-gray-400">
                        {referral.referred?.email}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      referral.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : referral.status === 'inactive'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {referral.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    {new Date(referral.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <select
                        value={referral.status}
                        onChange={(e) => updateReferralStatus(referral.id, e.target.value)}
                        className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs"
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="pending">Pending</option>
                      </select>
                      <button
                        onClick={() => setSelectedReferral(referral)}
                        className="text-blue-400 hover:text-blue-300"
                      >
                        View
                      </button>
                      <button
                        onClick={() => deleteReferral(referral.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Top Referrers */}
      {stats.topReferrers.length > 0 && (
        <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-600">
            <h3 className="text-xl font-semibold text-white">Top Referrers</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Rank
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Referrals
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Total Commissions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-600">
                {stats.topReferrers.map((referrer, index) => (
                  <tr key={referrer.user_id} className="hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      #{index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-white">{referrer.username}</div>
                        <div className="text-sm text-gray-400">{referrer.email}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {referrer.referral_count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      ${referrer.total_commissions.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Referral Details Modal */}
      {selectedReferral && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 p-6 rounded-lg border border-gray-600 max-w-2xl w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-white">Referral Details</h3>
              <button
                onClick={() => setSelectedReferral(null)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-1">Referral ID</label>
                <p className="text-white">{selectedReferral.id}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-300 mb-1">Referrer</label>
                  <p className="text-white">{selectedReferral.referrer?.username}</p>
                  <p className="text-gray-400 text-sm">{selectedReferral.referrer?.email}</p>
                </div>
                <div>
                  <label className="block text-gray-300 mb-1">Referred User</label>
                  <p className="text-white">{selectedReferral.referred?.username}</p>
                  <p className="text-gray-400 text-sm">{selectedReferral.referred?.email}</p>
                </div>
              </div>
              <div>
                <label className="block text-gray-300 mb-1">Status</label>
                <p className="text-white">{selectedReferral.status}</p>
              </div>
              <div>
                <label className="block text-gray-300 mb-1">Created Date</label>
                <p className="text-white">{new Date(selectedReferral.created_at).toLocaleString()}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
