import React, { useEffect, useCallback, useState } from 'react';
import type { GalleryLightboxProps } from '../../types/gallery';

export const GalleryLightbox: React.FC<GalleryLightboxProps> = ({
  images,
  currentIndex,
  isOpen,
  onClose,
  onNext,
  onPrevious
}) => {
  const currentImage = images[currentIndex];
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Keyboard navigation
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'Escape':
        onClose();
        break;
      case 'ArrowLeft':
        onPrevious();
        break;
      case 'ArrowRight':
        onNext();
        break;
    }
  }, [isOpen, onClose, onNext, onPrevious]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Touch/swipe navigation
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && images.length > 1) {
      onNext();
    }
    if (isRightSwipe && images.length > 1) {
      onPrevious();
    }
  };

  // Prevent body scroll when lightbox is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen || !currentImage) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Enhanced Backdrop with blur */}
      <div
        className="absolute inset-0 bg-black bg-opacity-95 backdrop-blur-sm transition-all duration-300"
        onClick={onClose}
      />

      {/* Content */}
      <div className="relative z-10 w-full h-full flex flex-col">
        {/* Enhanced Header with gold accents */}
        <div className="flex items-center justify-between p-4 lg:p-6 text-white bg-gradient-to-r from-black/50 to-transparent">
          <div className="flex-1">
            <p className="text-sm lg:text-base text-amber-300 font-medium">
              {currentIndex + 1} of {images.length}
            </p>
          </div>

          {/* Enhanced Close Button */}
          <button
            onClick={onClose}
            className="ml-4 p-3 lg:p-4 rounded-full bg-black/30 hover:bg-amber-600/20 border border-amber-500/30 hover:border-amber-400 transition-all duration-200 group"
            aria-label="Close lightbox"
          >
            <svg className="w-6 h-6 lg:w-7 lg:h-7 text-amber-400 group-hover:text-amber-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Enhanced Image Container */}
        <div
          className="flex-1 flex items-center justify-center p-4 lg:p-8 overflow-hidden"
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
        >
          <div className="relative w-full h-full flex items-center justify-center">
            <img
              src={currentImage.image_url}
              alt={currentImage.alt_text || "Gallery image"}
              className="rounded-lg shadow-2xl lightbox-image"
              style={{
                maxHeight: 'calc(100vh - 200px)',
                maxWidth: 'calc(100vw - 100px)',
                width: 'auto',
                height: 'auto',
                objectFit: 'contain',
                display: 'block'
              }}
              onLoad={(e) => {
                const img = e.target as HTMLImageElement;
                console.log('Lightbox image loaded:', {
                  src: img.src,
                  naturalWidth: img.naturalWidth,
                  naturalHeight: img.naturalHeight,
                  displayWidth: img.width,
                  displayHeight: img.height,
                  computedStyle: window.getComputedStyle(img)
                });
              }}
            />

            {/* Enhanced Navigation Buttons */}
            {images.length > 1 && (
              <>
                {/* Previous Button */}
                <button
                  onClick={onPrevious}
                  className="absolute left-2 lg:left-4 top-1/2 transform -translate-y-1/2 p-3 lg:p-4 rounded-full bg-black/60 hover:bg-black/80 border border-amber-500/30 hover:border-amber-400 text-amber-400 hover:text-amber-300 transition-all duration-200 group backdrop-blur-sm"
                  aria-label="Previous image"
                >
                  <svg className="w-6 h-6 lg:w-8 lg:h-8 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>

                {/* Next Button */}
                <button
                  onClick={onNext}
                  className="absolute right-2 lg:right-4 top-1/2 transform -translate-y-1/2 p-3 lg:p-4 rounded-full bg-black/60 hover:bg-black/80 border border-amber-500/30 hover:border-amber-400 text-amber-400 hover:text-amber-300 transition-all duration-200 group backdrop-blur-sm"
                  aria-label="Next image"
                >
                  <svg className="w-6 h-6 lg:w-8 lg:h-8 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            )}
          </div>
        </div>

        {/* Enhanced Footer */}
        <div className="p-4 lg:p-6 text-white bg-gradient-to-t from-black/50 to-transparent">
          <div className="max-w-4xl mx-auto">
            {/* Enhanced Image Info */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-xs lg:text-sm text-gray-400 space-y-2 sm:space-y-0">
              <div className="flex items-center space-x-4">
                {currentImage.width && currentImage.height && (
                  <span className="text-amber-400">{currentImage.width} × {currentImage.height}</span>
                )}
                {currentImage.file_size && (
                  <span>{formatFileSize(currentImage.file_size)}</span>
                )}
                <span>
                  {new Date(currentImage.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>

            {/* Thumbnail Navigation */}
            {images.length > 1 && (
              <div className="mt-4">
                <div className="flex items-center justify-center space-x-2 overflow-x-auto pb-2">
                  {images.map((image, index) => (
                    <button
                      key={image.id}
                      onClick={() => {
                        const event = new CustomEvent('lightbox-navigate', { detail: index });
                        document.dispatchEvent(event);
                      }}
                      className={`
                        flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all
                        ${index === currentIndex 
                          ? 'border-amber-400 opacity-100' 
                          : 'border-transparent opacity-60 hover:opacity-80'
                        }
                      `}
                    >
                      <img
                        src={image.image_url}
                        alt="Gallery thumbnail"
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Utility function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Custom hook for lightbox navigation
export const useLightboxNavigation = (
  images: any[], 
  onNavigate: (index: number) => void
) => {
  useEffect(() => {
    const handleNavigate = (e: CustomEvent) => {
      onNavigate(e.detail);
    };

    document.addEventListener('lightbox-navigate', handleNavigate as EventListener);
    return () => {
      document.removeEventListener('lightbox-navigate', handleNavigate as EventListener);
    };
  }, [onNavigate]);
};
