# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Application Configuration
VITE_APP_NAME=Aureus Alliance Holdings
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production

# Debug Mode (set to true for additional logging)
VITE_DEBUG=false

# Optional: Analytics
VITE_GA_TRACKING_ID=your-ga-id-here

# Optional: Error Tracking
VITE_SENTRY_DSN=your-sentry-dsn-here

# Railway Deployment
NODE_ENV=production
PORT=3000

# Resend Email Service Configuration
RESEND_API_KEY=re_xxxxxxxxxx_your_resend_api_key_here
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=Aureus Alliance Holdings
RESEND_DOMAIN=aureusalliance.com

# Email Verification Settings
EMAIL_VERIFICATION_EXPIRY_MINUTES=15
EMAIL_VERIFICATION_MAX_ATTEMPTS=3
EMAIL_RATE_LIMIT_WINDOW_MINUTES=10
