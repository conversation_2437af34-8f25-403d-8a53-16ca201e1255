import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA'

// Create admin client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function fixAdminLogin() {
  const email = '<EMAIL>'
  const newPassword = 'Underdog84061551000085@1'

  console.log('🔧 Fixing admin login for:', email)

  try {
    // First, check if user exists in auth
    const { data: authUsers, error: listError } = await supabase.auth.admin.listUsers()

    if (listError) {
      console.error('❌ Error listing users:', listError)
      return
    }

    const existingAuthUser = authUsers.users.find(user => user.email === email)

    if (existingAuthUser) {
      console.log('✅ Found existing auth user:', existingAuthUser.id)

      // Update the password
      const { data: updateData, error: updateError } = await supabase.auth.admin.updateUserById(
        existingAuthUser.id,
        { password: newPassword }
      )

      if (updateError) {
        console.error('❌ Error updating password:', updateError)
        return
      }

      console.log('✅ Password updated successfully!')
      console.log('🎉 You can now login with:')
      console.log('   Email:', email)
      console.log('   Password:', newPassword)

    } else {
      console.log('❌ No auth user found for:', email)
      console.log('💡 Try using Telegram ID login instead')
    }

  } catch (error) {
    console.error('❌ Script error:', error)
  }
}

fixAdminLogin()