import React, { useState, useEffect, useMemo } from 'react';
import { supabase } from '../../lib/supabase';

interface CurrentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
}

interface PurchaseCalculatorProps {
  amount: string;
  currentPhase: CurrentPhase | null;
  onAmountChange: (amount: string) => void;
  showAdvanced?: boolean;
}

export const PurchaseCalculator: React.FC<PurchaseCalculatorProps> = ({
  amount,
  currentPhase,
  onAmountChange,
  showAdvanced = false
}) => {
  const [calculatorMode, setCalculatorMode] = useState<'amount' | 'shares'>('amount');
  const [targetShares, setTargetShares] = useState<string>('');
  const [futurePhases, setFuturePhases] = useState<CurrentPhase[]>([]);
  const [showProjections, setShowProjections] = useState(false);

  useEffect(() => {
    if (showAdvanced) {
      loadFuturePhases();
    }
  }, [showAdvanced]);

  const loadFuturePhases = async () => {
    try {
      const { data, error } = await supabase
        .from('investment_phases')
        .select('*')
        .gt('phase_number', currentPhase?.phase_number || 0)
        .order('phase_number', { ascending: true })
        .limit(5);

      if (!error && data) {
        setFuturePhases(data);
      }
    } catch (error) {
      console.error('Error loading future phases:', error);
    }
  };

  const calculations = useMemo(() => {
    if (!currentPhase) return null;

    const purchaseAmount = parseFloat(amount) || 0;
    const shares = Math.floor(purchaseAmount / currentPhase.price_per_share);
    const totalCost = shares * currentPhase.price_per_share;
    const remainingAmount = purchaseAmount - totalCost;

    // Calculate potential future value
    const futureValue = futurePhases.length > 0 
      ? shares * futurePhases[futurePhases.length - 1].price_per_share
      : totalCost;

    const potentialGain = futureValue - totalCost;
    const potentialGainPercent = totalCost > 0 ? (potentialGain / totalCost) * 100 : 0;

    // Calculate dividend projections (simplified)
    const annualDividendRate = 0.12; // 12% annual dividend assumption
    const annualDividends = totalCost * annualDividendRate;
    const monthlyDividends = annualDividends / 12;

    return {
      purchaseAmount,
      shares,
      totalCost,
      remainingAmount,
      futureValue,
      potentialGain,
      potentialGainPercent,
      annualDividends,
      monthlyDividends,
      pricePerShare: currentPhase.price_per_share,
      remainingShares: currentPhase.total_shares_available - currentPhase.shares_sold
    };
  }, [amount, currentPhase, futurePhases]);

  const handleSharesInput = (sharesValue: string) => {
    setTargetShares(sharesValue);
    if (currentPhase && sharesValue) {
      const shares = parseInt(sharesValue) || 0;
      const requiredAmount = shares * currentPhase.price_per_share;
      onAmountChange(requiredAmount.toString());
    }
  };

  if (!calculations) return null;

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151',
      marginBottom: '24px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
          🧮 Purchase Calculator
        </h3>
        
        {showAdvanced && (
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={() => setCalculatorMode('amount')}
              style={{
                padding: '6px 12px',
                backgroundColor: calculatorMode === 'amount' ? '#3b82f6' : 'transparent',
                border: '1px solid #3b82f6',
                borderRadius: '6px',
                color: calculatorMode === 'amount' ? 'white' : '#60a5fa',
                fontSize: '12px',
                cursor: 'pointer'
              }}
            >
              By Amount
            </button>
            <button
              onClick={() => setCalculatorMode('shares')}
              style={{
                padding: '6px 12px',
                backgroundColor: calculatorMode === 'shares' ? '#3b82f6' : 'transparent',
                border: '1px solid #3b82f6',
                borderRadius: '6px',
                color: calculatorMode === 'shares' ? 'white' : '#60a5fa',
                fontSize: '12px',
                cursor: 'pointer'
              }}
            >
              By Shares
            </button>
          </div>
        )}
      </div>

      {/* Calculator Mode Toggle */}
      {showAdvanced && calculatorMode === 'shares' && (
        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
            Target Shares
          </label>
          <input
            type="number"
            value={targetShares}
            onChange={(e) => handleSharesInput(e.target.value)}
            placeholder="Enter number of shares"
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              border: '1px solid #4b5563',
              borderRadius: '8px',
              color: 'white',
              fontSize: '16px'
            }}
          />
        </div>
      )}

      {/* Main Calculations */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
        gap: '16px',
        marginBottom: '20px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ color: '#3b82f6', fontSize: '20px', fontWeight: 'bold' }}>
            {calculations.shares.toLocaleString()}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Shares</div>
        </div>
        
        <div style={{ textAlign: 'center' }}>
          <div style={{ color: '#10b981', fontSize: '20px', fontWeight: 'bold' }}>
            ${calculations.totalCost.toFixed(2)}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Total Cost</div>
        </div>
        
        <div style={{ textAlign: 'center' }}>
          <div style={{ color: '#f59e0b', fontSize: '20px', fontWeight: 'bold' }}>
            ${calculations.pricePerShare.toFixed(2)}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>Price/Share</div>
        </div>

        {calculations.remainingAmount > 0 && (
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#ef4444', fontSize: '20px', fontWeight: 'bold' }}>
              ${calculations.remainingAmount.toFixed(2)}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Remaining</div>
          </div>
        )}
      </div>

      {/* Advanced Projections */}
      {showAdvanced && (
        <>
          <button
            onClick={() => setShowProjections(!showProjections)}
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              border: '1px solid #4b5563',
              borderRadius: '8px',
              color: '#9ca3af',
              fontSize: '14px',
              cursor: 'pointer',
              marginBottom: '16px'
            }}
          >
            {showProjections ? '▼' : '▶'} Advanced Projections
          </button>

          {showProjections && (
            <div style={{
              backgroundColor: 'rgba(55, 65, 81, 0.3)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid #4b5563'
            }}>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '16px',
                marginBottom: '16px'
              }}>
                <div>
                  <div style={{ color: '#8b5cf6', fontSize: '18px', fontWeight: 'bold' }}>
                    ${calculations.futureValue.toFixed(2)}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>Projected Future Value</div>
                </div>
                
                <div>
                  <div style={{ 
                    color: calculations.potentialGain >= 0 ? '#10b981' : '#ef4444', 
                    fontSize: '18px', 
                    fontWeight: 'bold' 
                  }}>
                    ${calculations.potentialGain.toFixed(2)} ({calculations.potentialGainPercent.toFixed(1)}%)
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>Potential Gain</div>
                </div>
                
                <div>
                  <div style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold' }}>
                    ${calculations.annualDividends.toFixed(2)}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>Annual Dividends (Est.)</div>
                </div>
                
                <div>
                  <div style={{ color: '#06b6d4', fontSize: '18px', fontWeight: 'bold' }}>
                    ${calculations.monthlyDividends.toFixed(2)}
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px' }}>Monthly Dividends (Est.)</div>
                </div>
              </div>

              {/* Phase Progression */}
              {futurePhases.length > 0 && (
                <div>
                  <h4 style={{ color: '#9ca3af', fontSize: '14px', marginBottom: '12px' }}>
                    Phase Progression Preview:
                  </h4>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    {futurePhases.slice(0, 3).map(phase => (
                      <div key={phase.id} style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        padding: '8px 12px',
                        backgroundColor: 'rgba(75, 85, 99, 0.3)',
                        borderRadius: '6px'
                      }}>
                        <span style={{ color: '#d1d5db', fontSize: '12px' }}>
                          {phase.phase_name}
                        </span>
                        <span style={{ color: '#10b981', fontSize: '12px', fontWeight: '600' }}>
                          ${phase.price_per_share.toFixed(2)}/share
                        </span>
                        <span style={{ color: '#f59e0b', fontSize: '12px' }}>
                          ${(calculations.shares * phase.price_per_share).toFixed(2)} value
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </>
      )}

      {/* Availability Warning */}
      {calculations.remainingShares < 1000 && (
        <div style={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '8px',
          padding: '12px',
          marginTop: '16px'
        }}>
          <div style={{ color: '#f87171', fontSize: '14px', fontWeight: '600' }}>
            ⚠️ Limited Availability
          </div>
          <div style={{ color: '#9ca3af', fontSize: '12px' }}>
            Only {calculations.remainingShares.toLocaleString()} shares remaining in {currentPhase?.phase_name}
          </div>
        </div>
      )}
    </div>
  );
};
