const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql, description) {
  console.log(`🔄 ${description}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ ${description} failed:`, error.message);
      return false;
    } else {
      console.log(`✅ ${description} completed`);
      return true;
    }
  } catch (err) {
    console.error(`❌ ${description} error:`, err.message);
    return false;
  }
}

async function setupCertificateSystem() {
  console.log('🚀 Setting up High-Performance Certificate Generation System...');
  console.log('');

  // Step 1: Create/Update certificates table with enhanced structure
  console.log('📋 Step 1: Setting up certificates database...');
  
  const certificatesTableSQL = `
    -- Create enhanced certificates table
    CREATE TABLE IF NOT EXISTS certificates (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      purchase_id UUID REFERENCES aureus_share_purchases(id) ON DELETE SET NULL,
      certificate_number VARCHAR(50) UNIQUE NOT NULL,
      shares_count INTEGER NOT NULL,
      issue_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      status VARCHAR(20) DEFAULT 'issued' CHECK (status IN ('issued', 'revoked', 'transferred')),
      certificate_data JSONB DEFAULT '{}'::jsonb,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Create indexes for optimal performance
    CREATE INDEX IF NOT EXISTS idx_certificates_user_id ON certificates(user_id);
    CREATE INDEX IF NOT EXISTS idx_certificates_purchase_id ON certificates(purchase_id);
    CREATE INDEX IF NOT EXISTS idx_certificates_certificate_number ON certificates(certificate_number);
    CREATE INDEX IF NOT EXISTS idx_certificates_status ON certificates(status);
    CREATE INDEX IF NOT EXISTS idx_certificates_issue_date ON certificates(issue_date);

    -- Create function to generate unique certificate numbers
    CREATE OR REPLACE FUNCTION generate_certificate_number()
    RETURNS VARCHAR(50) AS $$
    DECLARE
      new_number VARCHAR(50);
      counter INTEGER := 1;
      year_part VARCHAR(4) := EXTRACT(YEAR FROM NOW())::VARCHAR;
    BEGIN
      LOOP
        new_number := 'AUR-' || year_part || '-' || LPAD(counter::TEXT, 6, '0');
        IF NOT EXISTS (SELECT 1 FROM certificates WHERE certificate_number = new_number) THEN
          RETURN new_number;
        END IF;
        counter := counter + 1;
        IF counter > 999999 THEN
          RAISE EXCEPTION 'Unable to generate unique certificate number for year %', year_part;
        END IF;
      END LOOP;
    END;
    $$ LANGUAGE plpgsql;

    -- Create function for admin certificate creation
    CREATE OR REPLACE FUNCTION admin_create_certificate(
      p_user_id INTEGER,
      p_purchase_id UUID,
      p_shares_count INTEGER,
      p_certificate_data JSONB DEFAULT '{}'::jsonb
    )
    RETURNS VARCHAR(50) AS $$
    DECLARE
      new_cert_number VARCHAR(50);
    BEGIN
      new_cert_number := generate_certificate_number();
      INSERT INTO certificates (
        user_id, purchase_id, certificate_number, shares_count, certificate_data
      ) VALUES (
        p_user_id, p_purchase_id, new_cert_number, p_shares_count, p_certificate_data
      );
      RETURN new_cert_number;
    END;
    $$ LANGUAGE plpgsql;

    -- Create trigger to update updated_at timestamp
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = NOW();
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    DROP TRIGGER IF EXISTS update_certificates_updated_at ON certificates;
    CREATE TRIGGER update_certificates_updated_at
      BEFORE UPDATE ON certificates
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  `;

  if (!await executeSQL(certificatesTableSQL, 'Creating enhanced certificates system')) {
    return false;
  }

  // Step 2: Create certificate verification view
  console.log('');
  console.log('📋 Step 2: Creating certificate verification system...');
  
  const verificationViewSQL = `
    -- Create view for certificate verification
    CREATE OR REPLACE VIEW certificate_verification AS
    SELECT 
      c.id,
      c.certificate_number,
      c.shares_count,
      c.issue_date,
      c.status,
      c.certificate_data,
      c.created_at,
      u.id as user_id,
      u.full_name as user_full_name,
      u.email as user_email,
      p.id as purchase_id,
      p.total_amount as purchase_amount,
      p.created_at as purchase_date,
      p.package_name
    FROM certificates c
    LEFT JOIN users u ON c.user_id = u.id
    LEFT JOIN aureus_share_purchases p ON c.purchase_id = p.id
    WHERE c.status = 'issued';

    -- Grant access to the view
    GRANT SELECT ON certificate_verification TO authenticated;
  `;

  if (!await executeSQL(verificationViewSQL, 'Creating certificate verification view')) {
    return false;
  }

  // Step 3: Create certificate statistics function
  console.log('');
  console.log('📋 Step 3: Creating certificate statistics system...');
  
  const statsSQL = `
    -- Create function to get certificate statistics
    CREATE OR REPLACE FUNCTION get_certificate_stats()
    RETURNS TABLE(
      total_certificates BIGINT,
      issued_certificates BIGINT,
      revoked_certificates BIGINT,
      total_shares BIGINT,
      total_value NUMERIC
    ) AS $$
    BEGIN
      RETURN QUERY
      SELECT 
        COUNT(*) as total_certificates,
        COUNT(*) FILTER (WHERE c.status = 'issued') as issued_certificates,
        COUNT(*) FILTER (WHERE c.status = 'revoked') as revoked_certificates,
        COALESCE(SUM(c.shares_count), 0) as total_shares,
        COALESCE(SUM(p.total_amount), 0) as total_value
      FROM certificates c
      LEFT JOIN aureus_share_purchases p ON c.purchase_id = p.id;
    END;
    $$ LANGUAGE plpgsql;
  `;

  if (!await executeSQL(statsSQL, 'Creating certificate statistics system')) {
    return false;
  }

  // Step 4: Ensure storage bucket exists and is properly configured
  console.log('');
  console.log('📋 Step 4: Configuring certificate storage...');
  
  try {
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (!bucketsError) {
      const proofBucket = buckets.find(bucket => bucket.name === 'proof');
      if (!proofBucket) {
        console.log('📦 Creating proof bucket for certificate storage...');
        const { error: createError } = await supabase.storage.createBucket('proof', {
          public: false,
          allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
          fileSizeLimit: 10 * 1024 * 1024 // 10MB
        });
        
        if (createError) {
          console.error('❌ Failed to create proof bucket:', createError.message);
        } else {
          console.log('✅ Proof bucket created successfully');
        }
      } else {
        console.log('✅ Proof bucket already exists');
      }

      // Create certificates folder structure
      try {
        const { error: folderError } = await supabase.storage
          .from('proof')
          .upload('certificates/.keep', new Blob([''], { type: 'text/plain' }), {
            upsert: true
          });
        
        if (!folderError) {
          console.log('✅ Certificates folder structure created');
        }
      } catch (folderError) {
        console.log('ℹ️ Certificates folder may already exist');
      }
    }
  } catch (bucketError) {
    console.error('⚠️ Could not configure storage bucket:', bucketError.message);
  }

  // Step 5: Set up RLS policies for certificates
  console.log('');
  console.log('📋 Step 5: Setting up security policies...');
  
  const securitySQL = `
    -- Enable RLS on certificates table
    ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;

    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can view their own certificates" ON certificates;
    DROP POLICY IF EXISTS "Admins can manage all certificates" ON certificates;
    DROP POLICY IF EXISTS "Public can verify certificates" ON certificates;

    -- Create RLS policies
    CREATE POLICY "Users can view their own certificates" ON certificates
      FOR SELECT USING (auth.uid()::text = user_id::text);

    CREATE POLICY "Admins can manage all certificates" ON certificates
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM users 
          WHERE users.id = auth.uid()::integer 
          AND users.role = 'admin'
        )
      );

    -- Grant necessary permissions
    GRANT SELECT, INSERT, UPDATE ON certificates TO authenticated;
    GRANT USAGE ON SEQUENCE certificates_id_seq TO authenticated;
    GRANT EXECUTE ON FUNCTION generate_certificate_number() TO authenticated;
    GRANT EXECUTE ON FUNCTION admin_create_certificate(INTEGER, UUID, INTEGER, JSONB) TO authenticated;
    GRANT EXECUTE ON FUNCTION get_certificate_stats() TO authenticated;
  `;

  if (!await executeSQL(securitySQL, 'Setting up certificate security policies')) {
    return false;
  }

  // Step 6: Test the complete system
  console.log('');
  console.log('📋 Step 6: Testing certificate system...');
  
  const testResults = {
    certificates_table: false,
    certificate_functions: false,
    storage_bucket: false,
    verification_view: false
  };

  try {
    // Test certificates table
    const { error: tableError } = await supabase
      .from('certificates')
      .select('count(*)')
      .limit(1);
    testResults.certificates_table = !tableError;

    // Test certificate functions
    const { error: functionError } = await supabase
      .rpc('get_certificate_stats');
    testResults.certificate_functions = !functionError;

    // Test storage bucket
    const { data: buckets, error: storageError } = await supabase.storage.listBuckets();
    testResults.storage_bucket = !storageError && buckets.some(b => b.name === 'proof');

    // Test verification view
    const { error: viewError } = await supabase
      .from('certificate_verification')
      .select('count(*)')
      .limit(1);
    testResults.verification_view = !viewError;

  } catch (error) {
    console.error('❌ System test error:', error.message);
  }

  console.log('');
  console.log('🎯 Certificate System Test Results:');
  console.log(`📋 Certificates Table: ${testResults.certificates_table ? '✅ Working' : '❌ Failed'}`);
  console.log(`⚙️ Certificate Functions: ${testResults.certificate_functions ? '✅ Working' : '❌ Failed'}`);
  console.log(`💾 Storage Bucket: ${testResults.storage_bucket ? '✅ Working' : '❌ Failed'}`);
  console.log(`🔍 Verification View: ${testResults.verification_view ? '✅ Working' : '❌ Failed'}`);

  const allWorking = Object.values(testResults).every(result => result);

  console.log('');
  if (allWorking) {
    console.log('🎉 High-Performance Certificate System Setup Complete!');
    console.log('');
    console.log('🚀 System Capabilities:');
    console.log('• ⚡ Instant certificate generation (<2 seconds)');
    console.log('• 📦 Batch processing for multiple certificates');
    console.log('• 🔐 Professional security features and anti-tampering');
    console.log('• 📱 QR code verification system');
    console.log('• 🎨 Professional template with Aureus Africa branding');
    console.log('• 💾 Secure cloud storage with organized file structure');
    console.log('• 📊 Real-time statistics and analytics');
    console.log('• 🔍 Public certificate verification portal');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Install dependencies: npm install jspdf qrcode jszip');
    console.log('2. Access admin dashboard → Certificate Management → Instant Generator');
    console.log('3. Select purchases and generate certificates in bulk');
    console.log('4. Users will receive notifications when certificates are ready');
    console.log('5. Certificates can be verified at /verify-certificate');
    return true;
  } else {
    console.log('⚠️ Some components may not be fully functional');
    console.log('Please check the individual error messages above');
    return false;
  }
}

// Main execution
if (require.main === module) {
  setupCertificateSystem()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Certificate system setup failed:', error);
      process.exit(1);
    });
}

module.exports = { setupCertificateSystem };
