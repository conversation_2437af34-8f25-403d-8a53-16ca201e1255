import React, { useState } from 'react';

const ImageTest: React.FC = () => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [failedImages, setFailedImages] = useState<Set<string>>(new Set());

  const testImages = [
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-1.jpg',
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-2.jpg',
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-3.jpg',
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-4.jpg',
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-5.jpg',
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-6.jpg',
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-7.jpg',
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-8.jpg',
    'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-9.jpg'
  ];

  const handleImageLoad = (url: string) => {
    setLoadedImages(prev => new Set([...prev, url]));
    console.log(`✅ Image loaded: ${url}`);
  };

  const handleImageError = (url: string) => {
    setFailedImages(prev => new Set([...prev, url]));
    console.error(`❌ Image failed to load: ${url}`);
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Gallery Image Test</h2>
      <div className="grid grid-cols-3 gap-4">
        {testImages.map((url, index) => (
          <div key={url} className="border rounded p-2">
            <p className="text-sm mb-2">Image {index + 1}</p>
            <img
              src={url}
              alt={`Test image ${index + 1}`}
              className="w-full h-32 object-cover rounded"
              onLoad={() => handleImageLoad(url)}
              onError={() => handleImageError(url)}
            />
            <div className="mt-2 text-xs">
              {loadedImages.has(url) && <span className="text-green-600">✅ Loaded</span>}
              {failedImages.has(url) && <span className="text-red-600">❌ Failed</span>}
              {!loadedImages.has(url) && !failedImages.has(url) && <span className="text-gray-500">⏳ Loading...</span>}
            </div>
          </div>
        ))}
      </div>
      <div className="mt-4">
        <p>Loaded: {loadedImages.size}/{testImages.length}</p>
        <p>Failed: {failedImages.size}/{testImages.length}</p>
      </div>
    </div>
  );
};

export default ImageTest;
