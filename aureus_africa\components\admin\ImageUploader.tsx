import React, { useState, useCallback } from 'react';
import { GalleryService } from '../../lib/galleryService';
import type { 
  ImageUploaderProps, 
  GalleryUploadProgress, 
  GalleryCategory,
  CreateGalleryImageRequest 
} from '../../types/gallery';

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  onUploadComplete,
  onUploadProgress,
  allowMultiple = true,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  defaultCategory
}) => {
  const [uploads, setUploads] = useState<GalleryUploadProgress[]>([]);
  const [categories, setCategories] = useState<GalleryCategory[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load categories on mount
  React.useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const data = await GalleryService.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    if (!allowMultiple && fileArray.length > 1) {
      alert('Only one file is allowed');
      return;
    }

    // Validate files
    const validFiles: File[] = [];
    for (const file of fileArray) {
      const validation = GalleryService.validateImageFile(file);
      if (!validation.valid) {
        alert(`${file.name}: ${validation.error}`);
        continue;
      }
      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    // Initialize upload progress
    const newUploads: GalleryUploadProgress[] = validFiles.map(file => ({
      file,
      progress: 0,
      status: 'pending'
    }));

    setUploads(prev => [...prev, ...newUploads]);
    onUploadProgress?.(newUploads);

    // Start uploads
    uploadFiles(validFiles);
  }, [allowMultiple, onUploadProgress]);

  const uploadFiles = async (files: File[]) => {
    setIsLoading(true);
    const completedUploads: any[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        // Update status to uploading
        setUploads(prev => prev.map(upload => 
          upload.file === file 
            ? { ...upload, status: 'uploading', progress: 0 }
            : upload
        ));

        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setUploads(prev => prev.map(upload => 
            upload.file === file && upload.progress < 90
              ? { ...upload, progress: upload.progress + 10 }
              : upload
          ));
        }, 200);

        // Upload file
        const uploadResult = await GalleryService.uploadImage(file, 'gallery');

        clearInterval(progressInterval);

        // Update to processing
        setUploads(prev => prev.map(upload => 
          upload.file === file 
            ? { ...upload, status: 'processing', progress: 95 }
            : upload
        ));

        // Create database record
        const imageData: CreateGalleryImageRequest = {
          title: file.name.split('.')[0].replace(/[-_]/g, ' '),
          image_url: uploadResult.url,
          category_id: defaultCategory && defaultCategory.trim() !== '' ? defaultCategory : null,
          alt_text: `Gallery image: ${file.name}`,
          file_size: uploadResult.file_size,
          width: uploadResult.width,
          height: uploadResult.height,
          display_order: 0,
          is_featured: false,
          is_active: true
        };

        const galleryImage = await GalleryService.createImage(imageData);

        // Update to complete
        setUploads(prev => prev.map(upload => 
          upload.file === file 
            ? { ...upload, status: 'complete', progress: 100, result: galleryImage }
            : upload
        ));

        completedUploads.push(galleryImage);

      } catch (error) {
        console.error('Upload failed:', error);
        setUploads(prev => prev.map(upload => 
          upload.file === file 
            ? { 
                ...upload, 
                status: 'error', 
                error: error instanceof Error ? error.message : 'Upload failed' 
              }
            : upload
        ));
      }
    }

    setIsLoading(false);
    
    if (completedUploads.length > 0) {
      onUploadComplete(completedUploads);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const clearUploads = () => {
    setUploads([]);
  };

  const retryUpload = (file: File) => {
    setUploads(prev => prev.filter(upload => upload.file !== file));
    uploadFiles([file]);
  };

  return (
    <div className="space-y-6">
      {/* Storage Setup Notice */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
              Storage Setup Required
            </h3>
            <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
              <p>To upload images, you need to create an 'assets' bucket in Supabase Storage with public upload policies.</p>
              <p className="mt-1">If you get "signature verification failed" errors, check your Supabase Storage configuration.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Upload Guidelines */}
      <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
              Upload Guidelines
            </h3>
            <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
              <ul className="list-disc list-inside space-y-1">
                <li>Maximum file size: {Math.round(maxFileSize / 1024 / 1024)}MB per image</li>
                <li>Supported formats: {acceptedTypes.join(', ').replace(/image\//g, '').toUpperCase()}</li>
                <li>Recommended resolution: 1920×1080 or higher</li>
                <li>Images will be automatically optimized for web display</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Upload Area */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${isDragOver 
            ? 'border-amber-400 bg-amber-50 dark:bg-amber-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-amber-400'
          }
          ${isLoading ? 'opacity-50 pointer-events-none' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="space-y-4">
          <div className="text-4xl text-gray-400">📸</div>
          <div>
            <p className="text-lg font-medium text-gray-900 dark:text-white">
              Drop images here or click to browse
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {allowMultiple ? 'Multiple files allowed' : 'Single file only'} • 
              Max {Math.round(maxFileSize / 1024 / 1024)}MB • 
              {acceptedTypes.join(', ').replace(/image\//g, '').toUpperCase()}
            </p>
          </div>
          <input
            type="file"
            multiple={allowMultiple}
            accept={acceptedTypes.join(',')}
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
            id="file-upload"
          />
          <label
            htmlFor="file-upload"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 cursor-pointer"
          >
            Choose Files
          </label>
        </div>
      </div>

      {/* Upload Progress */}
      {uploads.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Upload Progress ({uploads.filter(u => u.status === 'complete').length}/{uploads.length})
            </h3>
            <button
              onClick={clearUploads}
              className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Clear
            </button>
          </div>

          <div className="space-y-3">
            {uploads.map((upload, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {upload.file.name}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    upload.status === 'complete' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                    upload.status === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                    upload.status === 'uploading' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                    'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                  }`}>
                    {upload.status}
                  </span>
                </div>

                {upload.status !== 'complete' && upload.status !== 'error' && (
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-amber-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${upload.progress}%` }}
                    />
                  </div>
                )}

                {upload.status === 'error' && (
                  <div className="mt-2 flex items-center justify-between">
                    <span className="text-sm text-red-600 dark:text-red-400">
                      {upload.error}
                    </span>
                    <button
                      onClick={() => retryUpload(upload.file)}
                      className="text-sm text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300"
                    >
                      Retry
                    </button>
                  </div>
                )}

                {upload.status === 'complete' && upload.result && (
                  <div className="mt-2 text-sm text-green-600 dark:text-green-400">
                    ✓ Uploaded successfully
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
