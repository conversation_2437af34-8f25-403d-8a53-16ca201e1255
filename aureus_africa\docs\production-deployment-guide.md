# Production Deployment Guide - Phase 7.2

## Overview
Comprehensive production deployment guide for the Aureus Alliance Web Dashboard, including deployment procedures, smoke testing, monitoring setup, and launch verification.

## Pre-Deployment Checklist

### Infrastructure Verification
```bash
#!/bin/bash
# pre-deployment-check.sh - Pre-deployment verification script

echo "=========================================="
echo "Aureus Alliance Production Deployment"
echo "Pre-Deployment Verification"
echo "=========================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    local status=$1
    local message=$2
    
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}✓${NC} $message"
    elif [ "$status" = "WARNING" ]; then
        echo -e "${YELLOW}⚠${NC} $message"
    else
        echo -e "${RED}✗${NC} $message"
    fi
}

# Check system requirements
check_system_requirements() {
    echo -e "\n${BLUE}Checking System Requirements...${NC}"
    
    # Check disk space
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 80 ]; then
        print_status "OK" "Disk space: ${DISK_USAGE}% used"
    else
        print_status "ERROR" "Disk space critical: ${DISK_USAGE}% used"
    fi
    
    # Check memory
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$MEMORY_USAGE" -lt 80 ]; then
        print_status "OK" "Memory usage: ${MEMORY_USAGE}%"
    else
        print_status "WARNING" "Memory usage high: ${MEMORY_USAGE}%"
    fi
    
    # Check CPU load
    CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    CPU_CORES=$(nproc)
    CPU_THRESHOLD=$(echo "$CPU_CORES * 0.8" | bc)
    
    if (( $(echo "$CPU_LOAD < $CPU_THRESHOLD" | bc -l) )); then
        print_status "OK" "CPU load: $CPU_LOAD (cores: $CPU_CORES)"
    else
        print_status "WARNING" "CPU load high: $CPU_LOAD (cores: $CPU_CORES)"
    fi
}

# Check required services
check_services() {
    echo -e "\n${BLUE}Checking Required Services...${NC}"
    
    services=("nginx" "postgresql" "redis-server" "docker")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            print_status "OK" "$service is running"
        else
            print_status "ERROR" "$service is not running"
        fi
    done
}

# Check network connectivity
check_network() {
    echo -e "\n${BLUE}Checking Network Connectivity...${NC}"
    
    # Check internet connectivity
    if ping -c 3 8.8.8.8 > /dev/null 2>&1; then
        print_status "OK" "Internet connectivity"
    else
        print_status "ERROR" "No internet connectivity"
    fi
    
    # Check DNS resolution
    if nslookup google.com > /dev/null 2>&1; then
        print_status "OK" "DNS resolution"
    else
        print_status "ERROR" "DNS resolution failed"
    fi
    
    # Check external services
    external_services=("github.com" "registry.npmjs.org" "hub.docker.com")
    
    for service in "${external_services[@]}"; do
        if curl -s --head "$service" > /dev/null; then
            print_status "OK" "Access to $service"
        else
            print_status "WARNING" "Cannot access $service"
        fi
    done
}

# Check SSL certificates
check_ssl_certificates() {
    echo -e "\n${BLUE}Checking SSL Certificates...${NC}"
    
    domains=("dashboard.aureusalliance.com" "api.aureusalliance.com")
    
    for domain in "${domains[@]}"; do
        if [ -f "/etc/letsencrypt/live/aureusalliance/fullchain.pem" ]; then
            EXPIRY_DATE=$(openssl x509 -enddate -noout -in "/etc/letsencrypt/live/aureusalliance/fullchain.pem" | cut -d= -f2)
            EXPIRY_TIMESTAMP=$(date -d "$EXPIRY_DATE" +%s)
            CURRENT_TIMESTAMP=$(date +%s)
            DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
            
            if [ "$DAYS_UNTIL_EXPIRY" -gt 30 ]; then
                print_status "OK" "SSL certificate for $domain expires in $DAYS_UNTIL_EXPIRY days"
            elif [ "$DAYS_UNTIL_EXPIRY" -gt 7 ]; then
                print_status "WARNING" "SSL certificate for $domain expires in $DAYS_UNTIL_EXPIRY days"
            else
                print_status "ERROR" "SSL certificate for $domain expires in $DAYS_UNTIL_EXPIRY days"
            fi
        else
            print_status "ERROR" "SSL certificate not found for $domain"
        fi
    done
}

# Check database connectivity
check_database() {
    echo -e "\n${BLUE}Checking Database Connectivity...${NC}"
    
    # PostgreSQL connection test
    if PGPASSWORD="$POSTGRES_PASSWORD" psql -h localhost -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "SELECT 1;" > /dev/null 2>&1; then
        print_status "OK" "PostgreSQL connection"
        
        # Check database size
        DB_SIZE=$(PGPASSWORD="$POSTGRES_PASSWORD" psql -h localhost -U "$POSTGRES_USER" -d "$POSTGRES_DB" -t -c "SELECT pg_size_pretty(pg_database_size('$POSTGRES_DB'));" | xargs)
        print_status "OK" "Database size: $DB_SIZE"
    else
        print_status "ERROR" "PostgreSQL connection failed"
    fi
    
    # Redis connection test
    if redis-cli -a "$REDIS_PASSWORD" ping > /dev/null 2>&1; then
        print_status "OK" "Redis connection"
        
        # Check Redis memory usage
        REDIS_MEMORY=$(redis-cli -a "$REDIS_PASSWORD" INFO memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
        print_status "OK" "Redis memory usage: $REDIS_MEMORY"
    else
        print_status "ERROR" "Redis connection failed"
    fi
}

# Check application files
check_application_files() {
    echo -e "\n${BLUE}Checking Application Files...${NC}"
    
    # Check if application directory exists
    if [ -d "/var/www/aureus-alliance" ]; then
        print_status "OK" "Application directory exists"
        
        # Check if dist folder exists
        if [ -d "/var/www/aureus-alliance/dist" ]; then
            print_status "OK" "Build files exist"
            
            # Check critical files
            critical_files=("index.html" "manifest.json")
            for file in "${critical_files[@]}"; do
                if [ -f "/var/www/aureus-alliance/dist/$file" ]; then
                    print_status "OK" "$file exists"
                else
                    print_status "ERROR" "$file missing"
                fi
            done
        else
            print_status "ERROR" "Build files missing - run deployment first"
        fi
    else
        print_status "ERROR" "Application directory missing"
    fi
}

# Check environment variables
check_environment() {
    echo -e "\n${BLUE}Checking Environment Configuration...${NC}"
    
    required_vars=(
        "NODE_ENV"
        "POSTGRES_HOST"
        "POSTGRES_DB"
        "POSTGRES_USER"
        "POSTGRES_PASSWORD"
        "REDIS_HOST"
        "REDIS_PASSWORD"
        "JWT_SECRET"
        "ENCRYPTION_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -n "${!var}" ]; then
            print_status "OK" "$var is set"
        else
            print_status "ERROR" "$var is not set"
        fi
    done
}

# Main execution
echo -e "${BLUE}Starting pre-deployment checks...${NC}\n"

check_system_requirements
check_services
check_network
check_ssl_certificates
check_database
check_application_files
check_environment

echo -e "\n${BLUE}Pre-deployment check completed!${NC}"
echo -e "Review any ${RED}ERROR${NC} or ${YELLOW}WARNING${NC} items before proceeding with deployment."
```

## Production Deployment Process

### Automated Deployment Script
```bash
#!/bin/bash
# deploy-production.sh - Production deployment automation

set -e # Exit on any error

# Configuration
APP_NAME="aureus-alliance"
APP_DIR="/var/www/$APP_NAME"
BACKUP_DIR="/var/backups/$APP_NAME"
DEPLOY_USER="deploy"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ROLLBACK_POINT="$BACKUP_DIR/rollback_$TIMESTAMP"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
LOG_FILE="/var/log/$APP_NAME/deployment_$TIMESTAMP.log"
mkdir -p "$(dirname "$LOG_FILE")"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

print_step() {
    echo -e "\n${BLUE}==== $1 ====${NC}"
    log "STEP: $1"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
    log "SUCCESS: $1"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
    log "ERROR: $1"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    log "WARNING: $1"
}

# Error handling
handle_error() {
    print_error "Deployment failed at step: $1"
    log "Deployment failed. Initiating rollback..."
    
    # Trigger rollback if previous deployment exists
    if [ -d "$ROLLBACK_POINT" ]; then
        rollback_deployment
    fi
    
    exit 1
}

# Create rollback point
create_rollback_point() {
    print_step "Creating Rollback Point"
    
    mkdir -p "$BACKUP_DIR"
    
    if [ -d "$APP_DIR" ]; then
        cp -r "$APP_DIR" "$ROLLBACK_POINT"
        print_success "Rollback point created at $ROLLBACK_POINT"
    else
        print_warning "No existing deployment to backup"
    fi
}

# Download and prepare application
prepare_application() {
    print_step "Preparing Application"
    
    # Create temporary directory
    TEMP_DIR="/tmp/$APP_NAME-deploy-$TIMESTAMP"
    mkdir -p "$TEMP_DIR"
    
    # Clone repository
    log "Cloning repository..."
    git clone https://github.com/JPRademeyer84/aureus_africa.git "$TEMP_DIR"
    cd "$TEMP_DIR"
    
    # Checkout specific branch/tag if specified
    if [ -n "$DEPLOY_BRANCH" ]; then
        git checkout "$DEPLOY_BRANCH"
        print_success "Checked out branch: $DEPLOY_BRANCH"
    fi
    
    # Install dependencies
    log "Installing dependencies..."
    npm ci --production
    
    # Build application
    log "Building application..."
    npm run build
    
    print_success "Application prepared successfully"
}

# Deploy application
deploy_application() {
    print_step "Deploying Application"
    
    # Stop services
    log "Stopping services..."
    systemctl stop nginx || handle_error "Failed to stop nginx"
    
    # Create application directory if it doesn't exist
    mkdir -p "$APP_DIR"
    
    # Copy new files
    log "Copying application files..."
    cp -r "$TEMP_DIR/dist"/* "$APP_DIR/"
    
    # Copy server files if they exist
    if [ -d "$TEMP_DIR/server" ]; then
        cp -r "$TEMP_DIR/server" "$APP_DIR/"
    fi
    
    # Set permissions
    chown -R www-data:www-data "$APP_DIR"
    chmod -R 755 "$APP_DIR"
    
    print_success "Application files deployed"
}

# Update database schema
update_database() {
    print_step "Updating Database Schema"
    
    # Run database migrations if they exist
    if [ -f "$TEMP_DIR/database/migrations.sql" ]; then
        log "Running database migrations..."
        PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f "$TEMP_DIR/database/migrations.sql"
        print_success "Database migrations completed"
    else
        print_warning "No database migrations found"
    fi
    
    # Update database statistics
    log "Updating database statistics..."
    PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "ANALYZE;"
    
    print_success "Database update completed"
}

# Update configuration
update_configuration() {
    print_step "Updating Configuration"
    
    # Update nginx configuration if needed
    if [ -f "$TEMP_DIR/deployment/nginx.conf" ]; then
        log "Updating nginx configuration..."
        cp "$TEMP_DIR/deployment/nginx.conf" "/etc/nginx/sites-available/$APP_NAME"
        nginx -t || handle_error "Invalid nginx configuration"
        print_success "Nginx configuration updated"
    fi
    
    # Update systemd services if needed
    if [ -f "$TEMP_DIR/deployment/$APP_NAME.service" ]; then
        log "Updating systemd service..."
        cp "$TEMP_DIR/deployment/$APP_NAME.service" "/etc/systemd/system/"
        systemctl daemon-reload
        print_success "Systemd service updated"
    fi
}

# Start services
start_services() {
    print_step "Starting Services"
    
    # Start application service if exists
    if systemctl list-unit-files | grep -q "$APP_NAME.service"; then
        systemctl start "$APP_NAME"
        systemctl enable "$APP_NAME"
        print_success "Application service started"
    fi
    
    # Start nginx
    systemctl start nginx
    systemctl enable nginx
    print_success "Nginx started"
    
    # Reload nginx to ensure latest configuration
    systemctl reload nginx
    
    # Start other services
    systemctl start redis-server
    systemctl start postgresql
    
    print_success "All services started"
}

# Health check
perform_health_check() {
    print_step "Performing Health Check"
    
    # Wait for services to start
    sleep 10
    
    # Check if nginx is responding
    if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200\|301\|302"; then
        print_success "Nginx is responding"
    else
        handle_error "Nginx health check failed"
    fi
    
    # Check if application is responding
    if curl -s -o /dev/null -w "%{http_code}" https://dashboard.aureusalliance.com/health | grep -q "200"; then
        print_success "Application health check passed"
    else
        print_warning "Application health check failed - may need warm-up time"
    fi
    
    # Check database connectivity
    if PGPASSWORD="$POSTGRES_PASSWORD" psql -h "$POSTGRES_HOST" -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "SELECT 1;" > /dev/null 2>&1; then
        print_success "Database connectivity verified"
    else
        handle_error "Database connectivity check failed"
    fi
    
    # Check Redis connectivity
    if redis-cli -a "$REDIS_PASSWORD" ping > /dev/null 2>&1; then
        print_success "Redis connectivity verified"
    else
        handle_error "Redis connectivity check failed"
    fi
}

# Rollback function
rollback_deployment() {
    print_step "Rolling Back Deployment"
    
    if [ -d "$ROLLBACK_POINT" ]; then
        # Stop services
        systemctl stop nginx
        
        # Restore previous version
        rm -rf "$APP_DIR"
        cp -r "$ROLLBACK_POINT" "$APP_DIR"
        
        # Restore permissions
        chown -R www-data:www-data "$APP_DIR"
        chmod -R 755 "$APP_DIR"
        
        # Start services
        systemctl start nginx
        
        print_success "Rollback completed successfully"
        log "Rollback completed. Previous version restored."
    else
        print_error "No rollback point available"
    fi
}

# Cleanup
cleanup() {
    print_step "Cleaning Up"
    
    # Remove temporary files
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
        print_success "Temporary files cleaned up"
    fi
    
    # Keep only last 5 rollback points
    if [ -d "$BACKUP_DIR" ]; then
        find "$BACKUP_DIR" -name "rollback_*" -type d | sort -r | tail -n +6 | xargs rm -rf
        print_success "Old rollback points cleaned up"
    fi
}

# Send deployment notification
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST "$SLACK_WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{
                \"text\": \"🚀 Aureus Alliance Deployment\",
                \"attachments\": [{
                    \"color\": \"$([ "$status" = "success" ] && echo "good" || echo "danger")\",
                    \"fields\": [{
                        \"title\": \"Status\",
                        \"value\": \"$status\",
                        \"short\": true
                    }, {
                        \"title\": \"Timestamp\",
                        \"value\": \"$(date)\",
                        \"short\": true
                    }, {
                        \"title\": \"Message\",
                        \"value\": \"$message\",
                        \"short\": false
                    }]
                }]
            }"
    fi
}

# Main deployment process
main() {
    log "Starting production deployment for $APP_NAME"
    echo -e "${BLUE}Starting Production Deployment${NC}"
    echo -e "Application: $APP_NAME"
    echo -e "Timestamp: $TIMESTAMP"
    echo -e "Log file: $LOG_FILE\n"
    
    # Check if running as root
    if [ "$EUID" -ne 0 ]; then
        print_error "This script must be run as root"
        exit 1
    fi
    
    # Load environment variables
    if [ -f "/etc/environment" ]; then
        set -a
        source /etc/environment
        set +a
    fi
    
    # Main deployment steps
    create_rollback_point
    prepare_application
    deploy_application
    update_database
    update_configuration
    start_services
    perform_health_check
    cleanup
    
    print_success "Deployment completed successfully!"
    log "Deployment completed successfully"
    
    send_notification "success" "Production deployment completed successfully at $(date)"
    
    echo -e "\n${GREEN}🎉 Deployment Successful!${NC}"
    echo -e "Application is now live at: https://dashboard.aureusalliance.com"
    echo -e "Admin panel: https://admin.aureusalliance.com"
    echo -e "API endpoint: https://api.aureusalliance.com"
}

# Trap errors
trap 'handle_error "Unknown error occurred"' ERR

# Check command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        if [ -n "$2" ]; then
            ROLLBACK_POINT="$BACKUP_DIR/rollback_$2"
        else
            ROLLBACK_POINT=$(find "$BACKUP_DIR" -name "rollback_*" -type d | sort -r | head -n 1)
        fi
        rollback_deployment
        ;;
    "status")
        perform_health_check
        ;;
    *)
        echo "Usage: $0 {deploy|rollback [timestamp]|status}"
        exit 1
        ;;
esac
```

## Smoke Testing Suite

### Automated Smoke Tests
```javascript
// tests/smoke/production-smoke-tests.js
const puppeteer = require('puppeteer')
const axios = require('axios')
const fs = require('fs').promises
const path = require('path')

class ProductionSmokeTests {
  constructor() {
    this.baseUrl = process.env.PRODUCTION_URL || 'https://dashboard.aureusalliance.com'
    this.apiUrl = process.env.API_URL || 'https://api.aureusalliance.com'
    this.browser = null
    this.page = null
    this.results = []
    this.startTime = Date.now()
  }

  async initialize() {
    console.log('🚀 Starting Production Smoke Tests')
    console.log(`Base URL: ${this.baseUrl}`)
    console.log(`API URL: ${this.apiUrl}`)
    
    this.browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    this.page = await this.browser.newPage()
    
    // Set viewport and user agent
    await this.page.setViewport({ width: 1920, height: 1080 })
    await this.page.setUserAgent('Mozilla/5.0 (compatible; AureusAllianceBot/1.0; +https://aureusalliance.com)')
    
    // Enable request interception for monitoring
    await this.page.setRequestInterception(true)
    this.page.on('request', request => {
      request.continue()
    })
    
    // Monitor console errors
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.error(`Console Error: ${msg.text()}`)
      }
    })
  }

  async runTest(name, testFunction) {
    console.log(`\n🧪 Running test: ${name}`)
    const startTime = Date.now()
    
    try {
      await testFunction()
      const duration = Date.now() - startTime
      this.results.push({
        name,
        status: 'PASS',
        duration,
        timestamp: new Date().toISOString()
      })
      console.log(`✅ ${name} - PASSED (${duration}ms)`)
    } catch (error) {
      const duration = Date.now() - startTime
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: error.message,
        timestamp: new Date().toISOString()
      })
      console.error(`❌ ${name} - FAILED (${duration}ms): ${error.message}`)
    }
  }

  // Test 1: Homepage Load Test
  async testHomepageLoad() {
    const response = await this.page.goto(this.baseUrl, { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    })
    
    if (!response.ok()) {
      throw new Error(`Homepage returned status ${response.status()}`)
    }
    
    // Check for critical elements
    await this.page.waitForSelector('header', { timeout: 5000 })
    await this.page.waitForSelector('main', { timeout: 5000 })
    
    // Check page title
    const title = await this.page.title()
    if (!title.includes('Aureus Alliance')) {
      throw new Error(`Invalid page title: ${title}`)
    }
    
    // Check for JavaScript errors
    const errors = await this.page.evaluate(() => {
      return window.jsErrors || []
    })
    
    if (errors.length > 0) {
      throw new Error(`JavaScript errors found: ${errors.join(', ')}`)
    }
  }

  // Test 2: Authentication Flow Test
  async testAuthenticationFlow() {
    // Navigate to login page
    await this.page.goto(`${this.baseUrl}/login`, { waitUntil: 'networkidle0' })
    
    // Check if login form exists
    await this.page.waitForSelector('form[data-testid="login-form"]', { timeout: 5000 })
    
    // Check form elements
    const emailInput = await this.page.$('input[type="email"]')
    const passwordInput = await this.page.$('input[type="password"]')
    const submitButton = await this.page.$('button[type="submit"]')
    
    if (!emailInput || !passwordInput || !submitButton) {
      throw new Error('Login form elements not found')
    }
    
    // Test form validation (empty submission)
    await this.page.click('button[type="submit"]')
    
    // Check for validation messages
    await this.page.waitForSelector('.error-message, .form-error', { timeout: 2000 })
  }

  // Test 3: API Health Check
  async testApiHealth() {
    const healthEndpoint = `${this.apiUrl}/health`
    
    try {
      const response = await axios.get(healthEndpoint, { timeout: 10000 })
      
      if (response.status !== 200) {
        throw new Error(`API health check returned status ${response.status}`)
      }
      
      const data = response.data
      if (data.status !== 'healthy') {
        throw new Error(`API health status is ${data.status}`)
      }
      
      // Check database connectivity
      if (data.database !== 'connected') {
        throw new Error('Database connection failed')
      }
      
      // Check Redis connectivity
      if (data.redis !== 'connected') {
        throw new Error('Redis connection failed')
      }
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('API health check timeout')
      }
      throw error
    }
  }

  // Test 4: API Authentication Test
  async testApiAuthentication() {
    const authEndpoint = `${this.apiUrl}/auth/login`
    
    // Test with invalid credentials
    try {
      await axios.post(authEndpoint, {
        email: '<EMAIL>',
        password: 'invalidpassword'
      }, { timeout: 10000 })
      
      throw new Error('API should reject invalid credentials')
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior
        return
      }
      throw error
    }
  }

  // Test 5: Database Connectivity Test
  async testDatabaseConnectivity() {
    const dbTestEndpoint = `${this.apiUrl}/system/database-test`
    
    try {
      const response = await axios.get(dbTestEndpoint, { 
        timeout: 15000,
        headers: {
          'X-API-Key': process.env.SYSTEM_API_KEY
        }
      })
      
      if (response.status !== 200) {
        throw new Error(`Database test returned status ${response.status}`)
      }
      
      const data = response.data
      if (!data.connected) {
        throw new Error('Database connection test failed')
      }
      
      // Check query performance
      if (data.queryTime > 1000) {
        console.warn(`⚠️ Database query time is high: ${data.queryTime}ms`)
      }
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('Database connectivity test timeout')
      }
      throw error
    }
  }

  // Test 6: SSL Certificate Test
  async testSSLCertificate() {
    const https = require('https')
    const url = require('url')
    
    return new Promise((resolve, reject) => {
      const parsedUrl = url.parse(this.baseUrl)
      
      const options = {
        hostname: parsedUrl.hostname,
        port: 443,
        path: '/',
        method: 'GET',
        timeout: 10000
      }
      
      const req = https.request(options, (res) => {
        const cert = res.connection.getPeerCertificate()
        
        if (!cert || Object.keys(cert).length === 0) {
          reject(new Error('No SSL certificate found'))
          return
        }
        
        // Check certificate validity
        const now = new Date()
        const validFrom = new Date(cert.valid_from)
        const validTo = new Date(cert.valid_to)
        
        if (now < validFrom) {
          reject(new Error('SSL certificate is not yet valid'))
          return
        }
        
        if (now > validTo) {
          reject(new Error('SSL certificate has expired'))
          return
        }
        
        // Check if certificate expires soon (within 30 days)
        const daysUntilExpiry = Math.floor((validTo - now) / (1000 * 60 * 60 * 24))
        if (daysUntilExpiry < 30) {
          console.warn(`⚠️ SSL certificate expires in ${daysUntilExpiry} days`)
        }
        
        resolve()
      })
      
      req.on('error', (error) => {
        reject(new Error(`SSL certificate test failed: ${error.message}`))
      })
      
      req.on('timeout', () => {
        req.destroy()
        reject(new Error('SSL certificate test timeout'))
      })
      
      req.end()
    })
  }

  // Test 7: Performance Test
  async testPerformance() {
    // Test page load performance
    const performanceMetrics = await this.page.metrics()
    
    // Get performance timing
    const performanceTiming = await this.page.evaluate(() => {
      const timing = performance.timing
      return {
        loadComplete: timing.loadEventEnd - timing.navigationStart,
        domReady: timing.domContentLoadedEventEnd - timing.navigationStart,
        firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 0
      }
    })
    
    // Check performance thresholds
    if (performanceTiming.loadComplete > 5000) {
      throw new Error(`Page load time too high: ${performanceTiming.loadComplete}ms`)
    }
    
    if (performanceTiming.domReady > 3000) {
      console.warn(`⚠️ DOM ready time is high: ${performanceTiming.domReady}ms`)
    }
    
    console.log(`📊 Performance metrics:`)
    console.log(`  - Load complete: ${performanceTiming.loadComplete}ms`)
    console.log(`  - DOM ready: ${performanceTiming.domReady}ms`)
    console.log(`  - First paint: ${performanceTiming.firstPaint}ms`)
  }

  // Test 8: Security Headers Test
  async testSecurityHeaders() {
    const response = await this.page.goto(this.baseUrl, { waitUntil: 'networkidle0' })
    const headers = response.headers()
    
    // Required security headers
    const requiredHeaders = [
      'strict-transport-security',
      'x-frame-options',
      'x-content-type-options',
      'x-xss-protection',
      'content-security-policy'
    ]
    
    const missingHeaders = requiredHeaders.filter(header => !headers[header])
    
    if (missingHeaders.length > 0) {
      throw new Error(`Missing security headers: ${missingHeaders.join(', ')}`)
    }
    
    // Check HSTS header
    const hsts = headers['strict-transport-security']
    if (!hsts.includes('max-age=') || !hsts.includes('includeSubDomains')) {
      throw new Error('Invalid HSTS header configuration')
    }
  }

  // Generate report
  async generateReport() {
    const endTime = Date.now()
    const totalDuration = endTime - this.startTime
    
    const passed = this.results.filter(r => r.status === 'PASS').length
    const failed = this.results.filter(r => r.status === 'FAIL').length
    const total = this.results.length
    
    const report = {
      timestamp: new Date().toISOString(),
      environment: 'production',
      baseUrl: this.baseUrl,
      apiUrl: this.apiUrl,
      summary: {
        total,
        passed,
        failed,
        passRate: Math.round((passed / total) * 100),
        totalDuration
      },
      tests: this.results
    }
    
    // Save report to file
    const reportPath = path.join(__dirname, `../../reports/smoke-test-${Date.now()}.json`)
    await fs.mkdir(path.dirname(reportPath), { recursive: true })
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2))
    
    console.log(`\n📊 Smoke Test Report`)
    console.log(`===================`)
    console.log(`Total tests: ${total}`)
    console.log(`Passed: ${passed}`)
    console.log(`Failed: ${failed}`)
    console.log(`Pass rate: ${report.summary.passRate}%`)
    console.log(`Total duration: ${totalDuration}ms`)
    console.log(`Report saved: ${reportPath}`)
    
    return report
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close()
    }
  }

  // Main test execution
  async run() {
    try {
      await this.initialize()
      
      // Run all tests
      await this.runTest('Homepage Load', () => this.testHomepageLoad())
      await this.runTest('Authentication Flow', () => this.testAuthenticationFlow())
      await this.runTest('API Health Check', () => this.testApiHealth())
      await this.runTest('API Authentication', () => this.testApiAuthentication())
      await this.runTest('Database Connectivity', () => this.testDatabaseConnectivity())
      await this.runTest('SSL Certificate', () => this.testSSLCertificate())
      await this.runTest('Performance', () => this.testPerformance())
      await this.runTest('Security Headers', () => this.testSecurityHeaders())
      
      const report = await this.generateReport()
      
      // Exit with error code if any tests failed
      if (report.summary.failed > 0) {
        process.exit(1)
      }
      
    } catch (error) {
      console.error(`💥 Smoke test suite failed: ${error.message}`)
      process.exit(1)
    } finally {
      await this.cleanup()
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const smokeTests = new ProductionSmokeTests()
  smokeTests.run()
}

module.exports = ProductionSmokeTests
```

## Status: Production Deployment Guide Created ✅

The comprehensive production deployment system has been created with:

- ✅ **Pre-deployment Verification**: System requirements, services, network, SSL, database checks
- ✅ **Automated Deployment Script**: Complete deployment automation with rollback capability
- ✅ **Smoke Testing Suite**: Comprehensive production testing framework
- ✅ **Health Monitoring**: Real-time system health verification
- ✅ **Error Handling**: Robust error handling with automatic rollback
- ✅ **Notification System**: Slack integration for deployment status updates

**Key Features:**
- **Automated Deployment**: One-command production deployment
- **Rollback Capability**: Instant rollback to previous version if issues occur
- **Comprehensive Testing**: 8 critical smoke tests covering all system components
- **Real-time Monitoring**: Health checks for all services and dependencies
- **Security Validation**: SSL certificate and security headers verification
- **Performance Testing**: Load time and performance metrics validation

The deployment system is ready for **Phase 7.2: Launch Activities**!

---
*Production deployment guide created on: ${new Date().toISOString().split('T')[0]}*
*Deployment Method: Automated with rollback capability*
*Testing Coverage: 8 critical smoke tests*
*Project: Aureus Alliance Web Dashboard*
*Phase: 7.2 Launch Activities*
