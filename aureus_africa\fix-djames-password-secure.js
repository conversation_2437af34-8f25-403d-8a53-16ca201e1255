import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔐 FIXING DJAMES PASSWORD - MAKING ACCOUNT SECURE');
console.log('================================================');

const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ Missing VITE_SUPABASE_SERVICE_ROLE_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const fixDjamesPassword = async () => {
  try {
    const email = '<EMAIL>';
    const correctPassword = 'Gunst0n5o0!@#';
    
    console.log(`📧 Email: ${email}`);
    console.log(`🔑 Password: ${correctPassword}`);
    
    // Step 1: Find the user
    console.log('\n📋 Step 1: Finding user in database...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, username, email, password_hash, telegram_id')
      .eq('email', email)
      .single();
    
    if (userError) {
      console.error('❌ Error finding user:', userError);
      return;
    }
    
    if (!user) {
      console.error('❌ User not found');
      return;
    }
    
    console.log('✅ User found:');
    console.log(`   ID: ${user.id}`);
    console.log(`   Username: ${user.username}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Telegram ID: ${user.telegram_id}`);
    
    // Step 2: Generate secure password hash
    console.log('\n📋 Step 2: Generating secure password hash...');
    const newHash = await bcrypt.hash(correctPassword, 12);
    console.log('✅ New secure hash generated');
    
    // Step 3: Test the hash works
    console.log('\n📋 Step 3: Testing hash verification...');
    const testResult = await bcrypt.compare(correctPassword, newHash);
    if (!testResult) {
      console.error('❌ Hash verification failed - something is wrong!');
      return;
    }
    console.log('✅ Hash verification successful');
    
    // Step 4: Update the user's password hash
    console.log('\n📋 Step 4: Updating password in database...');
    const { error: updateError } = await supabase
      .from('users')
      .update({
        password_hash: newHash,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);
    
    if (updateError) {
      console.error('❌ Error updating password:', updateError);
      return;
    }
    
    console.log('✅ Password updated successfully!');
    
    // Step 5: Verify the update worked
    console.log('\n📋 Step 5: Verifying the update...');
    const { data: updatedUser, error: verifyError } = await supabase
      .from('users')
      .select('id, email, password_hash')
      .eq('id', user.id)
      .single();
    
    if (verifyError) {
      console.error('❌ Error verifying update:', verifyError);
      return;
    }
    
    // Test the updated password
    const finalTest = await bcrypt.compare(correctPassword, updatedUser.password_hash);
    if (!finalTest) {
      console.error('❌ Final verification failed!');
      return;
    }
    
    console.log('✅ Final verification successful!');
    console.log('\n🎉 DJAMES ACCOUNT IS NOW SECURE!');
    console.log('================================================');
    console.log(`✅ Email: ${email}`);
    console.log(`✅ Password: ${correctPassword}`);
    console.log('✅ Account is secure with proper password hash');
    console.log('✅ You can now login normally without any bypass');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
};

fixDjamesPassword();
