#!/usr/bin/env node

/**
 * TEST PASSWORD RESET FROM WEB INTERFACE
 * 
 * This script simulates the exact same flow that happens when a user
 * clicks "Forgot your password?" on the web interface.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const serviceClient = createClient(supabaseUrl, supabaseServiceKey);

async function testWebPasswordReset() {
  console.log('🧪 Testing password reset from web interface...\n');

  try {
    const testEmail = '<EMAIL>';
    
    console.log(`📧 Testing password reset for: ${testEmail}`);
    
    // Step 1: Check if user exists (same as passwordReset.ts does)
    console.log('\n1️⃣ Checking if user exists...');
    
    const { data: user, error: userError } = await serviceClient
      .from('users')
      .select('id, email, username')
      .eq('email', testEmail)
      .single();
    
    if (userError) {
      console.error('❌ User lookup failed:', userError);
      return false;
    }
    
    if (!user) {
      console.error('❌ User not found');
      return false;
    }
    
    console.log('✅ User found:', { id: user.id, email: user.email, username: user.username });
    
    // Step 2: Generate reset token (same as passwordReset.ts does)
    console.log('\n2️⃣ Generating reset token...');
    
    const resetToken = require('crypto').randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    
    console.log('🔑 Reset token generated:', resetToken.substring(0, 16) + '...');
    
    // Step 3: Store reset token (same as passwordReset.ts does)
    console.log('\n3️⃣ Storing reset token in database...');
    
    const { error: updateError } = await serviceClient
      .from('users')
      .update({
        reset_token: resetToken,
        reset_token_expires: resetExpires.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);
    
    if (updateError) {
      console.error('❌ Failed to store reset token:', updateError);
      return false;
    }
    
    console.log('✅ Reset token stored successfully');
    
    // Step 4: Verify token was stored
    console.log('\n4️⃣ Verifying token storage...');
    
    const { data: verifyUser, error: verifyError } = await serviceClient
      .from('users')
      .select('reset_token, reset_token_expires')
      .eq('id', user.id)
      .single();
    
    if (verifyError) {
      console.error('❌ Token verification failed:', verifyError);
      return false;
    }
    
    if (verifyUser.reset_token !== resetToken) {
      console.error('❌ Token mismatch');
      return false;
    }
    
    console.log('✅ Token verification successful');
    
    // Step 5: Test reset link format
    console.log('\n5️⃣ Testing reset link format...');
    
    const resetLink = `http://localhost:8006/reset-password?token=${resetToken}`;
    console.log('🔗 Reset link:', resetLink);
    
    // Step 6: Simulate email sending (log what would be sent)
    console.log('\n6️⃣ Email content that would be sent:');
    console.log('📧 To:', testEmail);
    console.log('📧 Subject: Reset Your Aureus Alliance Password');
    console.log('📧 Content:');
    console.log(`
Hello ${user.username || 'User'},

You requested to reset your password for your Aureus Alliance account.

Click the link below to reset your password:
${resetLink}

This link will expire in 24 hours.

If you didn't request this password reset, please ignore this email.

Best regards,
The Aureus Alliance Team
    `);
    
    // Step 7: Clean up test token
    console.log('\n7️⃣ Cleaning up test token...');
    
    const { error: cleanupError } = await serviceClient
      .from('users')
      .update({
        reset_token: null,
        reset_token_expires: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);
    
    if (cleanupError) {
      console.error('❌ Cleanup failed:', cleanupError);
      return false;
    }
    
    console.log('✅ Test token cleaned up');
    
    console.log('\n🎉 Web password reset test PASSED!');
    console.log('📋 Test Summary:');
    console.log('   ✅ User lookup with service role client working');
    console.log('   ✅ Reset token generation working');
    console.log('   ✅ Database update with service role client working');
    console.log('   ✅ Token storage and retrieval working');
    console.log('   ✅ Reset link format correct');
    console.log('   ✅ Email content generation working');
    console.log('   ✅ Cleanup working');
    
    console.log('\n🔗 Manual Testing:');
    console.log('1. Go to: http://localhost:8006/login');
    console.log('2. Click "Login (Web)" tab');
    console.log(`3. Enter email: ${testEmail}`);
    console.log('4. Click "Forgot your password?"');
    console.log('5. Should see success message');
    console.log('6. Check email for reset link');
    
    return true;
    
  } catch (error) {
    console.error('❌ Web password reset test failed:', error);
    return false;
  }
}

// Run the test
testWebPasswordReset().then(success => {
  console.log(`\n${success ? '🎉' : '💥'} Test ${success ? 'PASSED' : 'FAILED'}`);
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
