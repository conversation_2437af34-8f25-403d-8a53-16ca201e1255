import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeSchema() {
  try {
    console.log('🔍 Analyzing database schema for account merging...\n');
    
    // Based on schema analysis, these tables reference users.id:
    const tablesWithUserFK = [
      'telegram_users.user_id',
      'aureus_share_purchases.user_id',
      'commission_balances.user_id',
      'commission_transactions.referrer_id',
      'commission_transactions.referred_id',
      'referrals.referrer_id',
      'referrals.referred_id',
      'crypto_payment_transactions.user_id',
      'terms_acceptance.user_id',
      'kyc_information.user_id',
      'commission_withdrawals.user_id'
    ];

    console.log('📋 Tables that reference users.id:');
    tablesWithUserFK.forEach(table => {
      console.log(`  • ${table} → users.id`);
    });

    // Check current data distribution
    console.log('\n📊 Current Data Analysis:');
    
    // Check users table
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email, telegram_id')
      .order('id');
    
    if (!usersError) {
      console.log(`\n👥 Users table (${users.length} records):`);
      users.forEach(user => {
        const hasWeb = user.email && !user.email.includes('@telegram.local');
        const hasTelegram = user.telegram_id || user.email.includes('@telegram.local');
        console.log(`  ID: ${user.id}, ${user.username}, Web: ${hasWeb}, Telegram: ${hasTelegram}`);
      });
    }

    // Check telegram_users table
    const { data: telegramUsers, error: tgError } = await supabase
      .from('telegram_users')
      .select('telegram_id, user_id, username, first_name')
      .order('telegram_id');
    
    if (!tgError) {
      console.log(`\n📱 Telegram users table (${telegramUsers.length} records):`);
      telegramUsers.forEach(tUser => {
        console.log(`  Telegram ID: ${tUser.telegram_id}, Linked to User: ${tUser.user_id || 'NULL'}, ${tUser.username}`);
      });
    }

    // Check share purchases distribution
    const { data: purchases, error: purchasesError } = await supabase
      .from('aureus_share_purchases')
      .select('user_id, shares_purchased, total_amount, status')
      .order('user_id');
    
    if (!purchasesError) {
      console.log(`\n💰 Share purchases (${purchases.length} records):`);
      const userPurchases = {};
      purchases.forEach(p => {
        if (!userPurchases[p.user_id]) {
          userPurchases[p.user_id] = { shares: 0, amount: 0, count: 0 };
        }
        userPurchases[p.user_id].shares += p.shares_purchased;
        userPurchases[p.user_id].amount += parseFloat(p.total_amount);
        userPurchases[p.user_id].count += 1;
      });
      
      Object.entries(userPurchases).forEach(([userId, data]) => {
        console.log(`  User ${userId}: ${data.shares} shares, R${data.amount.toFixed(2)}, ${data.count} purchases`);
      });
    }

    // Check commission balances
    const { data: commissions, error: commError } = await supabase
      .from('commission_balances')
      .select('user_id, usdt_balance, share_balance, total_earned_usdt, total_earned_shares')
      .order('user_id');
    
    if (!commError) {
      console.log(`\n💸 Commission balances (${commissions.length} records):`);
      commissions.forEach(c => {
        console.log(`  User ${c.user_id}: USDT: ${c.usdt_balance}, Shares: ${c.share_balance}, Earned: ${c.total_earned_usdt}/${c.total_earned_shares}`);
      });
    }

    // Check referrals
    const { data: referrals, error: refError } = await supabase
      .from('referrals')
      .select('referrer_id, referred_id, total_commission, status')
      .order('referrer_id');
    
    if (!refError) {
      console.log(`\n🔗 Referrals (${referrals.length} records):`);
      referrals.forEach(r => {
        console.log(`  Referrer ${r.referrer_id} → Referred ${r.referred_id}, Commission: R${r.total_commission}, Status: ${r.status}`);
      });
    }

  } catch (error) {
    console.error('❌ Analysis error:', error);
  }
}

analyzeSchema();
