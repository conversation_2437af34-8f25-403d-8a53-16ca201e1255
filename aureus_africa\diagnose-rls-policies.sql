-- RLS Policy Diagnostic Queries
-- Run these queries to identify specific policy issues

-- Query 2: Check for problematic RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Query 3: Check Security Definer Functions
SELECT 
    n.nspname as schema_name,
    p.proname as function_name,
    pg_get_function_result(p.oid) as return_type,
    pg_get_function_arguments(p.oid) as arguments,
    CASE p.prosecdef 
        WHEN true THEN 'SECURITY DEFINER'
        ELSE 'SECURITY INVOKER'
    END as security_type
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
    AND p.prosecdef = true
ORDER BY p.proname;

-- Query 4: Check for Missing or Broken Functions
SELECT 
    routine_name,
    routine_type,
    security_type,
    is_deterministic
FROM information_schema.routines 
WHERE routine_schema = 'public'
    AND routine_name IN ('is_admin_user', 'get_current_user_id')
ORDER BY routine_name;

-- Query 5: Test auth functions
SELECT 
    'auth.uid()' as function_name,
    CASE 
        WHEN auth.uid() IS NULL THEN 'NULL (not authenticated)'
        ELSE 'Working: ' || auth.uid()::text
    END as status;

-- Query 6: Check for tables with RLS enabled but no policies (potential issues)
SELECT 
    t.schemaname,
    t.tablename,
    t.rowsecurity as rls_enabled,
    COALESCE(p.policy_count, 0) as policy_count
FROM pg_tables t
LEFT JOIN (
    SELECT schemaname, tablename, count(*) as policy_count
    FROM pg_policies 
    WHERE schemaname = 'public'
    GROUP BY schemaname, tablename
) p ON t.schemaname = p.schemaname AND t.tablename = p.tablename
WHERE t.schemaname = 'public' 
    AND t.rowsecurity = true 
    AND COALESCE(p.policy_count, 0) = 0
ORDER BY t.tablename;
