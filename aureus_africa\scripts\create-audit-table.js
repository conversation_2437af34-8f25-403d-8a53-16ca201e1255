import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createAuditLogsTable() {
  try {
    console.log('📋 Creating admin_audit_logs table...');
    
    // Create the table directly using Supabase client
    const { error } = await supabase.rpc('exec', {
      sql: `
        CREATE TABLE IF NOT EXISTS admin_audit_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          
          -- Admin Information
          admin_email VARCHAR(255) NOT NULL,
          admin_user_id UUID,
          admin_role VARCHAR(50),
          
          -- Action Details
          action VARCHAR(100) NOT NULL,
          target_type VARCHAR(50) NOT NULL,
          target_id VARCHAR(255) NOT NULL,
          
          -- Change Details
          old_values JSONB DEFAULT '{}'::jsonb,
          new_values JSONB DEFAULT '{}'::jsonb,
          details JSONB DEFAULT '{}'::jsonb,
          
          -- Request Information
          ip_address INET,
          user_agent TEXT,
          session_id VARCHAR(255),
          
          -- Timestamps
          timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          
          -- Status
          status VARCHAR(20) DEFAULT 'success'
        );
      `
    });

    if (error) {
      console.error('❌ Error creating table:', error);
      return;
    }

    console.log('✅ Table created successfully!');

    // Create indexes
    console.log('📋 Creating indexes...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_admin_email ON admin_audit_logs(admin_email);',
      'CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_action ON admin_audit_logs(action);',
      'CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_target_type ON admin_audit_logs(target_type);',
      'CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_timestamp ON admin_audit_logs(timestamp DESC);'
    ];

    for (const indexSql of indexes) {
      const { error: indexError } = await supabase.rpc('exec', { sql: indexSql });
      if (indexError) {
        console.error('⚠️ Error creating index:', indexError);
      }
    }

    console.log('✅ Indexes created successfully!');

    // Test the table by inserting a sample record
    console.log('🧪 Testing table with sample record...');
    
    const { error: insertError } = await supabase
      .from('admin_audit_logs')
      .insert({
        admin_email: '<EMAIL>',
        action: 'CREATE_TABLE',
        target_type: 'system',
        target_id: 'admin_audit_logs',
        details: { message: 'Table created successfully' }
      });

    if (insertError) {
      console.error('❌ Error inserting test record:', insertError);
    } else {
      console.log('✅ Test record inserted successfully!');
    }

    // Query the table to verify
    const { data, error: queryError } = await supabase
      .from('admin_audit_logs')
      .select('*')
      .limit(1);

    if (queryError) {
      console.error('❌ Error querying table:', queryError);
    } else {
      console.log('✅ Table query successful! Records found:', data?.length || 0);
    }

    console.log('🎉 Audit logs table setup complete!');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

createAuditLogsTable();
