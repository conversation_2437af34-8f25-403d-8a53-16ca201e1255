#!/usr/bin/env node

/**
 * FINAL VERIFICATION
 * 
 * This script performs a final verification that all issues are resolved
 * and the system is working correctly.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const finalVerification = async () => {
  try {
    console.log('🔍 FINAL VERIFICATION - Checking all systems...\n');

    let allTestsPassed = true;

    // Test 1: Verify Telegram user login credentials
    console.log('📋 Test 1: Telegram User Login Verification');
    const telegramId = '1393852532';
    const testPassword = 'Gunst0n5o0!@#';

    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (telegramError || !telegramUser) {
      console.log('❌ Test 1 FAILED: Telegram user not found');
      allTestsPassed = false;
    } else if (!telegramUser.user_id) {
      console.log('❌ Test 1 FAILED: Telegram user not linked');
      allTestsPassed = false;
    } else {
      // Check linked user password
      const { data: linkedUser, error: linkedError } = await supabase
        .from('users')
        .select('password_hash')
        .eq('id', telegramUser.user_id)
        .single();

      if (linkedError || !linkedUser) {
        console.log('❌ Test 1 FAILED: Linked user not found');
        allTestsPassed = false;
      } else {
        const passwordValid = await bcrypt.compare(testPassword, linkedUser.password_hash);
        if (passwordValid) {
          console.log('✅ Test 1 PASSED: Telegram user login credentials are correct');
        } else {
          console.log('❌ Test 1 FAILED: Password verification failed');
          allTestsPassed = false;
        }
      }
    }

    // Test 2: Verify admin audit logging works
    console.log('\n📋 Test 2: Admin Audit Logging Verification');
    
    const logEntry = {
      admin_telegram_id: 0,
      admin_username: '<EMAIL>',
      action: 'VERIFICATION_TEST',
      target_type: 'system',
      target_id: 'verification',
      details: {
        test: true,
        timestamp: new Date().toISOString(),
        source: 'final_verification'
      }
    };

    const { error: logError } = await supabase
      .from('admin_audit_logs')
      .insert(logEntry);

    if (logError) {
      console.log('❌ Test 2 FAILED: Admin audit logging error:', logError.message);
      allTestsPassed = false;
    } else {
      console.log('✅ Test 2 PASSED: Admin audit logging works correctly');
      
      // Clean up test record
      await supabase
        .from('admin_audit_logs')
        .delete()
        .eq('action', 'VERIFICATION_TEST');
    }

    // Test 3: Verify database connectivity and schema
    console.log('\n📋 Test 3: Database Schema Verification');
    
    const { data: schemaTest, error: schemaError } = await supabase
      .from('admin_audit_logs')
      .select('admin_telegram_id, admin_username, action, target_type, target_id, details')
      .limit(1);

    if (schemaError) {
      console.log('❌ Test 3 FAILED: Database schema error:', schemaError.message);
      allTestsPassed = false;
    } else {
      console.log('✅ Test 3 PASSED: Database schema is correct');
    }

    // Test 4: Verify Telegram user identification
    console.log('\n📋 Test 4: Telegram User Identification');
    
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', 4)
      .single();

    if (userError || !testUser) {
      console.log('❌ Test 4 FAILED: Test user not found');
      allTestsPassed = false;
    } else {
      // Check if this user is linked to Telegram
      const { data: linkedTelegram, error: linkError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('user_id', testUser.id)
        .single();

      if (linkError || !linkedTelegram) {
        console.log('❌ Test 4 FAILED: Telegram link not found');
        allTestsPassed = false;
      } else {
        console.log('✅ Test 4 PASSED: Telegram user identification works');
        console.log(`   User ID ${testUser.id} is linked to Telegram ID ${linkedTelegram.telegram_id}`);
      }
    }

    // Final Results
    console.log('\n' + '='.repeat(60));
    if (allTestsPassed) {
      console.log('🎉 FINAL VERIFICATION: ALL TESTS PASSED');
      console.log('✅ System Status: FULLY OPERATIONAL');
      console.log('✅ Login System: Working correctly');
      console.log('✅ Admin Panel: Functioning without errors');
      console.log('✅ Database: Schema and connectivity verified');
      console.log('✅ Telegram Integration: Properly linked and functional');
      
      console.log('\n📋 READY FOR USE:');
      console.log(`   🔑 User can login with Telegram ID: ${telegramId}`);
      console.log(`   🔑 Password: ${testPassword}`);
      console.log('   🛠️ Admin panel works without database errors');
      console.log('   📊 All systems are operational');
      
    } else {
      console.log('❌ FINAL VERIFICATION: SOME TESTS FAILED');
      console.log('⚠️ System Status: NEEDS ATTENTION');
      console.log('Please review the failed tests above and address any issues.');
    }
    console.log('='.repeat(60));

  } catch (error) {
    console.error('❌ Final verification failed:', error);
  }
};

finalVerification();
