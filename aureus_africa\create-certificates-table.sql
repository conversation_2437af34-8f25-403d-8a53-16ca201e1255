-- Create certificates table with correct data types
-- This table will store official share certificates for users

CREATE TABLE IF NOT EXISTS certificates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  purchase_id UUID REFERENCES aureus_share_purchases(id) ON DELETE SET NULL,
  certificate_number VARCHAR(50) UNIQUE NOT NULL,
  shares_count INTEGER NOT NULL,
  issue_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'issued' CHECK (status IN ('issued', 'revoked', 'transferred')),
  certificate_data JSONB DEFAULT '{}'::jsonb, -- Store additional certificate metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for certificates table
CREATE INDEX IF NOT EXISTS idx_certificates_user_id ON certificates(user_id);
CREATE INDEX IF NOT EXISTS idx_certificates_purchase_id ON certificates(purchase_id);
CREATE INDEX IF NOT EXISTS idx_certificates_certificate_number ON certificates(certificate_number);
CREATE INDEX IF NOT EXISTS idx_certificates_status ON certificates(status);

-- Function to generate unique certificate numbers
CREATE OR REPLACE FUNCTION generate_certificate_number()
RETURNS VARCHAR(50) AS $$
DECLARE
  new_number VARCHAR(50);
  counter INTEGER := 1;
BEGIN
  LOOP
    -- Generate certificate number in format: AUR-YYYY-NNNNNN
    new_number := 'AUR-' || EXTRACT(YEAR FROM NOW()) || '-' || LPAD(counter::TEXT, 6, '0');
    
    -- Check if this number already exists
    IF NOT EXISTS (SELECT 1 FROM certificates WHERE certificate_number = new_number) THEN
      RETURN new_number;
    END IF;
    
    counter := counter + 1;
    
    -- Safety check to prevent infinite loop
    IF counter > 999999 THEN
      RAISE EXCEPTION 'Unable to generate unique certificate number';
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically generate certificate numbers
CREATE OR REPLACE FUNCTION set_certificate_number()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.certificate_number IS NULL OR NEW.certificate_number = '' THEN
    NEW.certificate_number := generate_certificate_number();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_certificate_number
  BEFORE INSERT ON certificates
  FOR EACH ROW
  EXECUTE FUNCTION set_certificate_number();

-- Admin function to manually create certificates (called by admin interface)
CREATE OR REPLACE FUNCTION admin_create_certificate(
  p_user_id INTEGER,
  p_purchase_id UUID,
  p_shares_count INTEGER,
  p_certificate_data JSONB DEFAULT '{}'::jsonb
)
RETURNS VARCHAR(50) AS $$
DECLARE
  new_cert_number VARCHAR(50);
BEGIN
  -- Generate certificate number
  new_cert_number := generate_certificate_number();

  -- Insert certificate
  INSERT INTO certificates (
    user_id,
    purchase_id,
    certificate_number,
    shares_count,
    certificate_data
  ) VALUES (
    p_user_id,
    p_purchase_id,
    new_cert_number,
    p_shares_count,
    p_certificate_data
  );

  RETURN new_cert_number;
END;
$$ LANGUAGE plpgsql;

-- Admin function to create certificates for approved purchases
CREATE OR REPLACE FUNCTION admin_create_certificates_for_approved_purchases()
RETURNS TABLE(certificate_number VARCHAR(50), user_id INTEGER, shares_count INTEGER) AS $$
BEGIN
  RETURN QUERY
  WITH new_certificates AS (
    INSERT INTO certificates (user_id, purchase_id, shares_count, certificate_data)
    SELECT
      asp.user_id,
      asp.id,
      asp.shares_purchased,
      jsonb_build_object(
        'package_name', asp.package_name,
        'total_amount', asp.total_amount,
        'commission_used', asp.commission_used,
        'payment_method', asp.payment_method,
        'created_by', 'admin_batch_creation'
      )
    FROM aureus_share_purchases asp
    WHERE asp.status = 'active'
      AND NOT EXISTS (
        SELECT 1 FROM certificates c
        WHERE c.purchase_id = asp.id
      )
    RETURNING certificates.certificate_number, certificates.user_id, certificates.shares_count
  )
  SELECT nc.certificate_number, nc.user_id, nc.shares_count
  FROM new_certificates nc;
END;
$$ LANGUAGE plpgsql;

-- Admin can run this to create certificates for existing approved purchases:
-- SELECT * FROM admin_create_certificates_for_approved_purchases();

-- Grant necessary permissions (UUID tables don't have sequences)
GRANT SELECT, INSERT, UPDATE ON certificates TO authenticated;

-- Add RLS (Row Level Security) policies
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own certificates
-- Since your system uses INTEGER user_id, we need to match against the users table
CREATE POLICY "Users can view own certificates" ON certificates
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM users
      WHERE users.id = certificates.user_id
    )
  );

-- Alternative simpler policy if you want to disable RLS for now:
-- DROP POLICY IF EXISTS "Users can view own certificates" ON certificates;
-- This allows all authenticated users to see certificates (you can add proper auth later)

-- Policy: Allow authenticated users to insert certificates (for admin functions)
CREATE POLICY "Authenticated can insert certificates" ON certificates
  FOR INSERT TO authenticated WITH CHECK (true);

-- Policy: Allow authenticated users to update certificates (for admin functions)
CREATE POLICY "Authenticated can update certificates" ON certificates
  FOR UPDATE TO authenticated USING (true);

COMMENT ON TABLE certificates IS 'Official share certificates for user purchases';
COMMENT ON COLUMN certificates.certificate_number IS 'Unique certificate number in format AUR-YYYY-NNNNNN';
COMMENT ON COLUMN certificates.certificate_data IS 'Additional certificate metadata stored as JSON';
