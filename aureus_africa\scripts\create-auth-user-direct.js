import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://ixqjqjqjqjqjqjqjqjqj.supabase.co'
const supabaseServiceKey = 'YOUR_SERVICE_ROLE_KEY_HERE' // Replace with your service role key

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createAuthUser() {
  try {
    console.log('Creating auth user...')
    
    const { data, error } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'Admin123!',
      email_confirm: true
    })

    if (error) {
      console.error('Error creating user:', error)
      return
    }

    console.log('User created successfully:', data.user.email)
    console.log('User ID:', data.user.id)
    
  } catch (error) {
    console.error('Unexpected error:', error)
  }
}

createAuthUser()
