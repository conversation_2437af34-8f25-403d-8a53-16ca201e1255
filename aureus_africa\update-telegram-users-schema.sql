-- Update telegram_users table to support web authentication
-- Add missing fields needed for web login functionality

-- Add email field (required for web login)
ALTER TABLE telegram_users 
ADD COLUMN IF NOT EXISTS email VARCHAR(255) UNIQUE;

-- Add password_hash field (required for web login)
ALTER TABLE telegram_users 
ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- Add phone field (for user profile)
ALTER TABLE telegram_users 
ADD COLUMN IF NOT EXISTS phone VARCHAR(50);

-- Add full_name field (for user profile)
ALTER TABLE telegram_users 
ADD COLUMN IF NOT EXISTS full_name VARCHAR(255);

-- Add country_of_residence field (for compliance)
ALTER TABLE telegram_users 
ADD COLUMN IF NOT EXISTS country_of_residence VARCHAR(3);

-- Add is_web_enabled field (to track if account has web access)
ALTER TABLE telegram_users 
ADD COLUMN IF NOT EXISTS is_web_enabled BOOLEAN DEFAULT FALSE;

-- Add web_linked_at timestamp (to track when web access was enabled)
ALTER TABLE telegram_users 
ADD COLUMN IF NOT EXISTS web_linked_at TIMESTAMP WITH TIME ZONE;

-- Create indexes for new fields
CREATE INDEX IF NOT EXISTS idx_telegram_users_email ON telegram_users(email);
CREATE INDEX IF NOT EXISTS idx_telegram_users_is_web_enabled ON telegram_users(is_web_enabled);

-- Update existing registration_mode values to be more descriptive
UPDATE telegram_users 
SET registration_mode = 'telegram_only' 
WHERE registration_mode = 'login' AND is_web_enabled = FALSE;

-- Verify the schema changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'telegram_users' 
AND column_name IN ('email', 'password_hash', 'phone', 'full_name', 'country_of_residence', 'is_web_enabled', 'web_linked_at')
ORDER BY column_name;
