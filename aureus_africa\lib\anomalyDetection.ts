/**
 * AI-POWERED ANOMALY DETECTION SYSTEM
 * 
 * This module provides intelligent anomaly detection for user behavior,
 * login patterns, financial transactions, and security threats.
 */

import { supabase } from './supabase';

interface UserBehaviorProfile {
  userId: number;
  avgSessionDuration: number;
  commonLoginTimes: number[];
  commonIpAddresses: string[];
  avgTransactionAmount: number;
  transactionFrequency: number;
  commonDevices: string[];
  geolocationPattern: string[];
  lastUpdated: Date;
}

interface AnomalyScore {
  score: number; // 0-100, higher = more anomalous
  factors: string[];
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  confidence: number; // 0-100, higher = more confident
}

interface SecurityAnomaly {
  id: string;
  userId: number;
  anomalyType: string;
  score: number;
  severity: string;
  description: string;
  factors: string[];
  metadata: any;
  timestamp: Date;
  resolved: boolean;
}

class AnomalyDetectionEngine {
  private userProfiles: Map<number, UserBehaviorProfile> = new Map();
  private readonly ANOMALY_THRESHOLDS = {
    LOW: 30,
    MEDIUM: 50,
    HIGH: 70,
    CRITICAL: 85
  };

  /**
   * Analyze user login for anomalies
   */
  async analyzeLoginAnomaly(
    userId: number,
    ipAddress: string,
    userAgent: string,
    geolocation?: { country: string; city: string; lat: number; lon: number }
  ): Promise<AnomalyScore> {
    try {
      console.log(`🔍 Analyzing login anomaly for user ${userId}`);

      const profile = await this.getUserBehaviorProfile(userId);
      const factors: string[] = [];
      let totalScore = 0;
      let confidence = 0;

      // 1. IP Address Analysis
      const ipScore = this.analyzeIpAnomaly(ipAddress, profile);
      if (ipScore.score > 0) {
        totalScore += ipScore.score;
        factors.push(...ipScore.factors);
        confidence += 20;
      }

      // 2. Time-based Analysis
      const timeScore = this.analyzeTimeAnomaly(new Date(), profile);
      if (timeScore.score > 0) {
        totalScore += timeScore.score;
        factors.push(...timeScore.factors);
        confidence += 15;
      }

      // 3. Device/User Agent Analysis
      const deviceScore = this.analyzeDeviceAnomaly(userAgent, profile);
      if (deviceScore.score > 0) {
        totalScore += deviceScore.score;
        factors.push(...deviceScore.factors);
        confidence += 15;
      }

      // 4. Geolocation Analysis
      if (geolocation) {
        const geoScore = this.analyzeGeolocationAnomaly(geolocation, profile);
        if (geoScore.score > 0) {
          totalScore += geoScore.score;
          factors.push(...geoScore.factors);
          confidence += 25;
        }
      }

      // 5. Velocity Analysis (rapid successive logins)
      const velocityScore = await this.analyzeLoginVelocity(userId);
      if (velocityScore.score > 0) {
        totalScore += velocityScore.score;
        factors.push(...velocityScore.factors);
        confidence += 25;
      }

      const finalScore = Math.min(100, totalScore);
      const severity = this.calculateSeverity(finalScore);

      // Log anomaly if significant
      if (finalScore >= this.ANOMALY_THRESHOLDS.MEDIUM) {
        await this.logAnomaly(userId, 'LOGIN_ANOMALY', finalScore, severity, factors, {
          ipAddress,
          userAgent,
          geolocation,
          timestamp: new Date().toISOString()
        });
      }

      console.log(`🔍 Login anomaly analysis complete: Score ${finalScore}, Severity ${severity}`);
      return { score: finalScore, factors, severity, confidence };

    } catch (error) {
      console.error('❌ Login anomaly analysis error:', error);
      return { score: 0, factors: ['Analysis failed'], severity: 'LOW', confidence: 0 };
    }
  }

  /**
   * Analyze financial transaction for anomalies
   */
  async analyzeTransactionAnomaly(
    userId: number,
    amount: number,
    transactionType: string,
    metadata: any = {}
  ): Promise<AnomalyScore> {
    try {
      console.log(`💰 Analyzing transaction anomaly for user ${userId}: $${amount}`);

      const profile = await this.getUserBehaviorProfile(userId);
      const factors: string[] = [];
      let totalScore = 0;
      let confidence = 0;

      // 1. Amount Analysis
      const amountScore = this.analyzeAmountAnomaly(amount, profile);
      if (amountScore.score > 0) {
        totalScore += amountScore.score;
        factors.push(...amountScore.factors);
        confidence += 30;
      }

      // 2. Frequency Analysis
      const frequencyScore = await this.analyzeTransactionFrequency(userId);
      if (frequencyScore.score > 0) {
        totalScore += frequencyScore.score;
        factors.push(...frequencyScore.factors);
        confidence += 25;
      }

      // 3. Time Pattern Analysis
      const timeScore = this.analyzeTransactionTimeAnomaly(new Date(), profile);
      if (timeScore.score > 0) {
        totalScore += timeScore.score;
        factors.push(...timeScore.factors);
        confidence += 20;
      }

      // 4. Velocity Analysis (rapid transactions)
      const velocityScore = await this.analyzeTransactionVelocity(userId, amount);
      if (velocityScore.score > 0) {
        totalScore += velocityScore.score;
        factors.push(...velocityScore.factors);
        confidence += 25;
      }

      const finalScore = Math.min(100, totalScore);
      const severity = this.calculateSeverity(finalScore);

      // Log anomaly if significant
      if (finalScore >= this.ANOMALY_THRESHOLDS.MEDIUM) {
        await this.logAnomaly(userId, 'TRANSACTION_ANOMALY', finalScore, severity, factors, {
          amount,
          transactionType,
          ...metadata,
          timestamp: new Date().toISOString()
        });
      }

      console.log(`💰 Transaction anomaly analysis complete: Score ${finalScore}, Severity ${severity}`);
      return { score: finalScore, factors, severity, confidence };

    } catch (error) {
      console.error('❌ Transaction anomaly analysis error:', error);
      return { score: 0, factors: ['Analysis failed'], severity: 'LOW', confidence: 0 };
    }
  }

  /**
   * Get or create user behavior profile
   */
  private async getUserBehaviorProfile(userId: number): Promise<UserBehaviorProfile> {
    try {
      // Check cache first
      if (this.userProfiles.has(userId)) {
        const profile = this.userProfiles.get(userId)!;
        // Refresh if older than 24 hours
        if (Date.now() - profile.lastUpdated.getTime() < 24 * 60 * 60 * 1000) {
          return profile;
        }
      }

      // Build profile from historical data
      const profile = await this.buildUserProfile(userId);
      this.userProfiles.set(userId, profile);
      return profile;

    } catch (error) {
      console.error('❌ Error getting user profile:', error);
      return this.getDefaultProfile(userId);
    }
  }

  /**
   * Build user behavior profile from historical data
   */
  private async buildUserProfile(userId: number): Promise<UserBehaviorProfile> {
    try {
      // Get login history (last 30 days)
      const { data: loginHistory } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .eq('target_id', userId.toString())
        .like('action', '%LOGIN%')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(100);

      // Get transaction history (last 30 days)
      const { data: transactionHistory } = await supabase
        .from('crypto_payment_transactions')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(100);

      // Analyze patterns
      const commonLoginTimes = this.extractCommonTimes(loginHistory || []);
      const commonIpAddresses = this.extractCommonIPs(loginHistory || []);
      const commonDevices = this.extractCommonDevices(loginHistory || []);
      const geolocationPattern = this.extractGeolocationPattern(loginHistory || []);
      
      const avgTransactionAmount = this.calculateAverageAmount(transactionHistory || []);
      const transactionFrequency = this.calculateTransactionFrequency(transactionHistory || []);
      const avgSessionDuration = this.calculateAverageSessionDuration(loginHistory || []);

      return {
        userId,
        avgSessionDuration,
        commonLoginTimes,
        commonIpAddresses,
        avgTransactionAmount,
        transactionFrequency,
        commonDevices,
        geolocationPattern,
        lastUpdated: new Date()
      };

    } catch (error) {
      console.error('❌ Error building user profile:', error);
      return this.getDefaultProfile(userId);
    }
  }

  /**
   * Analyze IP address anomaly
   */
  private analyzeIpAnomaly(ipAddress: string, profile: UserBehaviorProfile): { score: number; factors: string[] } {
    const factors: string[] = [];
    let score = 0;

    if (profile.commonIpAddresses.length > 0) {
      if (!profile.commonIpAddresses.includes(ipAddress)) {
        score += 25;
        factors.push('New IP address');
        
        // Check if IP is from different subnet
        const isNewSubnet = !profile.commonIpAddresses.some(commonIp => 
          this.isSameSubnet(ipAddress, commonIp)
        );
        
        if (isNewSubnet) {
          score += 15;
          factors.push('Different IP subnet');
        }
      }
    }

    return { score, factors };
  }

  /**
   * Analyze time-based anomaly
   */
  private analyzeTimeAnomaly(loginTime: Date, profile: UserBehaviorProfile): { score: number; factors: string[] } {
    const factors: string[] = [];
    let score = 0;

    const hour = loginTime.getHours();
    
    if (profile.commonLoginTimes.length > 0) {
      const isUnusualTime = !profile.commonLoginTimes.some(commonHour => 
        Math.abs(hour - commonHour) <= 2
      );
      
      if (isUnusualTime) {
        score += 20;
        factors.push('Unusual login time');
        
        // Extra penalty for very unusual hours (2-6 AM)
        if (hour >= 2 && hour <= 6) {
          score += 15;
          factors.push('Very unusual login time (2-6 AM)');
        }
      }
    }

    return { score, factors };
  }

  /**
   * Analyze device/user agent anomaly
   */
  private analyzeDeviceAnomaly(userAgent: string, profile: UserBehaviorProfile): { score: number; factors: string[] } {
    const factors: string[] = [];
    let score = 0;

    if (profile.commonDevices.length > 0) {
      const deviceFingerprint = this.extractDeviceFingerprint(userAgent);
      
      if (!profile.commonDevices.includes(deviceFingerprint)) {
        score += 20;
        factors.push('New device/browser');
      }
    }

    return { score, factors };
  }

  /**
   * Analyze geolocation anomaly
   */
  private analyzeGeolocationAnomaly(
    geolocation: { country: string; city: string; lat: number; lon: number },
    profile: UserBehaviorProfile
  ): { score: number; factors: string[] } {
    const factors: string[] = [];
    let score = 0;

    if (profile.geolocationPattern.length > 0) {
      const locationKey = `${geolocation.country}:${geolocation.city}`;
      
      if (!profile.geolocationPattern.includes(locationKey)) {
        score += 30;
        factors.push('New geographic location');
        
        // Check if country is completely new
        const isNewCountry = !profile.geolocationPattern.some(pattern => 
          pattern.startsWith(geolocation.country + ':')
        );
        
        if (isNewCountry) {
          score += 20;
          factors.push('New country');
        }
      }
    }

    return { score, factors };
  }

  /**
   * Analyze login velocity (rapid successive logins)
   */
  private async analyzeLoginVelocity(userId: number): Promise<{ score: number; factors: string[] }> {
    try {
      const factors: string[] = [];
      let score = 0;

      // Check for logins in the last 5 minutes
      const { data: recentLogins } = await supabase
        .from('admin_audit_logs')
        .select('created_at')
        .eq('target_id', userId.toString())
        .like('action', '%LOGIN%')
        .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString());

      if (recentLogins && recentLogins.length > 3) {
        score += 25;
        factors.push('Rapid successive logins');
      }

      return { score, factors };

    } catch (error) {
      console.error('❌ Login velocity analysis error:', error);
      return { score: 0, factors: [] };
    }
  }

  /**
   * Calculate severity based on score
   */
  private calculateSeverity(score: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (score >= this.ANOMALY_THRESHOLDS.CRITICAL) return 'CRITICAL';
    if (score >= this.ANOMALY_THRESHOLDS.HIGH) return 'HIGH';
    if (score >= this.ANOMALY_THRESHOLDS.MEDIUM) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Helper methods for profile building
   */
  private extractCommonTimes(loginHistory: any[]): number[] {
    const hours = loginHistory.map(log => new Date(log.created_at).getHours());
    const hourCounts = hours.reduce((acc, hour) => {
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    return Object.entries(hourCounts)
      .filter(([_, count]) => count >= 2)
      .map(([hour, _]) => parseInt(hour));
  }

  private extractCommonIPs(loginHistory: any[]): string[] {
    const ips = loginHistory
      .map(log => log.metadata?.ipAddress)
      .filter(ip => ip);
    
    const ipCounts = ips.reduce((acc, ip) => {
      acc[ip] = (acc[ip] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(ipCounts)
      .filter(([_, count]) => count >= 2)
      .map(([ip, _]) => ip);
  }

  private extractCommonDevices(loginHistory: any[]): string[] {
    const devices = loginHistory
      .map(log => this.extractDeviceFingerprint(log.metadata?.userAgent || ''))
      .filter(device => device);

    const deviceCounts = devices.reduce((acc, device) => {
      acc[device] = (acc[device] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(deviceCounts)
      .filter(([_, count]) => count >= 2)
      .map(([device, _]) => device);
  }

  private extractGeolocationPattern(loginHistory: any[]): string[] {
    const locations = loginHistory
      .map(log => {
        const geo = log.metadata?.geolocation;
        return geo ? `${geo.country}:${geo.city}` : null;
      })
      .filter(location => location);

    return [...new Set(locations)];
  }

  private calculateAverageAmount(transactionHistory: any[]): number {
    if (transactionHistory.length === 0) return 0;
    const total = transactionHistory.reduce((sum, tx) => sum + (parseFloat(tx.amount) || 0), 0);
    return total / transactionHistory.length;
  }

  private calculateTransactionFrequency(transactionHistory: any[]): number {
    if (transactionHistory.length === 0) return 0;
    const days = 30; // Last 30 days
    return transactionHistory.length / days;
  }

  private calculateAverageSessionDuration(loginHistory: any[]): number {
    // Simplified calculation - would need session end times for accuracy
    return 30 * 60; // Default 30 minutes
  }

  private extractDeviceFingerprint(userAgent: string): string {
    // Extract browser and OS info
    const browser = userAgent.match(/(Chrome|Firefox|Safari|Edge|Opera)\/[\d.]+/)?.[0] || 'Unknown';
    const os = userAgent.match(/(Windows|Mac|Linux|Android|iOS)/)?.[0] || 'Unknown';
    return `${browser}:${os}`;
  }

  private isSameSubnet(ip1: string, ip2: string): boolean {
    // Simplified subnet check (first 3 octets)
    const subnet1 = ip1.split('.').slice(0, 3).join('.');
    const subnet2 = ip2.split('.').slice(0, 3).join('.');
    return subnet1 === subnet2;
  }

  private getDefaultProfile(userId: number): UserBehaviorProfile {
    return {
      userId,
      avgSessionDuration: 30 * 60,
      commonLoginTimes: [],
      commonIpAddresses: [],
      avgTransactionAmount: 0,
      transactionFrequency: 0,
      commonDevices: [],
      geolocationPattern: [],
      lastUpdated: new Date()
    };
  }

  /**
   * Additional analysis methods
   */
  private analyzeAmountAnomaly(amount: number, profile: UserBehaviorProfile): { score: number; factors: string[] } {
    const factors: string[] = [];
    let score = 0;

    if (profile.avgTransactionAmount > 0) {
      const ratio = amount / profile.avgTransactionAmount;
      
      if (ratio > 5) {
        score += 40;
        factors.push('Transaction amount 5x higher than average');
      } else if (ratio > 3) {
        score += 25;
        factors.push('Transaction amount 3x higher than average');
      } else if (ratio > 2) {
        score += 15;
        factors.push('Transaction amount 2x higher than average');
      }
    }

    return { score, factors };
  }

  private async analyzeTransactionFrequency(userId: number): Promise<{ score: number; factors: string[] }> {
    try {
      const factors: string[] = [];
      let score = 0;

      // Check transactions in last hour
      const { data: recentTransactions } = await supabase
        .from('crypto_payment_transactions')
        .select('created_at')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString());

      if (recentTransactions && recentTransactions.length > 3) {
        score += 30;
        factors.push('High transaction frequency');
      }

      return { score, factors };

    } catch (error) {
      console.error('❌ Transaction frequency analysis error:', error);
      return { score: 0, factors: [] };
    }
  }

  private analyzeTransactionTimeAnomaly(transactionTime: Date, profile: UserBehaviorProfile): { score: number; factors: string[] } {
    // Similar to login time analysis but for transactions
    return this.analyzeTimeAnomaly(transactionTime, profile);
  }

  private async analyzeTransactionVelocity(userId: number, amount: number): Promise<{ score: number; factors: string[] }> {
    try {
      const factors: string[] = [];
      let score = 0;

      // Check for rapid transactions
      const { data: recentTransactions } = await supabase
        .from('crypto_payment_transactions')
        .select('amount, created_at')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 10 * 60 * 1000).toISOString());

      if (recentTransactions && recentTransactions.length > 1) {
        score += 20;
        factors.push('Rapid transaction velocity');

        // Check for similar amounts (possible automation)
        const similarAmounts = recentTransactions.filter(tx => 
          Math.abs(parseFloat(tx.amount) - amount) < amount * 0.1
        );

        if (similarAmounts.length > 1) {
          score += 15;
          factors.push('Similar transaction amounts (possible automation)');
        }
      }

      return { score, factors };

    } catch (error) {
      console.error('❌ Transaction velocity analysis error:', error);
      return { score: 0, factors: [] };
    }
  }

  /**
   * Log anomaly to database
   */
  private async logAnomaly(
    userId: number,
    anomalyType: string,
    score: number,
    severity: string,
    factors: string[],
    metadata: any
  ): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'anomaly_detection_system',
          action: `ANOMALY_DETECTED_${anomalyType}`,
          target_type: 'security_anomaly',
          target_id: userId.toString(),
          metadata: {
            anomalyType,
            score,
            severity,
            factors,
            confidence: metadata.confidence || 0,
            ...metadata,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log anomaly:', error);
    }
  }
}

// Create singleton instance
export const anomalyDetection = new AnomalyDetectionEngine();

export default anomalyDetection;
