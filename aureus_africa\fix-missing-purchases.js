import { createClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';
import dotenv from 'dotenv';
dotenv.config();

// Initialize Supabase client with service role key
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_SERVICE_ROLE_KEY
);

async function fixMissingSharePurchases() {
  console.log('🔧 Starting to fix missing share purchases...\n');
  
  try {
    // Get current phase for pricing
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();
    
    if (phaseError || !currentPhase) {
      throw new Error('Failed to get current phase');
    }
    
    console.log(`📊 Current Phase: ${currentPhase.phase_name} - $${currentPhase.price_per_share}/share\n`);
    
    // Get all approved payments without share purchases
    const { data: payments, error: paymentsError } = await supabase
      .from('crypto_payment_transactions')
      .select(`
        id,
        user_id,
        amount,
        currency,
        created_at,
        investment_id,
        users!inner(username, full_name)
      `)
      .eq('status', 'approved')
      .order('created_at', { ascending: false });

    if (paymentsError) {
      console.error('❌ Error fetching payments:', paymentsError);
      return;
    }

    console.log(`✅ Found ${payments.length} approved payments\n`);

    let fixedCount = 0;
    let skippedCount = 0;

    for (const payment of payments) {
      console.log(`\n🔍 Processing payment ${payment.id} for user ${payment.users.username} ($${payment.amount})`);
      
      let existingPurchase = null;
      
      // Check if this payment already has a corresponding share purchase
      if (payment.investment_id) {
        // Check aureus_share_purchases
        const { data: sp1, error: e1 } = await supabase
          .from('aureus_share_purchases')
          .select('*')
          .eq('id', payment.investment_id)
          .single();
        
        if (!e1 && sp1) {
          existingPurchase = sp1;
          console.log(`   ✅ Share purchase already exists in aureus_share_purchases`);
          skippedCount++;
          continue;
        }
        
        // Check aureus_investments
        const { data: sp2, error: e2 } = await supabase
          .from('aureus_investments')
          .select('*')
          .eq('id', payment.investment_id)
          .single();
        
        if (!e2 && sp2) {
          existingPurchase = sp2;
          console.log(`   ✅ Investment record already exists in aureus_investments`);
          skippedCount++;
          continue;
        }
      }
      
      // Calculate shares based on payment amount and current phase price
      const sharesPurchased = Math.floor(payment.amount / currentPhase.price_per_share);
      const totalAmount = sharesPurchased * currentPhase.price_per_share;
      
      console.log(`   💰 Creating purchase: ${sharesPurchased} shares at $${currentPhase.price_per_share} each = $${totalAmount}`);
      
      // Create new share purchase record
      const newPurchaseId = randomUUID();
      const { data: newPurchase, error: createError } = await supabase
        .from('aureus_share_purchases')
        .insert({
          id: newPurchaseId,
          user_id: payment.user_id,
          package_name: `${currentPhase.phase_name} Package`,
          shares_purchased: sharesPurchased,
          total_amount: totalAmount,
          commission_used: 0,
          remaining_payment: 0,
          payment_method: 'crypto',
          status: 'active',
          created_at: payment.created_at
        })
        .select()
        .single();

      if (createError) {
        console.error(`   ❌ Error creating share purchase:`, createError);
        continue;
      }

      console.log(`   ✅ Created share purchase ${newPurchase.id}`);

      // Update the payment to link to the new purchase
      const { error: updateError } = await supabase
        .from('crypto_payment_transactions')
        .update({ investment_id: newPurchaseId })
        .eq('id', payment.id);

      if (updateError) {
        console.error(`   ⚠️ Warning: Could not link payment to purchase:`, updateError);
      } else {
        console.log(`   🔗 Linked payment to purchase`);
      }

      fixedCount++;
    }

    console.log(`\n✅ Fix complete!`);
    console.log(`   📊 Fixed: ${fixedCount} purchases`);
    console.log(`   ⏭️ Skipped: ${skippedCount} (already existed)`);
    console.log(`   📈 Total: ${payments.length} payments processed`);

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixMissingSharePurchases().then(() => {
  console.log('\n🎉 All done!');
  process.exit(0);
}).catch(console.error);
