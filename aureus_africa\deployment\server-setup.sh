#!/bin/bash

# Aureus Alliance Website - Complete Server Setup Script
# Run this script on your Ubuntu server as root

set -e

echo "🚀 Starting Aureus Alliance Website Server Setup..."

# Update system
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# Install Node.js 20 LTS
echo "📦 Installing Node.js 20 LTS..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
apt-get install -y nodejs

# Install PM2 for process management
echo "📦 Installing PM2..."
npm install -g pm2

# Install Nginx
echo "📦 Installing Nginx..."
apt install -y nginx

# Install SSL tools
echo "📦 Installing SSL tools..."
apt install -y certbot python3-certbot-nginx

# Create application directory
echo "📁 Creating application directory..."
mkdir -p /var/www/aureus-alliance
chown -R www-data:www-data /var/www/aureus-alliance

# Create logs directory
mkdir -p /var/log/aureus-alliance
chown -R www-data:www-data /var/log/aureus-alliance

echo "✅ Server setup complete!"
echo ""
echo "Next steps:"
echo "1. Upload your application files to /var/www/aureus-alliance"
echo "2. Configure environment variables"
echo "3. Set up Nginx configuration"
echo "4. Start the application with PM2"
