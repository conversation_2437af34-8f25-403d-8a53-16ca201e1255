-- User Payment Methods Table for One-Click Purchases
-- This table stores saved payment methods for streamlined share purchases

CREATE TABLE IF NOT EXISTS user_payment_methods (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  method_type VARCHAR(20) NOT NULL CHECK (method_type IN ('crypto', 'bank_transfer')),
  method_name VARCHAR(100) NOT NULL,
  
  -- Crypto payment method fields
  network_id VARCHAR(50), -- BSC, POL, TRON
  network_name VARCHAR(100),
  network_technical VARCHAR(50), -- BEP-20, ERC-20, TRC-20
  wallet_address VARCHAR(255),
  
  -- Bank transfer fields (encrypted)
  bank_name VARCHAR(100),
  account_holder VARCHAR(100),
  account_number_encrypted TEXT,
  routing_number_encrypted TEXT,
  swift_code VARCHAR(20),
  
  -- Settings
  is_default BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_used_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_payment_methods_user_id ON user_payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_user_payment_methods_active ON user_payment_methods(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_user_payment_methods_default ON user_payment_methods(user_id, is_default);

-- RLS Policies
ALTER TABLE user_payment_methods ENABLE ROW LEVEL SECURITY;

-- Users can only access their own payment methods
CREATE POLICY "Users can view own payment methods" ON user_payment_methods
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert own payment methods" ON user_payment_methods
  FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update own payment methods" ON user_payment_methods
  FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete own payment methods" ON user_payment_methods
  FOR DELETE USING (auth.uid()::text = user_id::text);

-- Function to ensure only one default payment method per user
CREATE OR REPLACE FUNCTION ensure_single_default_payment_method()
RETURNS TRIGGER AS $$
BEGIN
  -- If setting this method as default, unset all other defaults for this user
  IF NEW.is_default = TRUE THEN
    UPDATE user_payment_methods 
    SET is_default = FALSE, updated_at = NOW()
    WHERE user_id = NEW.user_id 
      AND id != NEW.id 
      AND is_default = TRUE;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to maintain single default payment method
CREATE TRIGGER trigger_ensure_single_default_payment_method
  BEFORE INSERT OR UPDATE ON user_payment_methods
  FOR EACH ROW
  EXECUTE FUNCTION ensure_single_default_payment_method();

-- Function to update last_used_at when payment method is used
CREATE OR REPLACE FUNCTION update_payment_method_usage(method_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_payment_methods 
  SET last_used_at = NOW(), updated_at = NOW()
  WHERE id = method_id;
END;
$$ LANGUAGE plpgsql;

-- Sample data for testing (remove in production)
-- INSERT INTO user_payment_methods (user_id, method_type, method_name, network_id, network_name, network_technical, is_default) VALUES
-- (1, 'crypto', 'My BSC Wallet', 'BSC', 'Binance Smart Chain', 'BEP-20', true),
-- (1, 'crypto', 'Polygon Wallet', 'POL', 'Polygon Network', 'ERC-20', false);

COMMENT ON TABLE user_payment_methods IS 'Stores saved payment methods for one-click share purchases';
COMMENT ON COLUMN user_payment_methods.method_type IS 'Type of payment method: crypto or bank_transfer';
COMMENT ON COLUMN user_payment_methods.is_default IS 'Whether this is the users default payment method';
COMMENT ON COLUMN user_payment_methods.account_number_encrypted IS 'Encrypted bank account number for security';
COMMENT ON COLUMN user_payment_methods.routing_number_encrypted IS 'Encrypted routing number for security';
