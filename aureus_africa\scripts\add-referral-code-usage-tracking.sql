-- =====================================================
-- Referral Code Usage Tracking Enhancement
-- =====================================================
-- This script adds comprehensive referral code usage tracking
-- to monitor which referral codes are being used most frequently

-- 1. Create referral_code_usage table for detailed tracking
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'referral_code_usage'
    ) THEN
        CREATE TABLE public.referral_code_usage (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            referral_code VARCHAR(255) NOT NULL,
            campaign_source VARCHAR(100),
            ip_address INET,
            user_agent TEXT,
            referer_url TEXT,
            used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            converted BOOLEAN DEFAULT FALSE,
            converted_at TIMESTAMP WITH TIME ZONE,
            user_id INTEGER REFERENCES public.users(id) ON DELETE SET NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Add indexes for better query performance
        CREATE INDEX IF NOT EXISTS idx_referral_code_usage_code 
        ON public.referral_code_usage(referral_code);
        
        CREATE INDEX IF NOT EXISTS idx_referral_code_usage_campaign_source 
        ON public.referral_code_usage(campaign_source);
        
        CREATE INDEX IF NOT EXISTS idx_referral_code_usage_used_at 
        ON public.referral_code_usage(used_at);
        
        CREATE INDEX IF NOT EXISTS idx_referral_code_usage_converted 
        ON public.referral_code_usage(converted);
        
        CREATE INDEX IF NOT EXISTS idx_referral_code_usage_ip_address 
        ON public.referral_code_usage(ip_address);

        RAISE NOTICE '✅ Created referral_code_usage table with indexes';
    ELSE
        RAISE NOTICE '✅ referral_code_usage table already exists';
    END IF;
END $$;

-- 2. Create a view for referral code performance analytics
CREATE OR REPLACE VIEW public.referral_code_performance AS
SELECT 
    rcu.referral_code,
    rcu.campaign_source,
    COUNT(*) as total_uses,
    COUNT(DISTINCT rcu.ip_address) as unique_visitors,
    COUNT(CASE WHEN rcu.converted = true THEN 1 END) as conversions,
    ROUND(
        (COUNT(CASE WHEN rcu.converted = true THEN 1 END)::DECIMAL / COUNT(*)) * 100, 
        2
    ) as conversion_rate,
    MIN(rcu.used_at) as first_used,
    MAX(rcu.used_at) as last_used,
    DATE_TRUNC('day', rcu.used_at) as usage_date
FROM public.referral_code_usage rcu
GROUP BY rcu.referral_code, rcu.campaign_source, DATE_TRUNC('day', rcu.used_at)
ORDER BY total_uses DESC;

-- 3. Create a function to get top performing referral codes
CREATE OR REPLACE FUNCTION public.get_top_referral_codes(
    limit_count INTEGER DEFAULT 10,
    date_range_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    referral_code VARCHAR(255),
    campaign_source VARCHAR(100),
    total_uses BIGINT,
    unique_visitors BIGINT,
    conversions BIGINT,
    conversion_rate NUMERIC,
    first_used TIMESTAMP WITH TIME ZONE,
    last_used TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rcu.referral_code,
        rcu.campaign_source,
        COUNT(*) as total_uses,
        COUNT(DISTINCT rcu.ip_address) as unique_visitors,
        COUNT(CASE WHEN rcu.converted = true THEN 1 END) as conversions,
        ROUND(
            (COUNT(CASE WHEN rcu.converted = true THEN 1 END)::DECIMAL / COUNT(*)) * 100, 
            2
        ) as conversion_rate,
        MIN(rcu.used_at) as first_used,
        MAX(rcu.used_at) as last_used
    FROM public.referral_code_usage rcu
    WHERE rcu.used_at >= NOW() - INTERVAL '1 day' * date_range_days
    GROUP BY rcu.referral_code, rcu.campaign_source
    ORDER BY total_uses DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 4. Create a function to track referral code usage
CREATE OR REPLACE FUNCTION public.track_referral_usage(
    p_referral_code VARCHAR(255),
    p_campaign_source VARCHAR(100) DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_referer_url TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    usage_id UUID;
BEGIN
    INSERT INTO public.referral_code_usage (
        referral_code,
        campaign_source,
        ip_address,
        user_agent,
        referer_url,
        used_at
    ) VALUES (
        p_referral_code,
        p_campaign_source,
        p_ip_address,
        p_user_agent,
        p_referer_url,
        NOW()
    ) RETURNING id INTO usage_id;
    
    RETURN usage_id;
END;
$$ LANGUAGE plpgsql;

-- 5. Add campaign_source column to referrals table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'referrals' 
        AND column_name = 'campaign_source'
    ) THEN
        ALTER TABLE public.referrals 
        ADD COLUMN campaign_source VARCHAR(100);
        
        CREATE INDEX IF NOT EXISTS idx_referrals_campaign_source 
        ON public.referrals(campaign_source);
        
        RAISE NOTICE '✅ Added campaign_source column to referrals table';
    ELSE
        RAISE NOTICE '✅ campaign_source column already exists in referrals table';
    END IF;
END $$;

-- 6. Create a trigger to update referral analytics when usage is tracked
CREATE OR REPLACE FUNCTION public.update_referral_analytics_on_usage()
RETURNS TRIGGER AS $$
BEGIN
    -- Update or insert daily analytics record
    INSERT INTO public.referral_analytics (
        referrer_id,
        campaign_name,
        campaign_source,
        clicks,
        date_tracked
    ) 
    SELECT 
        u.id,
        COALESCE(NEW.campaign_source, 'direct'),
        NEW.campaign_source,
        1,
        CURRENT_DATE
    FROM public.users u
    WHERE u.username = NEW.referral_code
    ON CONFLICT (referrer_id, campaign_name, date_tracked)
    DO UPDATE SET 
        clicks = public.referral_analytics.clicks + 1,
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trigger_update_analytics_on_usage ON public.referral_code_usage;
CREATE TRIGGER trigger_update_analytics_on_usage
    AFTER INSERT ON public.referral_code_usage
    FOR EACH ROW
    EXECUTE FUNCTION public.update_referral_analytics_on_usage();

-- 7. Verify the setup
SELECT 
    'referral_code_usage' as table_name,
    COUNT(*) as record_count
FROM public.referral_code_usage
UNION ALL
SELECT 
    'referral_analytics' as table_name,
    COUNT(*) as record_count
FROM public.referral_analytics;

-- 8. Test the tracking function
SELECT public.track_referral_usage(
    'test_user',
    'facebook',
    '192.168.1.1'::INET,
    'Mozilla/5.0 Test Browser',
    'https://facebook.com'
) as test_usage_id;

RAISE NOTICE '✅ Referral code usage tracking system setup complete!';
RAISE NOTICE '📊 Use get_top_referral_codes() function to get performance data';
RAISE NOTICE '🔍 Use referral_code_performance view for detailed analytics';
