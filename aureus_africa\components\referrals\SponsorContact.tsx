import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface SponsorContactProps {
  userId: number;
  className?: string;
}

interface SponsorInfo {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  telegram_username?: string;
  phone?: string;
  total_referrals: number;
  total_earnings: number;
  created_at: string;
  is_active: boolean;
}

interface ContactMessage {
  id: string;
  sender_id: number;
  recipient_id: number;
  subject: string;
  message: string;
  status: 'sent' | 'read' | 'replied';
  created_at: string;
}

export const SponsorContact: React.FC<SponsorContactProps> = ({ userId, className = '' }) => {
  const [sponsor, setSponsor] = useState<SponsorInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [showContactForm, setShowContactForm] = useState(false);
  const [contactSubject, setContactSubject] = useState('');
  const [contactMessage, setContactMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [recentMessages, setRecentMessages] = useState<ContactMessage[]>([]);

  useEffect(() => {
    loadSponsorInfo();
    loadRecentMessages();
  }, [userId]);

  const loadSponsorInfo = async () => {
    try {
      // Get user's referral relationship to find sponsor
      const { data: referralData, error: referralError } = await supabase
        .from('referrals')
        .select('referrer_id')
        .eq('referred_id', userId)
        .eq('status', 'active')
        .single();

      if (referralError || !referralData) {
        console.log('No active sponsor found');
        setLoading(false);
        return;
      }

      // Get sponsor details
      const { data: sponsorData, error: sponsorError } = await supabase
        .from('users')
        .select(`
          id,
          username,
          first_name,
          last_name,
          email,
          telegram_username,
          phone,
          total_referrals,
          total_earnings,
          created_at,
          is_active
        `)
        .eq('id', referralData.referrer_id)
        .single();

      if (sponsorError) throw sponsorError;

      setSponsor(sponsorData);
    } catch (error) {
      console.error('Error loading sponsor info:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRecentMessages = async () => {
    try {
      const { data, error } = await supabase
        .from('user_messages')
        .select('*')
        .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      setRecentMessages(data || []);
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const sendMessage = async () => {
    if (!sponsor || !contactSubject.trim() || !contactMessage.trim()) return;

    setSending(true);
    try {
      const { error } = await supabase
        .from('user_messages')
        .insert({
          sender_id: userId,
          recipient_id: sponsor.id,
          subject: contactSubject.trim(),
          message: contactMessage.trim(),
          status: 'sent',
          created_at: new Date().toISOString()
        });

      if (error) throw error;

      // Reset form
      setContactSubject('');
      setContactMessage('');
      setShowContactForm(false);

      // Reload messages
      await loadRecentMessages();

      alert('Message sent successfully!');
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    } finally {
      setSending(false);
    }
  };

  const getContactMethods = () => {
    const methods = [];
    
    if (sponsor?.email) {
      methods.push({
        type: 'email',
        label: 'Email',
        value: sponsor.email,
        icon: '📧',
        action: () => window.open(`mailto:${sponsor.email}`)
      });
    }
    
    if (sponsor?.telegram_username) {
      methods.push({
        type: 'telegram',
        label: 'Telegram',
        value: `@${sponsor.telegram_username}`,
        icon: '✈️',
        action: () => window.open(`https://t.me/${sponsor.telegram_username}`)
      });
    }
    
    if (sponsor?.phone) {
      methods.push({
        type: 'phone',
        label: 'Phone',
        value: sponsor.phone,
        icon: '📱',
        action: () => window.open(`tel:${sponsor.phone}`)
      });
    }

    return methods;
  };

  if (loading) {
    return (
      <div className={`${className} text-center p-8`}>
        <div className="text-2xl mb-4">👥</div>
        <div className="text-gray-400">Loading sponsor information...</div>
      </div>
    );
  }

  if (!sponsor) {
    return (
      <div className={`${className} text-center p-8`}>
        <div className="text-4xl mb-4">🤝</div>
        <h3 className="text-white text-xl mb-4">No Sponsor Assigned</h3>
        <p className="text-gray-400 mb-6">
          You don't currently have an active sponsor. Contact support if you believe this is an error.
        </p>
        <button
          onClick={() => window.open('mailto:<EMAIL>')}
          className="bg-yellow-500 hover:bg-yellow-600 text-black px-6 py-2 rounded-lg font-semibold transition-colors"
        >
          Contact Support
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Sponsor Profile Card */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 mb-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center text-2xl font-bold text-black">
              {sponsor.first_name?.[0] || sponsor.username[0].toUpperCase()}
            </div>
            <div>
              <h2 className="text-white text-xl font-semibold">
                {sponsor.first_name} {sponsor.last_name}
              </h2>
              <p className="text-gray-400">@{sponsor.username}</p>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`w-2 h-2 rounded-full ${sponsor.is_active ? 'bg-green-500' : 'bg-gray-500'}`}></span>
                <span className="text-sm text-gray-400">
                  {sponsor.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-400">Your Sponsor</div>
            <div className="text-xs text-gray-500">
              Member since {new Date(sponsor.created_at).toLocaleDateString()}
            </div>
          </div>
        </div>

        {/* Sponsor Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-400">{sponsor.total_referrals}</div>
            <div className="text-sm text-gray-400">Total Referrals</div>
          </div>
          <div className="bg-gray-700 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400">${sponsor.total_earnings.toFixed(2)}</div>
            <div className="text-sm text-gray-400">Total Earnings</div>
          </div>
        </div>

        {/* Contact Methods */}
        <div className="mb-6">
          <h3 className="text-white font-semibold mb-3">📞 Contact Methods</h3>
          <div className="grid grid-cols-1 gap-2">
            {getContactMethods().map((method, index) => (
              <button
                key={index}
                onClick={method.action}
                className="flex items-center justify-between p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors text-left"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-xl">{method.icon}</span>
                  <div>
                    <div className="text-white font-medium">{method.label}</div>
                    <div className="text-gray-400 text-sm">{method.value}</div>
                  </div>
                </div>
                <span className="text-gray-400">→</span>
              </button>
            ))}
          </div>
        </div>

        {/* Internal Message System */}
        <div className="border-t border-gray-700 pt-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-white font-semibold">💬 Internal Messages</h3>
            <button
              onClick={() => setShowContactForm(!showContactForm)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              {showContactForm ? 'Cancel' : 'Send Message'}
            </button>
          </div>

          {/* Message Form */}
          {showContactForm && (
            <div className="bg-gray-700 rounded-lg p-4 mb-4">
              <div className="mb-3">
                <label className="block text-sm font-medium text-gray-300 mb-1">Subject</label>
                <input
                  type="text"
                  value={contactSubject}
                  onChange={(e) => setContactSubject(e.target.value)}
                  placeholder="Enter message subject..."
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-1">Message</label>
                <textarea
                  value={contactMessage}
                  onChange={(e) => setContactMessage(e.target.value)}
                  placeholder="Type your message here..."
                  rows={4}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={sendMessage}
                  disabled={sending || !contactSubject.trim() || !contactMessage.trim()}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  {sending ? 'Sending...' : 'Send Message'}
                </button>
                <button
                  onClick={() => setShowContactForm(false)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}

          {/* Recent Messages */}
          {recentMessages.length > 0 && (
            <div>
              <h4 className="text-gray-300 text-sm font-medium mb-2">Recent Messages</h4>
              <div className="space-y-2">
                {recentMessages.slice(0, 3).map((message) => (
                  <div key={message.id} className="bg-gray-700 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-white text-sm font-medium">{message.subject}</span>
                      <span className="text-xs text-gray-400">
                        {new Date(message.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-gray-300 text-sm line-clamp-2">{message.message}</p>
                    <div className="flex items-center justify-between mt-2">
                      <span className={`text-xs px-2 py-1 rounded ${
                        message.status === 'read' ? 'bg-green-600 text-white' :
                        message.status === 'replied' ? 'bg-blue-600 text-white' :
                        'bg-gray-600 text-gray-300'
                      }`}>
                        {message.status}
                      </span>
                      <span className="text-xs text-gray-400">
                        {message.sender_id === userId ? 'Sent by you' : 'Received'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
        <h3 className="text-white font-semibold mb-3">🚀 Quick Actions</h3>
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => setShowContactForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg text-sm font-medium transition-colors"
          >
            💬 Ask Question
          </button>
          <button
            onClick={() => window.open(`https://t.me/${sponsor.telegram_username || 'AureusAllianceBot'}`)}
            className="bg-green-600 hover:bg-green-700 text-white p-3 rounded-lg text-sm font-medium transition-colors"
          >
            ✈️ Telegram Chat
          </button>
          <button
            onClick={() => window.open(`mailto:${sponsor.email}?subject=Referral Support Request`)}
            className="bg-yellow-600 hover:bg-yellow-700 text-white p-3 rounded-lg text-sm font-medium transition-colors"
          >
            📧 Email Support
          </button>
          <button
            onClick={() => alert('Feature coming soon!')}
            className="bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-lg text-sm font-medium transition-colors"
          >
            📅 Schedule Call
          </button>
        </div>
      </div>
    </div>
  );
};
