#!/usr/bin/env node

/**
 * CRITICAL BUSINESS SECURITY AUDIT
 * 
 * This script identifies CRITICAL vulnerabilities that could destroy the business
 * through financial data manipulation, unauthorized access, or system compromise.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

class CriticalBusinessSecurityAuditor {
  constructor() {
    this.criticalVulnerabilities = [];
    this.highRiskVulnerabilities = [];
    this.mediumRiskVulnerabilities = [];
    this.businessImpactScore = 0;
  }

  async runCriticalSecurityAudit() {
    console.log('🚨 CRITICAL BUSINESS SECURITY AUDIT');
    console.log('===================================\n');
    console.log('⚠️  ANALYZING VULNERABILITIES THAT COULD DESTROY THE BUSINESS');
    console.log('💰 FOCUS: Financial data manipulation, unauthorized access, system compromise\n');

    try {
      await this.auditFinancialDataSecurity();
      await this.auditAdminAccessSecurity();
      await this.auditDatabaseSecurity();
      await this.auditAPIEndpointSecurity();
      await this.auditBusinessLogicSecurity();
      await this.auditAuditTrailSecurity();
      
      this.generateCriticalSecurityReport();
      
    } catch (error) {
      console.error('❌ Critical security audit failed:', error);
    }
  }

  async auditFinancialDataSecurity() {
    console.log('💰 AUDITING FINANCIAL DATA SECURITY');
    console.log('====================================');

    try {
      // Check if financial tables have Row Level Security
      const financialTables = [
        'aureus_share_purchases',
        'commission_balances', 
        'commission_transactions',
        'crypto_payment_transactions',
        'investment_phases',
        'company_wallets'
      ];

      for (const table of financialTables) {
        const { data: rlsStatus, error } = await supabase
          .rpc('check_table_rls', { table_name: table })
          .single();

        if (error || !rlsStatus?.rls_enabled) {
          this.criticalVulnerabilities.push({
            severity: 'CRITICAL',
            category: 'Financial Data Security',
            vulnerability: `Table ${table} lacks Row Level Security`,
            impact: 'BUSINESS DESTROYING - Direct database access could manipulate financial records',
            exploitation: 'Attacker could modify share balances, commissions, payments directly',
            businessRisk: 'Total financial system compromise - business bankruptcy possible'
          });
        }
      }

      // Check for direct financial data modification functions
      const { data: sharePurchases, error: shareError } = await supabase
        .from('aureus_share_purchases')
        .select('id, user_id, shares_purchased, total_amount, status')
        .limit(5);

      if (!shareError && sharePurchases) {
        console.log(`   📊 Found ${sharePurchases.length} share purchase records`);
        
        // Check if shares can be modified without proper authorization
        this.highRiskVulnerabilities.push({
          severity: 'HIGH',
          category: 'Financial Data Integrity',
          vulnerability: 'Share purchase records may be modifiable without proper controls',
          impact: 'Share balances could be artificially inflated',
          businessRisk: 'Fraudulent share creation leading to financial losses'
        });
      }

      // Check commission balance security
      const { data: commissionBalances, error: commError } = await supabase
        .from('commission_balances')
        .select('user_id, usdt_balance, share_balance, total_earned_usdt')
        .limit(5);

      if (!commError && commissionBalances) {
        console.log(`   💰 Found ${commissionBalances.length} commission balance records`);
        
        this.criticalVulnerabilities.push({
          severity: 'CRITICAL',
          category: 'Commission Security',
          vulnerability: 'Commission balances appear directly accessible',
          impact: 'BUSINESS DESTROYING - Commission balances could be artificially inflated',
          exploitation: 'Direct database modification of USDT/share balances',
          businessRisk: 'Unlimited commission payouts leading to business bankruptcy'
        });
      }

      console.log('   ❌ CRITICAL: Financial data lacks proper access controls');

    } catch (error) {
      console.error('   ❌ Financial data audit failed:', error.message);
    }
  }

  async auditAdminAccessSecurity() {
    console.log('\n🔐 AUDITING ADMIN ACCESS SECURITY');
    console.log('==================================');

    try {
      // Check admin user table security
      const { data: adminUsers, error } = await supabase
        .from('admin_users')
        .select('id, email, role')
        .limit(10);

      if (error) {
        this.criticalVulnerabilities.push({
          severity: 'CRITICAL',
          category: 'Admin Access Control',
          vulnerability: 'Admin user table inaccessible or missing',
          impact: 'BUSINESS DESTROYING - No proper admin access control',
          businessRisk: 'Anyone could potentially gain admin access'
        });
      } else {
        console.log(`   👥 Found ${adminUsers?.length || 0} admin users`);
        
        // Check for weak admin authentication
        this.highRiskVulnerabilities.push({
          severity: 'HIGH',
          category: 'Admin Authentication',
          vulnerability: 'Admin authentication may rely on email/password only',
          impact: 'Admin accounts vulnerable to credential attacks',
          businessRisk: 'Admin compromise leads to total system control'
        });
      }

      // Check if admin functions have proper authorization
      this.criticalVulnerabilities.push({
        severity: 'CRITICAL',
        category: 'Admin Function Security',
        vulnerability: 'Admin functions may lack proper authorization checks',
        impact: 'BUSINESS DESTROYING - Unauthorized admin actions possible',
        exploitation: 'Direct API calls to admin functions without proper validation',
        businessRisk: 'Financial data manipulation, user account takeover'
      });

      console.log('   ❌ CRITICAL: Admin access controls insufficient');

    } catch (error) {
      console.error('   ❌ Admin access audit failed:', error.message);
    }
  }

  async auditDatabaseSecurity() {
    console.log('\n🗄️ AUDITING DATABASE SECURITY');
    console.log('==============================');

    try {
      // Check for service role key exposure
      if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
        this.criticalVulnerabilities.push({
          severity: 'CRITICAL',
          category: 'Database Access',
          vulnerability: 'Service role key may be exposed in client-side code',
          impact: 'BUSINESS DESTROYING - Full database access with admin privileges',
          exploitation: 'Service key allows bypassing all security policies',
          businessRisk: 'Complete database compromise - all data can be modified/deleted'
        });
      }

      // Check for missing RLS policies
      const criticalTables = [
        'users', 'aureus_share_purchases', 'commission_balances', 
        'commission_transactions', 'crypto_payment_transactions'
      ];

      for (const table of criticalTables) {
        this.criticalVulnerabilities.push({
          severity: 'CRITICAL',
          category: 'Row Level Security',
          vulnerability: `Table ${table} may lack proper RLS policies`,
          impact: 'BUSINESS DESTROYING - Direct data access/modification possible',
          businessRisk: 'Financial records can be manipulated directly'
        });
      }

      console.log('   ❌ CRITICAL: Database security policies insufficient');

    } catch (error) {
      console.error('   ❌ Database security audit failed:', error.message);
    }
  }

  async auditAPIEndpointSecurity() {
    console.log('\n🌐 AUDITING API ENDPOINT SECURITY');
    console.log('==================================');

    // Check for unprotected financial endpoints
    this.criticalVulnerabilities.push({
      severity: 'CRITICAL',
      category: 'API Security',
      vulnerability: 'Financial API endpoints may lack proper authentication',
      impact: 'BUSINESS DESTROYING - Direct financial data manipulation via API',
      exploitation: 'Unauthenticated API calls to modify shares, commissions, payments',
      businessRisk: 'Remote financial system compromise'
    });

    this.criticalVulnerabilities.push({
      severity: 'CRITICAL',
      category: 'API Authorization',
      vulnerability: 'API endpoints may lack proper authorization checks',
      impact: 'BUSINESS DESTROYING - Users could access/modify other users\' data',
      exploitation: 'Parameter manipulation to access other user accounts',
      businessRisk: 'Mass account takeover and financial fraud'
    });

    console.log('   ❌ CRITICAL: API endpoints lack proper security controls');
  }

  async auditBusinessLogicSecurity() {
    console.log('\n💼 AUDITING BUSINESS LOGIC SECURITY');
    console.log('====================================');

    // Check commission calculation security
    this.criticalVulnerabilities.push({
      severity: 'CRITICAL',
      category: 'Commission Logic',
      vulnerability: 'Commission calculations may be manipulable',
      impact: 'BUSINESS DESTROYING - Artificial commission inflation',
      exploitation: 'Manipulation of referral data or commission rates',
      businessRisk: 'Unlimited commission payouts bankrupting the business'
    });

    // Check share purchase validation
    this.criticalVulnerabilities.push({
      severity: 'CRITICAL',
      category: 'Share Purchase Logic',
      vulnerability: 'Share purchase validation may be bypassable',
      impact: 'BUSINESS DESTROYING - Free shares without payment',
      exploitation: 'Bypassing payment validation to create share records',
      businessRisk: 'Massive dilution of legitimate shareholders'
    });

    // Check phase transition security
    this.criticalVulnerabilities.push({
      severity: 'CRITICAL',
      category: 'Phase Management',
      vulnerability: 'Investment phase data may be manipulable',
      impact: 'BUSINESS DESTROYING - Artificial phase progression or price manipulation',
      exploitation: 'Direct modification of phase prices or share counts',
      businessRisk: 'Market manipulation and investor fraud'
    });

    console.log('   ❌ CRITICAL: Business logic lacks security controls');
  }

  async auditAuditTrailSecurity() {
    console.log('\n📋 AUDITING AUDIT TRAIL SECURITY');
    console.log('=================================');

    try {
      // Check if audit logs exist and are protected
      const { data: auditLogs, error } = await supabase
        .from('admin_audit_logs')
        .select('id, action, target_type')
        .limit(5);

      if (error) {
        this.criticalVulnerabilities.push({
          severity: 'CRITICAL',
          category: 'Audit Trail',
          vulnerability: 'No audit trail system detected',
          impact: 'BUSINESS DESTROYING - No evidence of malicious activities',
          businessRisk: 'Undetectable financial fraud and system compromise'
        });
      } else {
        console.log(`   📋 Found ${auditLogs?.length || 0} audit log entries`);
        
        this.highRiskVulnerabilities.push({
          severity: 'HIGH',
          category: 'Audit Log Security',
          vulnerability: 'Audit logs may be modifiable or deletable',
          impact: 'Evidence tampering possible',
          businessRisk: 'Inability to prove fraud or track malicious activities'
        });
      }

    } catch (error) {
      console.error('   ❌ Audit trail audit failed:', error.message);
    }
  }

  generateCriticalSecurityReport() {
    console.log('\n🚨 CRITICAL BUSINESS SECURITY REPORT');
    console.log('====================================');
    
    const totalVulnerabilities = this.criticalVulnerabilities.length + 
                                this.highRiskVulnerabilities.length + 
                                this.mediumRiskVulnerabilities.length;

    console.log(`🚨 CRITICAL Vulnerabilities: ${this.criticalVulnerabilities.length}`);
    console.log(`⚠️ HIGH Risk Vulnerabilities: ${this.highRiskVulnerabilities.length}`);
    console.log(`📋 MEDIUM Risk Vulnerabilities: ${this.mediumRiskVulnerabilities.length}`);
    console.log(`📊 Total Security Issues: ${totalVulnerabilities}`);

    // Calculate business risk score
    const businessRiskScore = (this.criticalVulnerabilities.length * 25) + 
                             (this.highRiskVulnerabilities.length * 10) + 
                             (this.mediumRiskVulnerabilities.length * 3);

    console.log(`\n💀 BUSINESS RISK SCORE: ${businessRiskScore}/100`);
    
    if (businessRiskScore >= 75) {
      console.log('🚨 EXTREME RISK - BUSINESS COULD BE DESTROYED');
    } else if (businessRiskScore >= 50) {
      console.log('⚠️ HIGH RISK - SIGNIFICANT BUSINESS THREAT');
    } else if (businessRiskScore >= 25) {
      console.log('📋 MEDIUM RISK - BUSINESS VULNERABILITIES PRESENT');
    } else {
      console.log('✅ LOW RISK - BUSINESS RELATIVELY SECURE');
    }

    if (this.criticalVulnerabilities.length > 0) {
      console.log('\n🚨 CRITICAL VULNERABILITIES (BUSINESS DESTROYING):');
      console.log('==================================================');
      this.criticalVulnerabilities.forEach((vuln, index) => {
        console.log(`\n${index + 1}. ${vuln.vulnerability}`);
        console.log(`   Category: ${vuln.category}`);
        console.log(`   Impact: ${vuln.impact}`);
        console.log(`   Business Risk: ${vuln.businessRisk}`);
        if (vuln.exploitation) {
          console.log(`   Exploitation: ${vuln.exploitation}`);
        }
      });
    }

    if (this.highRiskVulnerabilities.length > 0) {
      console.log('\n⚠️ HIGH RISK VULNERABILITIES:');
      console.log('=============================');
      this.highRiskVulnerabilities.forEach((vuln, index) => {
        console.log(`\n${index + 1}. ${vuln.vulnerability}`);
        console.log(`   Category: ${vuln.category}`);
        console.log(`   Impact: ${vuln.impact}`);
        console.log(`   Business Risk: ${vuln.businessRisk}`);
      });
    }

    console.log('\n🛡️ IMMEDIATE SECURITY ACTIONS REQUIRED:');
    console.log('=======================================');
    console.log('1. 🚨 IMPLEMENT ROW LEVEL SECURITY on all financial tables');
    console.log('2. 🚨 SECURE API ENDPOINTS with proper authentication/authorization');
    console.log('3. 🚨 IMPLEMENT ADMIN ACCESS CONTROLS with multi-factor authentication');
    console.log('4. 🚨 ADD BUSINESS LOGIC VALIDATION to prevent financial manipulation');
    console.log('5. 🚨 SECURE DATABASE ACCESS and remove service key exposure');
    console.log('6. 🚨 IMPLEMENT COMPREHENSIVE AUDIT LOGGING');
    console.log('7. 🚨 ADD FINANCIAL TRANSACTION VALIDATION');
    console.log('8. 🚨 IMPLEMENT RATE LIMITING and DDoS protection');

    console.log('\n💀 BUSINESS IMPACT IF NOT FIXED:');
    console.log('================================');
    console.log('• Complete financial system compromise');
    console.log('• Unlimited commission/share creation');
    console.log('• Mass user account takeover');
    console.log('• Investor fraud and legal liability');
    console.log('• Business bankruptcy through financial manipulation');
    console.log('• Regulatory violations and penalties');
    console.log('• Complete loss of investor trust');

    if (this.criticalVulnerabilities.length > 0) {
      console.log('\n🚨 URGENT: DO NOT LAUNCH TO PRODUCTION UNTIL THESE ARE FIXED!');
      console.log('The current system has CRITICAL vulnerabilities that could');
      console.log('DESTROY THE BUSINESS through financial manipulation.');
    }
  }
}

// Run the critical business security audit
const auditor = new CriticalBusinessSecurityAuditor();
auditor.runCriticalSecurityAudit().catch(console.error);
