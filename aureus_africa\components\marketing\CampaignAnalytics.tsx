import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface CampaignData {
  id: string;
  campaign_name: string;
  campaign_source: string;
  clicks: number;
  registrations: number;
  conversions: number;
  total_revenue: number;
  date_tracked: string;
  conversion_rate: number;
  revenue_per_click: number;
  roi: number;
}

interface PerformanceMetrics {
  totalClicks: number;
  totalRegistrations: number;
  totalConversions: number;
  totalRevenue: number;
  averageConversionRate: number;
  averageRevenuePerClick: number;
  topPerformingCampaign: string;
  topPerformingPlatform: string;
}

interface CampaignAnalyticsProps {
  userId: number;
  dateRange?: { start: string; end: string };
}

export const CampaignAnalytics: React.FC<CampaignAnalyticsProps> = ({
  userId,
  dateRange
}) => {
  const [campaigns, setCampaigns] = useState<CampaignData[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [chartData, setChartData] = useState<any[]>([]);
  const [showDetailedView, setShowDetailedView] = useState(false);

  const platforms = [
    { id: 'all', name: 'All Platforms', icon: '📊' },
    { id: 'facebook', name: 'Facebook', icon: '📘' },
    { id: 'instagram', name: 'Instagram', icon: '📷' },
    { id: 'twitter', name: 'Twitter/X', icon: '🐦' },
    { id: 'linkedin', name: 'LinkedIn', icon: '💼' },
    { id: 'whatsapp', name: 'WhatsApp', icon: '💬' },
    { id: 'telegram', name: 'Telegram', icon: '✈️' }
  ];

  useEffect(() => {
    loadCampaignData();
  }, [userId, selectedTimeframe, selectedPlatform]);

  const loadCampaignData = async () => {
    setLoading(true);
    try {
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      
      switch (selectedTimeframe) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case 'all':
          startDate.setFullYear(2020); // Far back date
          break;
      }

      // Build query
      let query = supabase
        .from('referral_analytics')
        .select('*')
        .eq('referrer_id', userId)
        .gte('date_tracked', startDate.toISOString().split('T')[0])
        .lte('date_tracked', endDate.toISOString().split('T')[0])
        .order('date_tracked', { ascending: false });

      // Filter by platform if selected
      if (selectedPlatform !== 'all') {
        query = query.eq('campaign_source', selectedPlatform);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Process and enrich the data
      const enrichedData = (data || []).map(item => ({
        ...item,
        conversion_rate: item.clicks > 0 ? (item.conversions / item.clicks) * 100 : 0,
        revenue_per_click: item.clicks > 0 ? item.total_revenue / item.clicks : 0,
        roi: item.total_revenue > 0 ? ((item.total_revenue - (item.clicks * 0.5)) / (item.clicks * 0.5)) * 100 : 0 // Assuming $0.5 cost per click
      }));

      setCampaigns(enrichedData);
      calculateMetrics(enrichedData);
      prepareChartData(enrichedData);

    } catch (error) {
      console.error('Error loading campaign data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateMetrics = (data: CampaignData[]) => {
    const totalClicks = data.reduce((sum, item) => sum + item.clicks, 0);
    const totalRegistrations = data.reduce((sum, item) => sum + item.registrations, 0);
    const totalConversions = data.reduce((sum, item) => sum + item.conversions, 0);
    const totalRevenue = data.reduce((sum, item) => sum + item.total_revenue, 0);

    const averageConversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;
    const averageRevenuePerClick = totalClicks > 0 ? totalRevenue / totalClicks : 0;

    // Find top performing campaign and platform
    const topCampaign = data.reduce((prev, current) => 
      (current.total_revenue > prev.total_revenue) ? current : prev, data[0] || {} as CampaignData);
    
    const platformRevenue = data.reduce((acc, item) => {
      acc[item.campaign_source] = (acc[item.campaign_source] || 0) + item.total_revenue;
      return acc;
    }, {} as Record<string, number>);

    const topPlatform = Object.entries(platformRevenue).reduce((a, b) => 
      platformRevenue[a[0]] > platformRevenue[b[0]] ? a : b, ['', 0])[0];

    setMetrics({
      totalClicks,
      totalRegistrations,
      totalConversions,
      totalRevenue,
      averageConversionRate,
      averageRevenuePerClick,
      topPerformingCampaign: topCampaign?.campaign_name || 'N/A',
      topPerformingPlatform: topPlatform || 'N/A'
    });
  };

  const prepareChartData = (data: CampaignData[]) => {
    // Group data by date for time series chart
    const groupedByDate = data.reduce((acc, item) => {
      const date = item.date_tracked;
      if (!acc[date]) {
        acc[date] = { date, clicks: 0, conversions: 0, revenue: 0 };
      }
      acc[date].clicks += item.clicks;
      acc[date].conversions += item.conversions;
      acc[date].revenue += item.total_revenue;
      return acc;
    }, {} as Record<string, any>);

    const chartData = Object.values(groupedByDate).sort((a: any, b: any) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    setChartData(chartData);
  };

  const exportData = async () => {
    try {
      const csvContent = [
        ['Campaign', 'Platform', 'Date', 'Clicks', 'Registrations', 'Conversions', 'Revenue', 'Conversion Rate', 'Revenue per Click'].join(','),
        ...campaigns.map(campaign => [
          campaign.campaign_name,
          campaign.campaign_source,
          campaign.date_tracked,
          campaign.clicks,
          campaign.registrations,
          campaign.conversions,
          campaign.total_revenue.toFixed(2),
          campaign.conversion_rate.toFixed(2) + '%',
          campaign.revenue_per_click.toFixed(2)
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `campaign-analytics-${selectedTimeframe}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  if (loading) {
    return (
      <div style={{
        backgroundColor: 'rgba(31, 41, 55, 0.9)',
        borderRadius: '12px',
        padding: '24px',
        border: '1px solid #374151',
        textAlign: 'center'
      }}>
        <div style={{ color: '#9ca3af' }}>Loading analytics...</div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151'
    }}>
      {/* Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <div>
          <h3 style={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#f59e0b',
            marginBottom: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            📊 Campaign Analytics
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0 }}>
            Track performance and optimize your marketing campaigns
          </p>
        </div>

        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={() => setShowDetailedView(!showDetailedView)}
            style={{
              padding: '8px 16px',
              backgroundColor: 'rgba(59, 130, 246, 0.2)',
              border: '1px solid #3b82f6',
              borderRadius: '8px',
              color: '#60a5fa',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            {showDetailedView ? 'Summary' : 'Detailed'} View
          </button>
          
          <button
            onClick={exportData}
            style={{
              padding: '8px 16px',
              backgroundColor: 'rgba(16, 185, 129, 0.2)',
              border: '1px solid #10b981',
              borderRadius: '8px',
              color: '#10b981',
              fontSize: '12px',
              cursor: 'pointer'
            }}
          >
            📥 Export CSV
          </button>
        </div>
      </div>

      {/* Filters */}
      <div style={{ display: 'flex', gap: '16px', marginBottom: '24px', flexWrap: 'wrap' }}>
        <div>
          <label style={{ display: 'block', color: '#9ca3af', fontSize: '12px', marginBottom: '4px' }}>
            Time Period
          </label>
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value as any)}
            style={{
              padding: '8px 12px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              border: '1px solid #4b5563',
              borderRadius: '6px',
              color: 'white',
              fontSize: '14px'
            }}
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="all">All time</option>
          </select>
        </div>

        <div>
          <label style={{ display: 'block', color: '#9ca3af', fontSize: '12px', marginBottom: '4px' }}>
            Platform
          </label>
          <select
            value={selectedPlatform}
            onChange={(e) => setSelectedPlatform(e.target.value)}
            style={{
              padding: '8px 12px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              border: '1px solid #4b5563',
              borderRadius: '6px',
              color: 'white',
              fontSize: '14px'
            }}
          >
            {platforms.map(platform => (
              <option key={platform.id} value={platform.id}>
                {platform.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '24px'
        }}>
          <div style={{
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            border: '1px solid rgba(59, 130, 246, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>👆</div>
            <div style={{ color: '#60a5fa', fontSize: '20px', fontWeight: 'bold' }}>
              {metrics.totalClicks.toLocaleString()}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Total Clicks</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            border: '1px solid rgba(16, 185, 129, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>👥</div>
            <div style={{ color: '#10b981', fontSize: '20px', fontWeight: 'bold' }}>
              {metrics.totalRegistrations.toLocaleString()}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Registrations</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            border: '1px solid rgba(245, 158, 11, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>💰</div>
            <div style={{ color: '#f59e0b', fontSize: '20px', fontWeight: 'bold' }}>
              {metrics.totalConversions.toLocaleString()}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Conversions</div>
          </div>

          <div style={{
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            border: '1px solid rgba(139, 92, 246, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '24px', marginBottom: '4px' }}>📈</div>
            <div style={{ color: '#a78bfa', fontSize: '20px', fontWeight: 'bold' }}>
              ${metrics.totalRevenue.toFixed(2)}
            </div>
            <div style={{ color: '#9ca3af', fontSize: '12px' }}>Total Revenue</div>
          </div>
        </div>
      )}

      {/* Performance Insights */}
      {metrics && (
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '24px'
        }}>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
            🎯 Performance Insights
          </h4>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '12px',
            fontSize: '14px'
          }}>
            <div>
              <span style={{ color: '#9ca3af' }}>Conversion Rate:</span>
              <div style={{ color: '#10b981', fontWeight: '600' }}>
                {metrics.averageConversionRate.toFixed(2)}%
              </div>
            </div>
            <div>
              <span style={{ color: '#9ca3af' }}>Revenue per Click:</span>
              <div style={{ color: '#f59e0b', fontWeight: '600' }}>
                ${metrics.averageRevenuePerClick.toFixed(2)}
              </div>
            </div>
            <div>
              <span style={{ color: '#9ca3af' }}>Top Campaign:</span>
              <div style={{ color: '#60a5fa', fontWeight: '600' }}>
                {metrics.topPerformingCampaign}
              </div>
            </div>
            <div>
              <span style={{ color: '#9ca3af' }}>Top Platform:</span>
              <div style={{ color: '#a78bfa', fontWeight: '600' }}>
                {metrics.topPerformingPlatform}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Campaign Performance Table */}
      {showDetailedView && campaigns.length > 0 && (
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px',
          marginBottom: '24px'
        }}>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
            📋 Campaign Performance Details
          </h4>
          
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '1px solid #4b5563' }}>
                  <th style={{ padding: '8px', textAlign: 'left', color: '#9ca3af', fontSize: '12px' }}>Campaign</th>
                  <th style={{ padding: '8px', textAlign: 'left', color: '#9ca3af', fontSize: '12px' }}>Platform</th>
                  <th style={{ padding: '8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Clicks</th>
                  <th style={{ padding: '8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Conversions</th>
                  <th style={{ padding: '8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Conv. Rate</th>
                  <th style={{ padding: '8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>Revenue</th>
                  <th style={{ padding: '8px', textAlign: 'center', color: '#9ca3af', fontSize: '12px' }}>RPC</th>
                </tr>
              </thead>
              <tbody>
                {campaigns.slice(0, 10).map((campaign, index) => (
                  <tr key={campaign.id} style={{ borderBottom: '1px solid #374151' }}>
                    <td style={{ padding: '8px', color: '#f3f4f6', fontSize: '13px' }}>
                      {campaign.campaign_name}
                    </td>
                    <td style={{ padding: '8px', color: '#d1d5db', fontSize: '13px' }}>
                      <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                        {platforms.find(p => p.id === campaign.campaign_source)?.icon}
                        {campaign.campaign_source}
                      </span>
                    </td>
                    <td style={{ padding: '8px', textAlign: 'center', color: '#60a5fa', fontSize: '13px' }}>
                      {campaign.clicks}
                    </td>
                    <td style={{ padding: '8px', textAlign: 'center', color: '#10b981', fontSize: '13px' }}>
                      {campaign.conversions}
                    </td>
                    <td style={{ padding: '8px', textAlign: 'center', color: '#f59e0b', fontSize: '13px' }}>
                      {campaign.conversion_rate.toFixed(1)}%
                    </td>
                    <td style={{ padding: '8px', textAlign: 'center', color: '#a78bfa', fontSize: '13px' }}>
                      ${campaign.total_revenue.toFixed(2)}
                    </td>
                    <td style={{ padding: '8px', textAlign: 'center', color: '#9ca3af', fontSize: '13px' }}>
                      ${campaign.revenue_per_click.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Simple Chart Visualization */}
      {chartData.length > 0 && (
        <div style={{
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          borderRadius: '8px',
          padding: '16px'
        }}>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
            📈 Performance Trend
          </h4>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {chartData.slice(-7).map((data, index) => (
              <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div style={{ minWidth: '80px', color: '#9ca3af', fontSize: '12px' }}>
                  {new Date(data.date).toLocaleDateString()}
                </div>
                <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div style={{
                    width: `${Math.max((data.clicks / Math.max(...chartData.map((d: any) => d.clicks))) * 100, 5)}%`,
                    height: '20px',
                    backgroundColor: '#60a5fa',
                    borderRadius: '2px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '10px',
                    color: 'white'
                  }}>
                    {data.clicks}
                  </div>
                  <span style={{ color: '#9ca3af', fontSize: '12px', minWidth: '60px' }}>
                    ${data.revenue.toFixed(0)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {campaigns.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px', color: '#9ca3af' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
          <h3 style={{ fontSize: '18px', marginBottom: '8px' }}>No Campaign Data Yet</h3>
          <p style={{ fontSize: '14px' }}>
            Start creating and sharing referral links to see your campaign analytics here.
          </p>
        </div>
      )}
    </div>
  );
};
