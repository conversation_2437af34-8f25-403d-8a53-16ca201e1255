import React from 'react'
import { supabase } from './supabase'

export interface AdminUser {
  id: string
  email: string
  role: 'super_admin' | 'admin' | 'moderator'
  created_at: string
  updated_at: string
}

export interface AdminPermissions {
  canManageUsers: boolean
  canManagePayments: boolean
  canManageSponsors: boolean
  canManageGallery: boolean
  canViewAuditLogs: boolean
  canManageAdmins: boolean
  canAccessDebug: boolean
}

// Check if current user is admin
export const checkAdminStatus = async (userEmail: string): Promise<AdminUser | null> => {
  try {
    console.log('🔍 Checking admin status for:', userEmail)

    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', userEmail)
      .single()

    if (error) {
      console.log('❌ Admin check failed:', error.code, error.message)
      return null
    }

    if (data) {
      console.log('✅ Admin user verified:', data.email, data.role)
      return data as AdminUser
    }

    console.log('❌ User is not an admin:', userEmail)
    return null
  } catch (err) {
    console.error('Error checking admin status:', err)
    return null
  }
}

// Get admin permissions based on role
export const getAdminPermissions = (role: string): AdminPermissions => {
  switch (role) {
    case 'super_admin':
      return {
        canManageUsers: true,
        canManagePayments: true,
        canManageSponsors: true,
        canManageGallery: true,
        canViewAuditLogs: true,
        canManageAdmins: true,
        canAccessDebug: true
      }
    
    case 'admin':
      return {
        canManageUsers: true,
        canManagePayments: true,
        canManageSponsors: true,
        canManageGallery: true,
        canViewAuditLogs: true,
        canManageAdmins: false,
        canAccessDebug: false
      }
    
    case 'moderator':
      return {
        canManageUsers: false,
        canManagePayments: false,
        canManageSponsors: true,
        canManageGallery: true,
        canViewAuditLogs: false,
        canManageAdmins: false,
        canAccessDebug: false
      }
    
    default:
      return {
        canManageUsers: false,
        canManagePayments: false,
        canManageSponsors: false,
        canManageGallery: false,
        canViewAuditLogs: false,
        canManageAdmins: false,
        canAccessDebug: false
      }
  }
}

// Validate admin access for specific action
export const validateAdminAccess = async (
  userEmail: string, 
  requiredPermission: keyof AdminPermissions
): Promise<boolean> => {
  try {
    const adminUser = await checkAdminStatus(userEmail)
    
    if (!adminUser) {
      return false
    }

    const permissions = getAdminPermissions(adminUser.role)
    return permissions[requiredPermission]
  } catch (err) {
    console.error('Error validating admin access:', err)
    return false
  }
}

// Log admin action for audit trail
export const logAdminAction = async (
  adminEmail: string,
  action: string,
  targetType: string,
  targetId: string,
  details: any = {},
  oldValues: any = {},
  newValues: any = {}
) => {
  try {
    // Use the current table schema (admin_telegram_id, admin_username)
    // For web admin actions, we'll use a special telegram_id and the email as username
    const logEntry = {
      admin_telegram_id: 0, // Use 0 for web admin actions (system actions)
      admin_username: adminEmail, // Store email in username field for web admins
      action,
      target_type: targetType,
      target_id: targetId,
      details: {
        ...details,
        old_values: oldValues,
        new_values: newValues,
        timestamp: new Date().toISOString(),
        user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
        source: 'web_admin'
      },
      ip_address: null, // Would be set by server
      user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server'
    }

    console.log('🔍 Admin Action Logged:', logEntry)

    // Store in database audit_logs table
    const { error } = await supabase
      .from('admin_audit_logs')
      .insert(logEntry)

    if (error) {
      console.error('Failed to store audit log:', error)
      return false
    }

    return true
  } catch (err) {
    console.error('Error logging admin action:', err)
    return false
  }
}

// React hook for admin authentication
export const useAdminAuth = () => {
  const [adminUser, setAdminUser] = React.useState<AdminUser | null>(null)
  const [permissions, setPermissions] = React.useState<AdminPermissions | null>(null)
  const [loading, setLoading] = React.useState(true)

  React.useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get current user from Supabase auth
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user?.email) {
          setAdminUser(null)
          setPermissions(null)
          return
        }

        const admin = await checkAdminStatus(user.email)
        
        if (admin) {
          setAdminUser(admin)
          setPermissions(getAdminPermissions(admin.role))
        } else {
          setAdminUser(null)
          setPermissions(null)
        }
      } catch (err) {
        console.error('Error checking admin auth:', err)
        setAdminUser(null)
        setPermissions(null)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  return {
    adminUser,
    permissions,
    loading,
    isAdmin: !!adminUser,
    isSuperAdmin: adminUser?.role === 'super_admin',
    hasPermission: (permission: keyof AdminPermissions) => 
      permissions ? permissions[permission] : false
  }
}

// Higher-order component for admin route protection
export const withAdminAuth = (
  WrappedComponent: React.ComponentType<any>,
  requiredPermission?: keyof AdminPermissions
) => {
  return function AdminProtectedComponent(props: any) {
    const { adminUser, permissions, loading, hasPermission } = useAdminAuth()

    if (loading) {
      return React.createElement('div',
        { className: "flex items-center justify-center py-12" },
        React.createElement('div', { className: "animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400" }),
        React.createElement('span', { className: "ml-3 text-gray-300" }, "Checking permissions...")
      )
    }

    if (!adminUser) {
      return React.createElement('div',
        { className: "text-center py-12" },
        React.createElement('div', { className: "text-red-400 text-6xl mb-4" }, "🚫"),
        React.createElement('h2', { className: "text-2xl font-bold text-white mb-2" }, "Access Denied"),
        React.createElement('p', { className: "text-gray-400" },
          "You need administrator privileges to access this section."
        )
      )
    }

    if (requiredPermission && !hasPermission(requiredPermission)) {
      return React.createElement('div',
        { className: "text-center py-12" },
        React.createElement('div', { className: "text-yellow-400 text-6xl mb-4" }, "⚠️"),
        React.createElement('h2', { className: "text-2xl font-bold text-white mb-2" }, "Insufficient Permissions"),
        React.createElement('p', { className: "text-gray-400" },
          `Your admin role (${adminUser.role}) doesn't have permission to access this feature.`
        )
      )
    }

    return React.createElement(WrappedComponent, { ...props, adminUser, permissions })
  }
}

// Admin action wrapper with logging
export const withAdminLogging = (
  action: string,
  targetType: string
) => {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      try {
        // Get admin user email (would be from context/props)
        const adminEmail = '<EMAIL>' // TODO: Get from context
        
        // Execute the original method
        const result = await method.apply(this, args)
        
        // Log the action
        await logAdminAction(
          adminEmail,
          action,
          targetType,
          args[0]?.id || 'unknown',
          { args: args.slice(1) }
        )
        
        return result
      } catch (err) {
        console.error(`Error in admin action ${action}:`, err)
        throw err
      }
    }
  }
}
