<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test</title>
    <link rel="stylesheet" href="./index.css">
</head>
<body>
    <div class="bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white min-h-screen overflow-y-auto">
        <div class="container mx-auto px-4 py-8">
            <div class="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl mb-8">
                <div class="space-y-8">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-white mb-2">
                            CSS Test - Sign In to Your Account
                        </h3>
                        <p class="text-gray-400">
                            Access your Aureus Alliance Holdings dashboard
                        </p>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-2">
                                Email Address
                            </label>
                            <input 
                                type="email" 
                                class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent transition-all duration-300"
                                placeholder="<EMAIL>"
                            />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-semibold text-gray-300 mb-2">
                                Password
                            </label>
                            <input 
                                type="password" 
                                class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:border-transparent transition-all duration-300"
                                placeholder="••••••••••••••••"
                            />
                        </div>
                        
                        <button class="w-full bg-gradient-to-r from-gold-400 to-gold-600 text-black font-bold py-3 px-6 rounded-xl hover:from-gold-500 hover:to-gold-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-gold-400/25">
                            Sign In
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
