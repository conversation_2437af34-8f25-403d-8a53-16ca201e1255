const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, serviceKey);

async function createRPCFunctions() {
  console.log('🔧 Creating RPC functions for validation...');
  
  // Create email check function
  try {
    console.log('📝 Creating check_email_exists function...');
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql_query: `
        CREATE OR REPLACE FUNCTION check_email_exists(email_to_check TEXT)
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          RETURN EXISTS (
            SELECT 1 FROM users 
            WHERE LOWER(email) = LOWER(email_to_check)
          );
        END;
        $$;
      `
    });
    
    if (error) {
      console.error('❌ Error creating email function:', error);
    } else {
      console.log('✅ Email function created successfully');
    }
  } catch (e) {
    console.error('❌ Exception creating email function:', e.message);
  }

  // Create username check function
  try {
    console.log('📝 Creating check_username_exists function...');
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql_query: `
        CREATE OR REPLACE FUNCTION check_username_exists(username_to_check TEXT)
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          RETURN EXISTS (
            SELECT 1 FROM users 
            WHERE LOWER(username) = LOWER(username_to_check)
          );
        END;
        $$;
      `
    });
    
    if (error) {
      console.error('❌ Error creating username function:', error);
    } else {
      console.log('✅ Username function created successfully');
    }
  } catch (e) {
    console.error('❌ Exception creating username function:', e.message);
  }

  // Grant permissions
  const permissions = [
    'GRANT EXECUTE ON FUNCTION check_email_exists(TEXT) TO authenticated;',
    'GRANT EXECUTE ON FUNCTION check_username_exists(TEXT) TO authenticated;',
    'GRANT EXECUTE ON FUNCTION check_email_exists(TEXT) TO anon;',
    'GRANT EXECUTE ON FUNCTION check_username_exists(TEXT) TO anon;'
  ];

  for (let i = 0; i < permissions.length; i++) {
    try {
      console.log(`📝 Granting permission ${i + 1}/${permissions.length}...`);
      const { data, error } = await supabase.rpc('exec_sql', { sql_query: permissions[i] });
      if (error) {
        console.error(`❌ Error granting permission ${i + 1}:`, error);
      } else {
        console.log(`✅ Permission ${i + 1} granted successfully`);
      }
    } catch (e) {
      console.error(`❌ Exception granting permission ${i + 1}:`, e.message);
    }
  }
  
  // Test the functions
  console.log('🧪 Testing RPC functions...');
  
  try {
    const { data: emailTest, error: emailError } = await supabase
      .rpc('check_email_exists', { email_to_check: '<EMAIL>' });
    
    if (emailError) {
      console.error('❌ Email RPC test error:', emailError);
    } else {
      console.log('✅ Email RPC test successful:', emailTest);
    }
    
    const { data: usernameTest, error: usernameError } = await supabase
      .rpc('check_username_exists', { username_to_check: 'testuser' });
    
    if (usernameError) {
      console.error('❌ Username RPC test error:', usernameError);
    } else {
      console.log('✅ Username RPC test successful:', usernameTest);
    }
  } catch (error) {
    console.error('❌ Test exception:', error);
  }

  console.log('🎉 RPC function creation complete!');
}

createRPCFunctions();
