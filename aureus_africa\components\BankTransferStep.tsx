import React, { useState } from 'react';
import { supabase, getServiceRoleClient } from '../lib/supabase';

interface CurrentPhase {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
}

interface User {
  id: number;
  email: string;
  username?: string;
}

interface BankTransferStepProps {
  amount: string;
  shares: number;
  currentPhase: CurrentPhase | null;
  user: User | null;
  onBack: () => void;
  onComplete: () => void;
}

const BankTransferStep: React.FC<BankTransferStepProps> = ({
  amount,
  shares,
  currentPhase,
  user,
  onBack,
  onComplete
}) => {
  const [proofFile, setProofFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Bank transfer configuration (matching Telegram bot)
  const bankTransferConfig = {
    exchangeRate: 18, // USD to ZAR
    transactionFee: 0.10 // 10% transaction fee
  };

  const totalCost = shares * (currentPhase?.price_per_share || 0);
  const zarAmount = totalCost * bankTransferConfig.exchangeRate;
  const feeAmount = zarAmount * bankTransferConfig.transactionFee;
  const totalZarAmount = zarAmount + feeAmount;

  // Generate reference number (matching Telegram bot format)
  const referenceNumber = `AUR${Date.now().toString().slice(-8)}${Math.floor(Math.random() * 100).toString().padStart(2, '0')}`;

  // Company banking details
  const bankingDetails = {
    bankName: 'First National Bank (FNB)',
    accountName: 'AUREUS ALLIANCE HOLDINGS (PTY) LTD',
    accountNumber: '***********',
    branchCode: '250655',
    accountType: 'Business Cheque Account',
    reference: referenceNumber
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        setError('Please upload a valid file (JPG, PNG, WEBP, PDF)');
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size must be less than 5MB');
        return;
      }
      
      setProofFile(file);
      setError('');
    }
  };

  const handleSubmitPayment = async () => {
    if (!proofFile) {
      setError('Please upload payment proof');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Upload proof file to Supabase Storage (matching Telegram bot format)
      const fileExt = proofFile.name.split('.').pop();
      const timestamp = Date.now();
      const fileName = `payment_${user?.id}_${timestamp}.${fileExt}`;
      const filePath = fileName; // Store directly in bucket root like Telegram bot

      console.log('📤 Uploading file to Supabase Storage:', filePath);

      // Upload to existing 'proof' bucket using service role client to bypass RLS
      const serviceClient = getServiceRoleClient()
      const { data: uploadData, error: uploadError } = await serviceClient.storage
        .from('proof')
        .upload(filePath, proofFile);

      if (uploadError) {
        console.error('Upload error:', uploadError);

        // Provide specific error messages based on the error type
        if (uploadError.message.includes('Bucket not found')) {
          throw new Error('Proof bucket not found. Please check Supabase Storage configuration.');
        } else if (uploadError.message.includes('row-level security policy')) {
          throw new Error('Storage permission denied. Please run the storage policy fix script: sql/fix_storage_policies.sql');
        } else if (uploadError.message.includes('new row violates')) {
          throw new Error('Upload permission denied. Please contact admin to configure storage policies.');
        }

        throw new Error('Failed to upload proof file: ' + uploadError.message);
      }

      console.log('✅ File uploaded successfully:', uploadData);

      // Get public URL for the uploaded file using service role client
      const { data: { publicUrl } } = serviceClient.storage
        .from('proof')
        .getPublicUrl(filePath);

      const fileUrl = publicUrl;
      console.log('📎 File URL:', fileUrl);

      // Get the integer user ID from the users table using service role client
      console.log('🔍 DEBUG: User object in BankTransferStep:', {
        hasUser: !!user,
        userId: user?.id,
        userKeys: user ? Object.keys(user) : [],
        hasUserMetadata: !!user?.user_metadata,
        userMetadataUserId: user?.user_metadata?.user_id
      });

      let userData = null;
      let userError = null;

      // Handle different user ID formats - FIXED based on console output
      if (user?.database_user?.id) {
        // Direct database user ID (from database_user object)
        console.log('✅ Using database_user.id:', user.database_user.id);
        userData = { id: user.database_user.id };
        userError = null;
      } else if (user?.user_metadata?.user_id) {
        // Use user_id from metadata if available
        console.log('✅ Using user_metadata.user_id:', user.user_metadata.user_id);
        userData = { id: user.user_metadata.user_id };
        userError = null;
      } else if (user?.id?.startsWith('db_')) {
        // Direct database user ID (from database-only authentication)
        const dbUserId = user.id.replace('db_', '');
        console.log('✅ Using db_ prefixed ID:', dbUserId);
        userData = { id: parseInt(dbUserId) };
        userError = null;
      } else if (user?.id) {
        // Try auth_user_id lookup (standard Supabase auth)
        console.log('✅ Trying auth_user_id lookup with:', user.id);
        const result = await serviceClient
          .from('users')
          .select('id')
          .eq('auth_user_id', user.id)
          .single();
        userData = result.data;
        userError = result.error;
      } else {
        console.log('❌ No valid user ID found in user object');
        userError = { message: 'No user ID available' };
      }

      if (userError || !userData) {
        console.error('Error getting user data:', userError);
        console.error('User object:', user);
        throw new Error('User not found. Please ensure you are properly logged in.');
      }

      // Create bank transfer payment transaction directly in Supabase
      const paymentData = {
        user_id: userData.id, // Use integer ID from users table
        amount: totalCost,
        shares_to_purchase: shares,
        network: 'BANK_TRANSFER',
        currency: 'ZAR',
        sender_wallet: fileUrl, // Store proof file URL in sender_wallet field
        receiver_wallet: bankingDetails.accountNumber,
        screenshot_url: fileUrl,
        status: 'pending',
        transaction_notes: JSON.stringify({
          payment_method: 'bank_transfer',
          zar_amount: totalZarAmount,
          exchange_rate: bankTransferConfig.exchangeRate,
          transaction_fee_percent: bankTransferConfig.transactionFee * 100,
          reference_number: referenceNumber
        }),
        created_at: new Date().toISOString()
      };

      const { data: paymentResult, error: paymentError } = await serviceClient
        .from('crypto_payment_transactions')
        .insert([paymentData])
        .select()
        .single();

      if (paymentError) {
        console.error('Payment creation error:', paymentError);
        throw new Error('Failed to create payment transaction: ' + paymentError.message);
      }

      console.log('Bank transfer payment created:', paymentResult);
      
      onComplete();
    } catch (err) {
      console.error('Payment submission error:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit payment');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px', textAlign: 'center' }}>
        🏦 Bank Transfer Payment (ZAR)
      </h2>

      {/* Payment Summary */}
      <div style={{
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        border: '1px solid rgba(34, 197, 94, 0.3)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#10b981', fontWeight: 'bold', margin: '0 0 16px 0' }}>
          💰 PAYMENT SUMMARY:
        </h3>
        <div style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6' }}>
          <p style={{ margin: '0 0 8px 0' }}>• USD Amount: ${totalCost.toFixed(2)}</p>
          <p style={{ margin: '0 0 8px 0' }}>• ZAR Amount: R{zarAmount.toFixed(2)}</p>
          <p style={{ margin: '0 0 8px 0' }}>• Transaction Fee (10%): R{feeAmount.toFixed(2)}</p>
          <p style={{ margin: '0 0 8px 0', fontWeight: 'bold', color: '#10b981' }}>
            • Total to Pay: R{totalZarAmount.toFixed(2)}
          </p>
          <p style={{ margin: 0 }}>• Shares: {shares}</p>
        </div>
      </div>

      {/* Banking Details */}
      <div style={{
        backgroundColor: 'rgba(55, 65, 81, 0.5)',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#fbbf24', fontWeight: 'bold', margin: '0 0 16px 0' }}>
          🏦 BANKING DETAILS:
        </h3>
        <div style={{ color: '#d1d5db', fontSize: '14px' }}>
          <div style={{ marginBottom: '12px' }}>
            <span style={{ color: '#9ca3af' }}>Bank:</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontWeight: 'bold' }}>{bankingDetails.bankName}</span>
              <button
                onClick={() => copyToClipboard(bankingDetails.bankName)}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#374151',
                  border: 'none',
                  borderRadius: '4px',
                  color: '#9ca3af',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                Copy
              </button>
            </div>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <span style={{ color: '#9ca3af' }}>Account Name:</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontWeight: 'bold' }}>{bankingDetails.accountName}</span>
              <button
                onClick={() => copyToClipboard(bankingDetails.accountName)}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#374151',
                  border: 'none',
                  borderRadius: '4px',
                  color: '#9ca3af',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                Copy
              </button>
            </div>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <span style={{ color: '#9ca3af' }}>Account Number:</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontWeight: 'bold', fontFamily: 'monospace' }}>{bankingDetails.accountNumber}</span>
              <button
                onClick={() => copyToClipboard(bankingDetails.accountNumber)}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#374151',
                  border: 'none',
                  borderRadius: '4px',
                  color: '#9ca3af',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                Copy
              </button>
            </div>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <span style={{ color: '#9ca3af' }}>Branch Code:</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontWeight: 'bold', fontFamily: 'monospace' }}>{bankingDetails.branchCode}</span>
              <button
                onClick={() => copyToClipboard(bankingDetails.branchCode)}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#374151',
                  border: 'none',
                  borderRadius: '4px',
                  color: '#9ca3af',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                Copy
              </button>
            </div>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <span style={{ color: '#9ca3af' }}>Reference:</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontWeight: 'bold', fontFamily: 'monospace', color: '#fbbf24' }}>{referenceNumber}</span>
              <button
                onClick={() => copyToClipboard(referenceNumber)}
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#374151',
                  border: 'none',
                  borderRadius: '4px',
                  color: '#9ca3af',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                Copy
              </button>
            </div>
          </div>
          
          <div>
            <span style={{ color: '#9ca3af' }}>Account Type:</span>
            <span style={{ fontWeight: 'bold', marginLeft: '8px' }}>{bankingDetails.accountType}</span>
          </div>
        </div>
      </div>

      {/* Proof Upload */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#9ca3af', marginBottom: '8px', fontSize: '14px' }}>
          Upload Payment Proof (Bank Statement/Receipt)
        </label>
        <input
          type="file"
          accept="image/*,.pdf"
          onChange={handleFileUpload}
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            border: '2px solid #374151',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px'
          }}
        />
        {proofFile && (
          <p style={{ color: '#10b981', fontSize: '14px', marginTop: '8px' }}>
            ✅ File selected: {proofFile.name}
          </p>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div style={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '20px',
          color: '#f87171'
        }}>
          ❌ {error}
        </div>
      )}

      {/* Important Notice */}
      <div style={{
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        border: '1px solid rgba(245, 158, 11, 0.3)',
        borderRadius: '12px',
        padding: '16px',
        marginBottom: '20px'
      }}>
        <h3 style={{ color: '#fbbf24', fontWeight: 'bold', margin: '0 0 8px 0' }}>
          ⚠️ IMPORTANT:
        </h3>
        <p style={{ color: '#d1d5db', fontSize: '14px', margin: 0 }}>
          Use the reference number "{referenceNumber}" for your transfer. Processing time: 1-3 business days.
        </p>
      </div>

      {/* Submit Button */}
      <button
        onClick={handleSubmitPayment}
        disabled={loading || !proofFile}
        style={{
          width: '100%',
          padding: '16px',
          backgroundColor: (!loading && proofFile) ? '#10b981' : '#374151',
          border: 'none',
          borderRadius: '12px',
          color: 'white',
          fontSize: '18px',
          fontWeight: 'bold',
          cursor: (!loading && proofFile) ? 'pointer' : 'not-allowed',
          marginBottom: '16px'
        }}
      >
        {loading ? 'Submitting Payment...' : 'Submit Payment for Review'}
      </button>

      {/* Back Button */}
      <button
        onClick={onBack}
        disabled={loading}
        style={{
          width: '100%',
          padding: '12px',
          backgroundColor: 'transparent',
          border: '2px solid #374151',
          borderRadius: '8px',
          color: '#9ca3af',
          fontSize: '16px',
          cursor: loading ? 'not-allowed' : 'pointer'
        }}
      >
        ← Back to Payment Method
      </button>
    </div>
  );
};

export default BankTransferStep;
