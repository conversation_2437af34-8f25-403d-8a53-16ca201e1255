import React, { useState, useEffect } from 'react';
import { KYCCenter } from '../user/KYCCenter';
import SharePurchaseFlow from '../SharePurchaseFlow';
import { SavedPaymentMethods } from '../user/SavedPaymentMethods';
import { PurchaseCalculator } from '../user/PurchaseCalculator';

interface EnhancedUserDashboardProps {
  user: any;
  currentPhase?: any;
}

export const EnhancedUserDashboard: React.FC<EnhancedUserDashboardProps> = ({
  user,
  currentPhase
}) => {
  const [activeSection, setActiveSection] = useState<'overview' | 'kyc' | 'purchase' | 'payments'>('overview');
  const [showPurchaseFlow, setShowPurchaseFlow] = useState(false);
  const [kycStatus, setKycStatus] = useState<any>(null);
  const [dashboardStats, setDashboardStats] = useState({
    totalShares: 0,
    totalValue: 0,
    pendingPurchases: 0,
    kycCompleted: false
  });

  useEffect(() => {
    loadDashboardData();
  }, [user]);

  const loadDashboardData = async () => {
    try {
      // Load KYC status
      const kycResponse = await fetch('/api/user/kyc/status', {
        headers: { 'x-user-id': user.database_user?.id?.toString() || '0' }
      });
      if (kycResponse.ok) {
        const kycData = await kycResponse.json();
        setKycStatus(kycData);
        setDashboardStats(prev => ({ ...prev, kycCompleted: kycData.isCompleted }));
      }

      // Load other dashboard stats (portfolio, purchases, etc.)
      // This would typically fetch from multiple endpoints
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const renderQuickActions = () => (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151',
      marginBottom: '24px'
    }}>
      <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>
        ⚡ Quick Actions
      </h3>
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px'
      }}>
        <button
          onClick={() => setShowPurchaseFlow(true)}
          style={{
            padding: '20px',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            border: '2px solid rgba(16, 185, 129, 0.3)',
            borderRadius: '12px',
            color: '#10b981',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            textAlign: 'left',
            transition: 'all 0.2s ease'
          }}
        >
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>🚀</div>
          <div>Buy Shares</div>
          <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>
            Streamlined purchase flow
          </div>
        </button>

        <button
          onClick={() => setActiveSection('kyc')}
          style={{
            padding: '20px',
            backgroundColor: kycStatus?.isCompleted 
              ? 'rgba(16, 185, 129, 0.1)' 
              : 'rgba(245, 158, 11, 0.1)',
            border: kycStatus?.isCompleted 
              ? '2px solid rgba(16, 185, 129, 0.3)' 
              : '2px solid rgba(245, 158, 11, 0.3)',
            borderRadius: '12px',
            color: kycStatus?.isCompleted ? '#10b981' : '#f59e0b',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            textAlign: 'left',
            transition: 'all 0.2s ease'
          }}
        >
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>
            {kycStatus?.isCompleted ? '✅' : '🆔'}
          </div>
          <div>KYC Verification</div>
          <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>
            {kycStatus?.isCompleted ? 'Completed' : 'Required for certificates'}
          </div>
        </button>

        <button
          onClick={() => setActiveSection('payments')}
          style={{
            padding: '20px',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            border: '2px solid rgba(59, 130, 246, 0.3)',
            borderRadius: '12px',
            color: '#60a5fa',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            textAlign: 'left',
            transition: 'all 0.2s ease'
          }}
        >
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>💳</div>
          <div>Payment Methods</div>
          <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>
            Manage saved methods
          </div>
        </button>

        <button
          onClick={() => setActiveSection('purchase')}
          style={{
            padding: '20px',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            border: '2px solid rgba(139, 92, 246, 0.3)',
            borderRadius: '12px',
            color: '#a78bfa',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            textAlign: 'left',
            transition: 'all 0.2s ease'
          }}
        >
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>🧮</div>
          <div>Purchase Calculator</div>
          <div style={{ fontSize: '12px', color: '#9ca3af', marginTop: '4px' }}>
            Plan your purchases
          </div>
        </button>
      </div>
    </div>
  );

  const renderDashboardOverview = () => (
    <div>
      {/* Dashboard Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '20px',
        marginBottom: '24px'
      }}>
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '24px',
          border: '1px solid #374151',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>📊</div>
          <div style={{ color: '#3b82f6', fontSize: '24px', fontWeight: 'bold' }}>
            {dashboardStats.totalShares.toLocaleString()}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '14px' }}>Total Shares</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '24px',
          border: '1px solid #374151',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>💰</div>
          <div style={{ color: '#10b981', fontSize: '24px', fontWeight: 'bold' }}>
            ${dashboardStats.totalValue.toLocaleString()}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '14px' }}>Portfolio Value</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '24px',
          border: '1px solid #374151',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>⏳</div>
          <div style={{ color: '#f59e0b', fontSize: '24px', fontWeight: 'bold' }}>
            {dashboardStats.pendingPurchases}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '14px' }}>Pending Purchases</div>
        </div>

        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.9)',
          borderRadius: '12px',
          padding: '24px',
          border: '1px solid #374151',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>
            {dashboardStats.kycCompleted ? '✅' : '⏰'}
          </div>
          <div style={{ 
            color: dashboardStats.kycCompleted ? '#10b981' : '#f59e0b', 
            fontSize: '24px', 
            fontWeight: 'bold' 
          }}>
            {dashboardStats.kycCompleted ? 'Verified' : 'Pending'}
          </div>
          <div style={{ color: '#9ca3af', fontSize: '14px' }}>KYC Status</div>
        </div>
      </div>

      {/* Important Notices */}
      {!dashboardStats.kycCompleted && (
        <div style={{
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          border: '1px solid rgba(245, 158, 11, 0.3)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '24px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
            <span style={{ fontSize: '24px' }}>⚠️</span>
            <h3 style={{ color: '#f59e0b', fontSize: '18px', fontWeight: 'bold', margin: 0 }}>
              KYC Verification Required
            </h3>
          </div>
          <p style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6', margin: '0 0 16px 0' }}>
            Complete your KYC verification to receive share certificates and unlock all platform features. 
            The process takes only 5-10 minutes and ensures compliance with regulatory requirements.
          </p>
          <button
            onClick={() => setActiveSection('kyc')}
            style={{
              padding: '12px 24px',
              backgroundColor: '#f59e0b',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer'
            }}
          >
            Complete KYC Now →
          </button>
        </div>
      )}
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'kyc':
        return (
          <KYCCenter 
            userId={user.database_user?.id || 0}
          />
        );
      
      case 'payments':
        return (
          <SavedPaymentMethods 
            userId={user.database_user?.id || 0}
            showAddButton={true}
          />
        );
      
      case 'purchase':
        return (
          <PurchaseCalculator
            amount="1000"
            currentPhase={currentPhase}
            onAmountChange={() => {}}
            showAdvanced={true}
          />
        );
      
      default:
        return renderDashboardOverview();
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ marginBottom: '32px' }}>
        <h1 style={{
          fontSize: '32px',
          fontWeight: 'bold',
          color: '#f59e0b',
          marginBottom: '8px',
          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          🏆 Enhanced Dashboard
        </h1>
        <p style={{ color: '#9ca3af', fontSize: '16px' }}>
          Welcome back! Manage your shares, complete KYC, and track your portfolio.
        </p>
      </div>

      {/* Navigation */}
      <div style={{
        display: 'flex',
        gap: '8px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }}>
        {[
          { key: 'overview', label: '📊 Overview', icon: '📊' },
          { key: 'kyc', label: '🆔 KYC', icon: '🆔' },
          { key: 'purchase', label: '🧮 Calculator', icon: '🧮' },
          { key: 'payments', label: '💳 Payments', icon: '💳' }
        ].map(section => (
          <button
            key={section.key}
            onClick={() => setActiveSection(section.key as any)}
            style={{
              padding: '12px 20px',
              backgroundColor: activeSection === section.key 
                ? 'rgba(59, 130, 246, 0.2)' 
                : 'rgba(55, 65, 81, 0.5)',
              border: activeSection === section.key 
                ? '2px solid #3b82f6' 
                : '1px solid #4b5563',
              borderRadius: '8px',
              color: activeSection === section.key ? '#60a5fa' : '#d1d5db',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            {section.label}
          </button>
        ))}
      </div>

      {/* Quick Actions (only on overview) */}
      {activeSection === 'overview' && renderQuickActions()}

      {/* Main Content */}
      {renderSectionContent()}

      {/* Purchase Flow Modal */}
      {showPurchaseFlow && (
        <SharePurchaseFlow
          user={user}
          onClose={() => setShowPurchaseFlow(false)}
        />
      )}
    </div>
  );
};
