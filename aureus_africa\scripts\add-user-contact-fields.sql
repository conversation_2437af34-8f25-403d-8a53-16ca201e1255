-- Add missing contact fields to users table
-- This script adds phone_number and telegram_username columns if they don't exist

-- Add phone_number column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'phone_number'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE users ADD COLUMN phone_number VARCHAR(20);
        RAISE NOTICE 'Added phone_number column to users table';
    ELSE
        RAISE NOTICE 'phone_number column already exists in users table';
    END IF;
END $$;

-- Add telegram_username column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'telegram_username'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE users ADD COLUMN telegram_username VARCHAR(50);
        RAISE NOTICE 'Added telegram_username column to users table';
    ELSE
        RAISE NOTICE 'telegram_username column already exists in users table';
    END IF;
END $$;

-- <PERSON>reate index on telegram_username for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_telegram_username ON users(telegram_username);

-- Grant necessary permissions
GRANT SELECT, UPDATE ON users TO authenticated;

-- Verify the columns were added
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
AND column_name IN ('phone_number', 'telegram_username')
ORDER BY column_name;
