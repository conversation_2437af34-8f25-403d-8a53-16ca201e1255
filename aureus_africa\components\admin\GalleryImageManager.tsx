import React, { useState, useEffect, useMemo } from 'react';
import { useGalleryImages, useGalleryCategories } from '../../hooks/useGallery';
import { GalleryService } from '../../lib/galleryService';
import type { GalleryImage, GalleryCategory } from '../../types/gallery';

interface GalleryImageManagerProps {
  selectedCategory?: string;
  onCategoryChange?: (categoryId: string) => void;
  onRefresh?: () => void;
}

export const GalleryImageManager: React.FC<GalleryImageManagerProps> = ({
  selectedCategory = '',
  onCategoryChange,
  onRefresh
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);
  const [editingImage, setEditingImage] = useState<GalleryImage | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  const filters = useMemo(() => ({
    ...(selectedCategory && { category_id: selectedCategory }),
    ...(searchTerm && { search: searchTerm }),
    ...(showFeaturedOnly && { is_featured: true })
  }), [selectedCategory, searchTerm, showFeaturedOnly]);

  const { images, loading, error, refetch } = useGalleryImages(filters);
  const { categories } = useGalleryCategories();

  // Check if database is set up
  const isDatabaseSetup = images.length > 0 || (!loading && !error);

  // Show setup message if still loading for too long
  const [showSetupMessage, setShowSetupMessage] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (loading && images.length === 0) {
        setShowSetupMessage(true);
        console.log('Gallery loading timeout - showing setup message');
      }
    }, 5000); // Show message after 5 seconds of loading

    return () => clearTimeout(timer);
  }, [loading, images.length]);

  // Debug effect to log state changes
  useEffect(() => {
    console.log('GalleryImageManager state:', {
      loading,
      error,
      imagesCount: images.length,
      filters,
      showSetupMessage
    });
  }, [loading, error, images.length, filters, showSetupMessage]);

  const handleToggleFeatured = async (image: GalleryImage) => {
    try {
      await GalleryService.toggleImageFeatured(image.id);
      await refetch();
      onRefresh?.();
    } catch (error) {
      console.error('Failed to toggle featured status:', error);
      alert('Failed to update featured status');
    }
  };

  const handleDeleteImage = async (imageId: string) => {
    try {
      await GalleryService.deleteImage(imageId);
      await refetch();
      onRefresh?.();
      setShowDeleteConfirm(null);
    } catch (error) {
      console.error('Failed to delete image:', error);
      alert('Failed to delete image');
    }
  };

  const handleUpdateImage = async (imageData: any) => {
    try {
      await GalleryService.updateImage(imageData);
      await refetch();
      onRefresh?.();
      setEditingImage(null);
    } catch (error) {
      console.error('Failed to update image:', error);
      alert('Failed to update image');
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mb-4"></div>
        <p className="text-gray-400 text-center">
          {showSetupMessage ? (
            <>
              Setting up gallery system...<br />
              <span className="text-sm text-gray-500">This may take a moment on first load</span>
            </>
          ) : (
            'Loading gallery images...'
          )}
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-4">
          <svg className="w-12 h-12 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-white mb-2">Failed to load images</h3>
        <p className="text-gray-400 mb-4">{error}</p>
        <button
          onClick={() => refetch()}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Search Images
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by title or description..."
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Category
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => onCategoryChange?.(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-amber-500"
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Filter Options
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showFeaturedOnly}
                onChange={(e) => setShowFeaturedOnly(e.target.checked)}
                className="rounded border-gray-600 text-amber-600 shadow-sm focus:border-amber-500 focus:ring-amber-500"
              />
              <span className="ml-2 text-sm text-gray-300">Featured only</span>
            </label>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm('');
                setShowFeaturedOnly(false);
                onCategoryChange?.('');
              }}
              className="px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors"
            >
              Clear filters
            </button>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-400">
          {images.length === 0 ? 'No images found' : `${images.length} image(s) found`}
        </p>
      </div>

      {/* Images Grid */}
      {images.length === 0 ? (
        <div className="text-center py-12">
          {loading ? (
            <div className="text-gray-400">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-500 mx-auto mb-4"></div>
              <p>Loading images...</p>
            </div>
          ) : (
            <>
              {/* Database Setup Notice */}
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6 mb-6 max-w-2xl mx-auto">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                      Gallery Database Setup Required
                    </h3>
                    <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                      <p>The gallery database tables need to be created before you can manage images.</p>
                      <p className="mt-2">Please run the gallery schema SQL in your Supabase dashboard to enable full gallery management.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-white mb-2">No images found</h3>
              <p className="text-gray-400">Set up the database or try uploading some images.</p>
            </>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {images.map((image) => (
            <div key={image.id} className="bg-gray-800 rounded-lg overflow-hidden border border-gray-700">
              {/* Image */}
              <div className="aspect-w-16 aspect-h-12 bg-gray-700">
                <img
                  src={image.image_url}
                  alt={image.alt_text || image.title}
                  className="w-full h-48 object-cover"
                />
              </div>

              {/* Content */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="text-sm font-medium text-white truncate flex-1">
                    {image.title}
                  </h3>
                  {image.is_featured && (
                    <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                      ⭐
                    </span>
                  )}
                </div>

                {image.category && (
                  <p className="text-xs text-gray-400 mb-2">{image.category.name}</p>
                )}

                {image.description && (
                  <p className="text-xs text-gray-300 mb-3 line-clamp-2">
                    {image.description}
                  </p>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleToggleFeatured(image)}
                      className={`text-xs px-2 py-1 rounded ${
                        image.is_featured
                          ? 'bg-amber-600 text-white hover:bg-amber-700'
                          : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                      }`}
                    >
                      {image.is_featured ? 'Unfeature' : 'Feature'}
                    </button>
                    <button
                      onClick={() => setEditingImage(image)}
                      className="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Edit
                    </button>
                  </div>
                  <button
                    onClick={() => setShowDeleteConfirm(image.id)}
                    className="text-xs px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Edit Modal */}
      {editingImage && (
        <ImageEditModal
          image={editingImage}
          categories={categories}
          onSave={handleUpdateImage}
          onCancel={() => setEditingImage(null)}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <DeleteConfirmModal
          onConfirm={() => handleDeleteImage(showDeleteConfirm)}
          onCancel={() => setShowDeleteConfirm(null)}
        />
      )}
    </div>
  );
};

// Edit Modal Component
const ImageEditModal: React.FC<{
  image: GalleryImage;
  categories: GalleryCategory[];
  onSave: (data: any) => void;
  onCancel: () => void;
}> = ({ image, categories, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    title: image.title,
    description: image.description || '',
    category_id: image.category_id || '',
    alt_text: image.alt_text || '',
    is_featured: image.is_featured
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ id: image.id, ...formData });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md border border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4">Edit Image</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Title</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-amber-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-amber-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Category</label>
            <select
              value={formData.category_id}
              onChange={(e) => setFormData(prev => ({ ...prev, category_id: e.target.value }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-amber-500"
            >
              <option value="">No Category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_featured}
                onChange={(e) => setFormData(prev => ({ ...prev, is_featured: e.target.checked }))}
                className="rounded border-gray-600 text-amber-600 shadow-sm focus:border-amber-500 focus:ring-amber-500"
              />
              <span className="ml-2 text-sm text-gray-300">Featured image</span>
            </label>
          </div>

          <div className="flex items-center justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700"
            >
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Delete Confirmation Modal
const DeleteConfirmModal: React.FC<{
  onConfirm: () => void;
  onCancel: () => void;
}> = ({ onConfirm, onCancel }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-sm border border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4">Delete Image</h3>
        <p className="text-gray-300 mb-6">
          Are you sure you want to delete this image? This action cannot be undone.
        </p>
        
        <div className="flex items-center justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};
