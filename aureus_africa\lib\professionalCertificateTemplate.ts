// Professional Aureus Alliance Holdings Certificate Template
// Matches the official gold-bordered certificate design

import { CertificateData, CertificateTemplate } from './certificateTemplate';

export const PROFESSIONAL_CERTIFICATE_TEMPLATE: CertificateTemplate = {
  width: 1200,
  height: 850,
  dpi: 300,
  backgroundColor: '#FFFFFF',
  backgroundImage: '/certificate-background.png', // Optional background image
  
  elements: [
    // Certificate Number (Top Left)
    {
      type: 'text',
      id: 'cert_number_label',
      x: 50,
      y: 50,
      content: 'CERTIFICATE NO',
      fontSize: 12,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'left',
    },
    {
      type: 'text',
      id: 'cert_number',
      x: 50,
      y: 70,
      content: '',
      fontSize: 14,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'left',
      dynamic: true,
      dataField: 'certificateNumber',
    },

    // Number of Shares (Top Right)
    {
      type: 'text',
      id: 'shares_label',
      x: 1150,
      y: 50,
      content: 'NO OF SHARES',
      fontSize: 12,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'right',
    },
    {
      type: 'text',
      id: 'shares_count',
      x: 1150,
      y: 70,
      content: '',
      fontSize: 14,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'right',
      dynamic: true,
      dataField: 'sharesQuantity',
    },

    // Main Title
    {
      type: 'text',
      id: 'main_title',
      x: 600,
      y: 150,
      content: 'SHARE CERTIFICATE',
      fontSize: 48,
      fontFamily: 'serif',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'center',
    },

    // Company Name
    {
      type: 'text',
      id: 'company_name',
      x: 600,
      y: 200,
      content: 'AUREUS ALLIANCE HOLDINGS',
      fontSize: 24,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'center',
    },

    // Incorporation Details
    {
      type: 'text',
      id: 'incorporation',
      x: 600,
      y: 230,
      content: '(Incorporated in the Republic of South Africa)',
      fontSize: 14,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'center',
    },

    // Registration Details
    {
      type: 'text',
      id: 'registration',
      x: 600,
      y: 250,
      content: 'REG NO. 2025 / 368711 / 07',
      fontSize: 14,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'center',
    },

    // Registered Office Label
    {
      type: 'text',
      id: 'reg_office_label',
      x: 300,
      y: 300,
      content: 'Registered Office:',
      fontSize: 12,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'left',
    },

    // Postal Address Label
    {
      type: 'text',
      id: 'postal_label',
      x: 700,
      y: 300,
      content: 'Postal Address:',
      fontSize: 12,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'left',
    },

    // Address Details
    {
      type: 'text',
      id: 'address_details',
      x: 300,
      y: 320,
      content: '1848 Mees Avenue\nRandpark Ridge\nRandburg\nGauteng, 2194\nSouth Africa',
      fontSize: 11,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'left',
      lineHeight: 1.4,
    },

    // Postal Address Details
    {
      type: 'text',
      id: 'postal_details',
      x: 700,
      y: 320,
      content: '1848 Mees Avenue\nRandpark Ridge\nRandburg\nGauteng, 2194\nSouth Africa',
      fontSize: 11,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'left',
      lineHeight: 1.4,
    },

    // Main Certificate Text
    {
      type: 'text',
      id: 'main_text',
      x: 600,
      y: 450,
      content: 'This is to certify that the undermentioned is the registered proprietor of fully paid-up shares as shown below in the capital of the above Company, subject to the memorandum of incorporation',
      fontSize: 14,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'center',
      maxWidth: 800,
      lineHeight: 1.5,
    },

    // Table Headers
    {
      type: 'text',
      id: 'name_header',
      x: 100,
      y: 520,
      content: 'NAME AND ADDRESS',
      fontSize: 12,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#FFFFFF',
      alignment: 'center',
      backgroundColor: '#D4AF37',
    },

    // User Name (Dynamic)
    {
      type: 'text',
      id: 'user_name',
      x: 100,
      y: 550,
      content: '',
      fontSize: 14,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'left',
      dynamic: true,
      dataField: 'userFullName',
    },

    // User Address (Dynamic)
    {
      type: 'text',
      id: 'user_address',
      x: 100,
      y: 580,
      content: '',
      fontSize: 12,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'left',
      dynamic: true,
      dataField: 'userAddress',
    },

    // Share Class
    {
      type: 'text',
      id: 'share_class',
      x: 400,
      y: 550,
      content: 'NO PAR VALUE SHARES',
      fontSize: 12,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'center',
    },

    // Reference Number
    {
      type: 'text',
      id: 'ref_number',
      x: 550,
      y: 550,
      content: '1-700',
      fontSize: 12,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'center',
    },

    // Issue Date (Dynamic)
    {
      type: 'text',
      id: 'issue_date',
      x: 650,
      y: 550,
      content: '',
      fontSize: 12,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'center',
      dynamic: true,
      dataField: 'issueDate',
    },

    // Certificate Number in Table
    {
      type: 'text',
      id: 'cert_number_table',
      x: 750,
      y: 550,
      content: '',
      fontSize: 12,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'center',
      dynamic: true,
      dataField: 'certificateNumber',
    },

    // Number of Shares in Table
    {
      type: 'text',
      id: 'shares_table',
      x: 900,
      y: 550,
      content: '',
      fontSize: 12,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'center',
      dynamic: true,
      dataField: 'sharesQuantity',
    },

    // Company Logo/Seal Area
    {
      type: 'text',
      id: 'aureus_logo',
      x: 600,
      y: 680,
      content: 'AUREUS\nALLIANCE HOLDINGS',
      fontSize: 24,
      fontFamily: 'serif',
      fontWeight: 'bold',
      color: '#D4AF37',
      alignment: 'center',
      lineHeight: 1.2,
    },

    // CEO Signatures
    {
      type: 'text',
      id: 'ceo1_name',
      x: 250,
      y: 750,
      content: 'Mr DC James',
      fontSize: 12,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'center',
    },
    {
      type: 'text',
      id: 'ceo1_title',
      x: 250,
      y: 770,
      content: 'C.E.O AUREUS ALLIANCE HOLDINGS',
      fontSize: 10,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'center',
    },

    {
      type: 'text',
      id: 'ceo2_name',
      x: 950,
      y: 750,
      content: 'Mr JP Rademeyer',
      fontSize: 12,
      fontFamily: 'Arial',
      fontWeight: 'bold',
      color: '#000000',
      alignment: 'center',
    },
    {
      type: 'text',
      id: 'ceo2_title',
      x: 950,
      y: 770,
      content: 'C.E.O SMART UNITED NETWORK',
      fontSize: 10,
      fontFamily: 'Arial',
      color: '#000000',
      alignment: 'center',
    },
  ],

  securityElements: [
    {
      type: 'watermark',
      id: 'security_watermark',
      x: 600,
      y: 425,
      opacity: 0.1,
      content: 'AUREUS ALLIANCE HOLDINGS',
    },
  ],

  qrCode: {
    x: 1050,
    y: 750,
    size: 80,
    errorCorrectionLevel: 'M',
    margin: 4,
  },
};

// Helper function to format certificate data for the professional template
export const formatCertificateDataForProfessional = (data: CertificateData) => {
  return {
    ...data,
    issueDate: new Date(data.issueDate).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }),
    sharesQuantity: data.sharesQuantity.toLocaleString(),
  };
};
