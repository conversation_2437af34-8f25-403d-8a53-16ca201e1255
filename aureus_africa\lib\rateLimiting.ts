/**
 * AUTHENTICATION RATE LIMITING SYSTEM
 * 
 * This module provides rate limiting for authentication endpoints
 * to prevent brute force attacks while preserving bot functionality.
 */

interface RateLimitConfig {
  windowMs: number;
  maxAttempts: number;
  blockDurationMs: number;
  skipBotRequests: boolean;
}

interface RateLimitEntry {
  attempts: number;
  firstAttempt: number;
  lastAttempt: number;
  blocked: boolean;
  blockUntil?: number;
}

class AuthRateLimiter {
  private attempts: Map<string, RateLimitEntry> = new Map();
  private config: RateLimitConfig;

  constructor(config: Partial<RateLimitConfig> = {}) {
    this.config = {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxAttempts: 5, // 5 attempts per window
      blockDurationMs: 30 * 60 * 1000, // 30 minutes block
      skipBotRequests: true, // Skip rate limiting for bot
      ...config
    };

    // Clean up old entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  /**
   * Check if a request should be rate limited
   */
  checkRateLimit(identifier: string, isBotRequest: boolean = false): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
  } {
    // Skip rate limiting for bot requests
    if (isBotRequest && this.config.skipBotRequests) {
      return {
        allowed: true,
        remaining: this.config.maxAttempts,
        resetTime: Date.now() + this.config.windowMs
      };
    }

    const now = Date.now();
    const entry = this.attempts.get(identifier);

    // Check if currently blocked
    if (entry?.blocked && entry.blockUntil && now < entry.blockUntil) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.blockUntil,
        retryAfter: Math.ceil((entry.blockUntil - now) / 1000)
      };
    }

    // If no entry or window expired, create/reset entry
    if (!entry || (now - entry.firstAttempt) > this.config.windowMs) {
      this.attempts.set(identifier, {
        attempts: 1,
        firstAttempt: now,
        lastAttempt: now,
        blocked: false
      });

      return {
        allowed: true,
        remaining: this.config.maxAttempts - 1,
        resetTime: now + this.config.windowMs
      };
    }

    // Increment attempts
    entry.attempts++;
    entry.lastAttempt = now;

    // Check if limit exceeded
    if (entry.attempts > this.config.maxAttempts) {
      entry.blocked = true;
      entry.blockUntil = now + this.config.blockDurationMs;

      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.blockUntil,
        retryAfter: Math.ceil(this.config.blockDurationMs / 1000)
      };
    }

    return {
      allowed: true,
      remaining: this.config.maxAttempts - entry.attempts,
      resetTime: entry.firstAttempt + this.config.windowMs
    };
  }

  /**
   * Record a successful authentication (resets rate limit)
   */
  recordSuccess(identifier: string): void {
    this.attempts.delete(identifier);
  }

  /**
   * Record a failed authentication attempt
   */
  recordFailure(identifier: string, isBotRequest: boolean = false): {
    allowed: boolean;
    remaining: number;
    retryAfter?: number;
  } {
    const result = this.checkRateLimit(identifier, isBotRequest);
    
    // Log the failed attempt for monitoring
    console.log(`🚫 Failed auth attempt for ${identifier}: ${result.remaining} attempts remaining`);
    
    return result;
  }

  /**
   * Get current status for an identifier
   */
  getStatus(identifier: string): {
    attempts: number;
    remaining: number;
    blocked: boolean;
    resetTime?: number;
  } {
    const entry = this.attempts.get(identifier);
    
    if (!entry) {
      return {
        attempts: 0,
        remaining: this.config.maxAttempts,
        blocked: false
      };
    }

    const now = Date.now();
    const windowExpired = (now - entry.firstAttempt) > this.config.windowMs;
    const blockExpired = entry.blockUntil && now >= entry.blockUntil;

    if (windowExpired || blockExpired) {
      this.attempts.delete(identifier);
      return {
        attempts: 0,
        remaining: this.config.maxAttempts,
        blocked: false
      };
    }

    return {
      attempts: entry.attempts,
      remaining: Math.max(0, this.config.maxAttempts - entry.attempts),
      blocked: entry.blocked,
      resetTime: entry.blockUntil || (entry.firstAttempt + this.config.windowMs)
    };
  }

  /**
   * Manually block an identifier (for suspicious activity)
   */
  blockIdentifier(identifier: string, durationMs?: number): void {
    const blockDuration = durationMs || this.config.blockDurationMs;
    const now = Date.now();

    this.attempts.set(identifier, {
      attempts: this.config.maxAttempts + 1,
      firstAttempt: now,
      lastAttempt: now,
      blocked: true,
      blockUntil: now + blockDuration
    });

    console.log(`🔒 Manually blocked ${identifier} for ${blockDuration / 1000} seconds`);
  }

  /**
   * Unblock an identifier
   */
  unblockIdentifier(identifier: string): void {
    this.attempts.delete(identifier);
    console.log(`🔓 Unblocked ${identifier}`);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [identifier, entry] of this.attempts.entries()) {
      const windowExpired = (now - entry.firstAttempt) > this.config.windowMs;
      const blockExpired = entry.blockUntil && now >= entry.blockUntil;

      if (windowExpired || blockExpired) {
        this.attempts.delete(identifier);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} expired rate limit entries`);
    }
  }

  /**
   * Get statistics
   */
  getStats(): {
    totalEntries: number;
    blockedEntries: number;
    activeEntries: number;
  } {
    const now = Date.now();
    let blocked = 0;
    let active = 0;

    for (const entry of this.attempts.values()) {
      const windowExpired = (now - entry.firstAttempt) > this.config.windowMs;
      const blockExpired = entry.blockUntil && now >= entry.blockUntil;

      if (!windowExpired && !blockExpired) {
        active++;
        if (entry.blocked) {
          blocked++;
        }
      }
    }

    return {
      totalEntries: this.attempts.size,
      blockedEntries: blocked,
      activeEntries: active
    };
  }
}

// Create singleton instances for different types of rate limiting
export const authRateLimiter = new AuthRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 5, // 5 attempts per window
  blockDurationMs: 30 * 60 * 1000, // 30 minutes block
  skipBotRequests: true
});

export const registrationRateLimiter = new AuthRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxAttempts: 3, // 3 registration attempts per hour
  blockDurationMs: 2 * 60 * 60 * 1000, // 2 hours block
  skipBotRequests: true
});

export const passwordResetRateLimiter = new AuthRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxAttempts: 3, // 3 password reset attempts per hour
  blockDurationMs: 60 * 60 * 1000, // 1 hour block
  skipBotRequests: false // Don't skip for password reset
});

/**
 * Middleware function for Express/Next.js
 */
export const createRateLimitMiddleware = (limiter: AuthRateLimiter) => {
  return (req: any, res: any, next: any) => {
    // Get identifier (IP address or user ID)
    const identifier = req.ip || req.connection.remoteAddress || 'unknown';
    
    // Check if this is a bot request (has service role authorization)
    const isBotRequest = req.headers.authorization?.includes('service_role') || 
                        req.headers['user-agent']?.includes('aureus-bot');

    const result = limiter.checkRateLimit(identifier, isBotRequest);

    // Add rate limit headers
    res.setHeader('X-RateLimit-Limit', limiter['config'].maxAttempts);
    res.setHeader('X-RateLimit-Remaining', result.remaining);
    res.setHeader('X-RateLimit-Reset', new Date(result.resetTime).toISOString());

    if (!result.allowed) {
      res.setHeader('Retry-After', result.retryAfter || 0);
      return res.status(429).json({
        error: 'Too many attempts',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: result.retryAfter
      });
    }

    // Store limiter in request for later use
    req.rateLimiter = limiter;
    req.rateLimitIdentifier = identifier;
    req.isBotRequest = isBotRequest;

    next();
  };
};

/**
 * Helper function to get client identifier
 */
export const getClientIdentifier = (req: any): string => {
  // Try to get user ID first (more specific)
  if (req.user?.id) {
    return `user:${req.user.id}`;
  }

  // Fall back to IP address
  const ip = req.ip || 
            req.connection.remoteAddress || 
            req.headers['x-forwarded-for']?.split(',')[0] ||
            'unknown';

  return `ip:${ip}`;
};

/**
 * Test the rate limiting system
 */
export const testRateLimiting = (): boolean => {
  console.log('🧪 Testing rate limiting system...');

  const testLimiter = new AuthRateLimiter({
    windowMs: 1000, // 1 second for testing
    maxAttempts: 3,
    blockDurationMs: 2000, // 2 seconds block
    skipBotRequests: true
  });

  const testId = 'test-user';

  // Test normal requests
  for (let i = 1; i <= 3; i++) {
    const result = testLimiter.checkRateLimit(testId);
    if (!result.allowed) {
      console.log('❌ Rate limiting test failed: blocked too early');
      return false;
    }
    console.log(`   ✅ Request ${i}: allowed, ${result.remaining} remaining`);
  }

  // Test rate limit exceeded
  const blockedResult = testLimiter.checkRateLimit(testId);
  if (blockedResult.allowed) {
    console.log('❌ Rate limiting test failed: should be blocked');
    return false;
  }
  console.log('   ✅ Request 4: blocked as expected');

  // Test bot request bypass
  const botResult = testLimiter.checkRateLimit(testId, true);
  if (!botResult.allowed) {
    console.log('❌ Rate limiting test failed: bot request should be allowed');
    return false;
  }
  console.log('   ✅ Bot request: allowed (bypassed rate limit)');

  console.log('✅ Rate limiting test passed');
  return true;
};

export default authRateLimiter;
