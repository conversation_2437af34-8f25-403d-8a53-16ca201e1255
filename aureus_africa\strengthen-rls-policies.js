#!/usr/bin/env node

/**
 * STRENGTHEN RLS POLICIES
 * 
 * This script strengthens the RLS policies to ensure
 * financial data is properly protected.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

class RLSPolicyStrengthener {
  constructor() {
    this.strengthenedPolicies = [];
    this.errors = [];
  }

  async strengthenRLSPolicies() {
    console.log('🔒 STRENGTHENING RLS POLICIES');
    console.log('=============================\n');
    console.log('🛡️ Implementing stronger financial data protection');
    console.log('🚨 Blocking unauthorized access completely\n');

    try {
      await this.strengthenSharePurchasesRLS();
      await this.strengthenCommissionBalancesRLS();
      await this.strengthenCommissionTransactionsRLS();
      await this.strengthenPaymentTransactionsRLS();
      await this.testStrengthened();
      
      this.generateStrengtheningReport();
      
    } catch (error) {
      console.error('❌ RLS strengthening failed:', error);
    }
  }

  async strengthenSharePurchasesRLS() {
    console.log('📊 Strengthening Share Purchases RLS');
    console.log('====================================');

    try {
      // Drop existing policy and create stronger one
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Drop existing policy
          DROP POLICY IF EXISTS "safe_share_purchases_policy" ON public.aureus_share_purchases;
          
          -- Create stronger policy that blocks anonymous access
          CREATE POLICY "strong_share_purchases_policy" ON public.aureus_share_purchases
            FOR ALL USING (
              -- Only allow if user is authenticated AND (owns the record OR is admin OR service role)
              (
                auth.uid() IS NOT NULL AND (
                  user_id = (auth.jwt() ->> 'sub')::integer OR
                  EXISTS (
                    SELECT 1 FROM admin_users 
                    WHERE user_id = (auth.jwt() ->> 'sub')::integer 
                    AND is_active = true
                  )
                )
              ) OR
              -- Allow service role for bot operations
              current_setting('role') = 'service_role'
            );
        `
      });

      if (error) {
        console.log('   ❌ Share purchases RLS strengthening failed:', error.message);
        this.errors.push(`Share purchases RLS: ${error.message}`);
      } else {
        console.log('   ✅ Share purchases RLS strengthened');
        this.strengthenedPolicies.push('aureus_share_purchases');
      }

    } catch (error) {
      console.log('   ❌ Share purchases RLS error:', error.message);
      this.errors.push(`Share purchases RLS error: ${error.message}`);
    }
  }

  async strengthenCommissionBalancesRLS() {
    console.log('\n💰 Strengthening Commission Balances RLS');
    console.log('========================================');

    try {
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Drop existing policy
          DROP POLICY IF EXISTS "safe_commission_balances_policy" ON public.commission_balances;
          
          -- Create stronger policy
          CREATE POLICY "strong_commission_balances_policy" ON public.commission_balances
            FOR ALL USING (
              -- Only allow if user is authenticated AND (owns the record OR is admin OR service role)
              (
                auth.uid() IS NOT NULL AND (
                  user_id = (auth.jwt() ->> 'sub')::integer OR
                  EXISTS (
                    SELECT 1 FROM admin_users 
                    WHERE user_id = (auth.jwt() ->> 'sub')::integer 
                    AND is_active = true
                  )
                )
              ) OR
              -- Allow service role for bot operations
              current_setting('role') = 'service_role'
            );
        `
      });

      if (error) {
        console.log('   ❌ Commission balances RLS strengthening failed:', error.message);
        this.errors.push(`Commission balances RLS: ${error.message}`);
      } else {
        console.log('   ✅ Commission balances RLS strengthened');
        this.strengthenedPolicies.push('commission_balances');
      }

    } catch (error) {
      console.log('   ❌ Commission balances RLS error:', error.message);
      this.errors.push(`Commission balances RLS error: ${error.message}`);
    }
  }

  async strengthenCommissionTransactionsRLS() {
    console.log('\n💸 Strengthening Commission Transactions RLS');
    console.log('============================================');

    try {
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Drop existing policy
          DROP POLICY IF EXISTS "safe_commission_transactions_policy" ON public.commission_transactions;
          
          -- Create stronger policy
          CREATE POLICY "strong_commission_transactions_policy" ON public.commission_transactions
            FOR ALL USING (
              -- Only allow if user is authenticated AND (involved in transaction OR is admin OR service role)
              (
                auth.uid() IS NOT NULL AND (
                  referrer_id = (auth.jwt() ->> 'sub')::integer OR
                  referred_id = (auth.jwt() ->> 'sub')::integer OR
                  EXISTS (
                    SELECT 1 FROM admin_users 
                    WHERE user_id = (auth.jwt() ->> 'sub')::integer 
                    AND is_active = true
                  )
                )
              ) OR
              -- Allow service role for bot operations
              current_setting('role') = 'service_role'
            );
        `
      });

      if (error) {
        console.log('   ❌ Commission transactions RLS strengthening failed:', error.message);
        this.errors.push(`Commission transactions RLS: ${error.message}`);
      } else {
        console.log('   ✅ Commission transactions RLS strengthened');
        this.strengthenedPolicies.push('commission_transactions');
      }

    } catch (error) {
      console.log('   ❌ Commission transactions RLS error:', error.message);
      this.errors.push(`Commission transactions RLS error: ${error.message}`);
    }
  }

  async strengthenPaymentTransactionsRLS() {
    console.log('\n💳 Strengthening Payment Transactions RLS');
    console.log('=========================================');

    try {
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          -- Drop existing policy
          DROP POLICY IF EXISTS "safe_payment_transactions_policy" ON public.crypto_payment_transactions;
          
          -- Create stronger policy
          CREATE POLICY "strong_payment_transactions_policy" ON public.crypto_payment_transactions
            FOR ALL USING (
              -- Only allow if user is authenticated AND (owns the record OR is admin OR service role)
              (
                auth.uid() IS NOT NULL AND (
                  user_id = (auth.jwt() ->> 'sub')::integer OR
                  EXISTS (
                    SELECT 1 FROM admin_users 
                    WHERE user_id = (auth.jwt() ->> 'sub')::integer 
                    AND is_active = true
                  )
                )
              ) OR
              -- Allow service role for bot operations
              current_setting('role') = 'service_role'
            );
        `
      });

      if (error) {
        console.log('   ❌ Payment transactions RLS strengthening failed:', error.message);
        this.errors.push(`Payment transactions RLS: ${error.message}`);
      } else {
        console.log('   ✅ Payment transactions RLS strengthened');
        this.strengthenedPolicies.push('crypto_payment_transactions');
      }

    } catch (error) {
      console.log('   ❌ Payment transactions RLS error:', error.message);
      this.errors.push(`Payment transactions RLS error: ${error.message}`);
    }
  }

  async testStrengthened() {
    console.log('\n🧪 Testing Strengthened RLS Policies');
    console.log('====================================');

    try {
      // Test with anonymous client
      const anonClient = createClient(
        supabaseUrl, 
        process.env.VITE_SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      );

      const tables = this.strengthenedPolicies;
      let blockedCount = 0;
      let allowedCount = 0;

      for (const table of tables) {
        try {
          const { data, error } = await anonClient
            .from(table)
            .select('*')
            .limit(1);

          if (error) {
            console.log(`   ✅ ${table}: Anonymous access blocked`);
            blockedCount++;
          } else {
            console.log(`   ❌ ${table}: Anonymous access still allowed (${data?.length || 0} records)`);
            allowedCount++;
          }
        } catch (err) {
          console.log(`   ✅ ${table}: Access properly blocked`);
          blockedCount++;
        }
      }

      console.log(`\n   📊 Results: ${blockedCount} blocked, ${allowedCount} allowed`);

      if (allowedCount === 0) {
        console.log('   🎉 All anonymous access successfully blocked!');
      } else {
        console.log('   ⚠️ Some anonymous access still getting through');
      }

    } catch (error) {
      console.log('   ❌ Testing failed:', error.message);
    }
  }

  generateStrengtheningReport() {
    console.log('\n🔒 RLS STRENGTHENING REPORT');
    console.log('===========================');
    
    console.log(`✅ Policies Strengthened: ${this.strengthenedPolicies.length}`);
    console.log(`❌ Errors Encountered: ${this.errors.length}`);

    if (this.strengthenedPolicies.length > 0) {
      console.log('\n✅ STRENGTHENED POLICIES:');
      this.strengthenedPolicies.forEach((policy, index) => {
        console.log(`${index + 1}. ${policy}`);
      });
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS ENCOUNTERED:');
      this.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    if (this.strengthenedPolicies.length > 0 && this.errors.length === 0) {
      console.log('\n🎉 RLS STRENGTHENING SUCCESSFUL!');
      console.log('✅ Financial data now has stronger protection');
      console.log('✅ Anonymous access blocked on critical tables');
      console.log('✅ Service role access preserved for bot operations');
      console.log('✅ Authenticated users can only see their own data');
      
      console.log('\n🛡️ BUSINESS PROTECTION ENHANCED:');
      console.log('• Commission balances: SECURED');
      console.log('• Share purchases: SECURED');
      console.log('• Payment transactions: SECURED');
      console.log('• Commission transactions: SECURED');
      
      console.log('\n📋 NEXT STEPS:');
      console.log('1. Test user authentication flows');
      console.log('2. Verify admin panel still works');
      console.log('3. Test Telegram bot operations');
      console.log('4. Monitor for any functionality issues');
      
    } else {
      console.log('\n⚠️ RLS STRENGTHENING COMPLETED WITH ISSUES');
      console.log('Some policies may need manual review.');
    }

    // Log the strengthening
    supabase
      .from('admin_audit_logs')
      .insert({
        admin_email: 'security_system',
        action: 'RLS_POLICIES_STRENGTHENED',
        target_type: 'database_security',
        target_id: 'financial_tables',
        metadata: {
          strengthened_policies: this.strengthenedPolicies,
          errors: this.errors,
          strengthening_date: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      })
      .then(() => console.log('\n📋 RLS strengthening logged to audit trail'))
      .catch(err => console.log('\n⚠️ Failed to log RLS strengthening:', err.message));
  }
}

// Strengthen the RLS policies
const strengthener = new RLSPolicyStrengthener();
strengthener.strengthenRLSPolicies().catch(console.error);
