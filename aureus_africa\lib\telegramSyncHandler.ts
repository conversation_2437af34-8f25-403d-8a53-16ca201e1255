import { supabase } from './supabase';
import { AccountSyncService } from './accountSyncService';

/**
 * Telegram Bot Sync Handler
 * Handles /sync command and account linking from Telegram side
 */
export class TelegramSyncHandler {
  
  /**
   * Generate a secure sync token for web account linking
   */
  static generateSyncToken(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Create sync request from Telegram bot
   */
  static async createSyncRequest(telegramId: number, telegramUsername?: string): Promise<string | null> {
    try {
      const syncToken = this.generateSyncToken();
      
      // Store sync request in database
      const { error } = await supabase
        .from('telegram_sync_requests')
        .insert({
          telegram_id: telegramId,
          sync_token: syncToken,
          telegram_username: telegramUsername,
          status: 'pending',
          expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes
        });

      if (error) {
        console.error('Error creating sync request:', error);
        return null;
      }

      return syncToken;
    } catch (error) {
      console.error('Sync request creation failed:', error);
      return null;
    }
  }

  /**
   * Verify and process sync token from web
   */
  static async processSyncToken(syncToken: string, webUserId: string): Promise<boolean> {
    try {
      // Get sync request
      const { data: syncRequest, error: requestError } = await supabase
        .from('telegram_sync_requests')
        .select('*')
        .eq('sync_token', syncToken)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (requestError || !syncRequest) {
        console.error('Invalid or expired sync token');
        return false;
      }

      // Check if Telegram user exists
      const { data: telegramUser, error: telegramError } = await supabase
        .from('telegram_users')
        .select('*, users:user_id (*)')
        .eq('telegram_id', syncRequest.telegram_id)
        .single();

      if (telegramError || !telegramUser) {
        console.error('Telegram user not found');
        return false;
      }

      // Get web user
      const { data: webUser, error: webUserError } = await supabase
        .from('users')
        .select('*')
        .eq('auth_user_id', webUserId)
        .single();

      if (webUserError || !webUser) {
        console.error('Web user not found');
        return false;
      }

      // Create account link
      const linkId = await AccountSyncService.createAccountLink(
        webUserId,
        webUser.id,
        syncRequest.telegram_id,
        'telegram_to_web'
      );

      if (!linkId) {
        return false;
      }

      // Auto-confirm the link since user initiated from both sides
      await AccountSyncService.confirmAccountLink(linkId, webUser.id);

      // Mark sync request as completed
      await supabase
        .from('telegram_sync_requests')
        .update({ 
          status: 'completed',
          completed_at: new Date().toISOString(),
          linked_web_user_id: webUserId
        })
        .eq('sync_token', syncToken);

      // Create success notifications
      await AccountSyncService.createSyncNotification(
        webUser.id,
        'sync_completed',
        'Telegram Account Linked!',
        'Your Telegram account has been successfully linked. All your bot data is now available on the web dashboard.',
        '/dashboard',
        'View Dashboard'
      );

      return true;
    } catch (error) {
      console.error('Sync token processing failed:', error);
      return false;
    }
  }

  /**
   * Get sync instructions for Telegram bot
   */
  static getSyncInstructions(syncToken: string): string {
    const webUrl = import.meta.env.VITE_WEB_URL || 'http://localhost:8000';
    const syncUrl = `${webUrl}/sync?token=${syncToken}`;
    
    return `🔗 *Account Sync Instructions*

To link your Telegram account with the web dashboard:

1️⃣ Open this link in your browser:
${syncUrl}

2️⃣ Login to your web account

3️⃣ Confirm the account linking

⏰ This link expires in 10 minutes for security.

Once linked, you'll be able to:
✅ View your Telegram purchases on the web
✅ Access your referral dashboard online
✅ Check commission balances from anywhere
✅ Download share certificates

Need help? Contact our support team.`;
  }

  /**
   * Check if user has pending sync requests
   */
  static async hasPendingSyncRequest(telegramId: number): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('telegram_sync_requests')
        .select('id')
        .eq('telegram_id', telegramId)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .limit(1);

      return !error && data && data.length > 0;
    } catch (error) {
      console.error('Error checking pending sync requests:', error);
      return false;
    }
  }

  /**
   * Clean up expired sync requests
   */
  static async cleanupExpiredRequests(): Promise<void> {
    try {
      await supabase
        .from('telegram_sync_requests')
        .update({ status: 'expired' })
        .eq('status', 'pending')
        .lt('expires_at', new Date().toISOString());
    } catch (error) {
      console.error('Error cleaning up expired sync requests:', error);
    }
  }
}
