import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

// Use service role key for admin operations
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function resetUser4Password() {
  try {
    console.log('🔐 Resetting password for User ID 4...\n');

    // New password for user ID 4
    const newPassword = 'Demo2025!';
    
    console.log('📋 Password Details:');
    console.log('• User ID: 4');
    console.log('• New Password:', newPassword);
    console.log('• Password will be hashed with bcrypt\n');

    // Hash the new password
    console.log('🔒 Hashing password...');
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    console.log('✅ Password hashed successfully\n');

    // Update the password in the database
    console.log('💾 Updating password in database...');
    const { data, error } = await supabase
      .from('users')
      .update({ 
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('id', 4)
      .select('id, email, username, full_name');

    if (error) {
      console.error('❌ Error updating password:', error);
      return;
    }

    if (!data || data.length === 0) {
      console.error('❌ User ID 4 not found in database');
      return;
    }

    const user = data[0];
    console.log('✅ Password updated successfully!\n');

    console.log('👤 User Details:');
    console.log('• ID:', user.id);
    console.log('• Email:', user.email);
    console.log('• Username:', user.username);
    console.log('• Full Name:', user.full_name);
    console.log('\n🔑 Login Credentials:');
    console.log('• Email:', user.email);
    console.log('• Password:', newPassword);
    console.log('\n🎯 Demo User Access:');
    console.log('• This user has FULL ACCESS to all features');
    console.log('• Can use Buy Gold Shares functionality');
    console.log('• Can access Marketing Toolkit');
    console.log('• No demo mode restrictions apply');
    console.log('\n🚀 Ready for tomorrow\'s demonstration!');

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the script
resetUser4Password();
