import { supabase } from '../supabase';

export interface PhaseData {
  id: number;
  phase_number: number;
  phase_name: string;
  price_per_share: number;
  total_shares_available: number;
  shares_sold: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
  created_at: string;
  updated_at: string;
}

export interface PhaseTransitionResult {
  success: boolean;
  previousPhase?: PhaseData;
  newPhase?: PhaseData;
  transitionTime: string;
  affectedUsers: number;
  commissionsProcessed: number;
  error?: string;
}

export interface CommissionDistribution {
  userId: number;
  usdtAmount: number;
  sharesAmount: number;
  level: number;
  referralChain: number[];
}

export class PhaseAutomationService {
  private static instance: PhaseAutomationService;
  private transitionInProgress = false;
  private automationEnabled = true;

  static getInstance(): PhaseAutomationService {
    if (!PhaseAutomationService.instance) {
      PhaseAutomationService.instance = new PhaseAutomationService();
    }
    return PhaseAutomationService.instance;
  }

  /**
   * Check if current phase should transition to next phase
   */
  async checkPhaseTransition(): Promise<boolean> {
    try {
      if (!this.automationEnabled || this.transitionInProgress) {
        return false;
      }

      const currentPhase = await this.getCurrentPhase();
      if (!currentPhase) {
        console.error('No active phase found');
        return false;
      }

      // Check if phase is sold out
      const soldOutPercentage = (currentPhase.shares_sold / currentPhase.total_shares_available) * 100;
      
      if (soldOutPercentage >= 100) {
        console.log(`🔄 Phase ${currentPhase.phase_number} is sold out (${soldOutPercentage.toFixed(1)}%). Initiating transition...`);
        await this.initiatePhaseTransition(currentPhase);
        return true;
      }

      // Check for time-based transitions (if end_date is set)
      if (currentPhase.end_date) {
        const endDate = new Date(currentPhase.end_date);
        const now = new Date();
        
        if (now >= endDate) {
          console.log(`⏰ Phase ${currentPhase.phase_number} has reached end date. Initiating transition...`);
          await this.initiatePhaseTransition(currentPhase);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking phase transition:', error);
      return false;
    }
  }

  /**
   * Get current active phase
   */
  async getCurrentPhase(): Promise<PhaseData | null> {
    try {
      const { data, error } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error getting current phase:', error);
      return null;
    }
  }

  /**
   * Get next phase in sequence
   */
  async getNextPhase(currentPhaseNumber: number): Promise<PhaseData | null> {
    try {
      const { data, error } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('phase_number', currentPhaseNumber + 1)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error getting next phase:', error);
      return null;
    }
  }

  /**
   * Initiate phase transition with full automation
   */
  async initiatePhaseTransition(currentPhase: PhaseData): Promise<PhaseTransitionResult> {
    if (this.transitionInProgress) {
      return {
        success: false,
        error: 'Phase transition already in progress',
        transitionTime: new Date().toISOString(),
        affectedUsers: 0,
        commissionsProcessed: 0
      };
    }

    this.transitionInProgress = true;

    try {
      console.log(`🚀 Starting automated phase transition from Phase ${currentPhase.phase_number}`);

      // Get next phase
      const nextPhase = await this.getNextPhase(currentPhase.phase_number);
      if (!nextPhase) {
        throw new Error(`No next phase found after Phase ${currentPhase.phase_number}`);
      }

      // Start database transaction
      const { data: transactionResult, error: transactionError } = await supabase.rpc(
        'execute_phase_transition',
        {
          current_phase_id: currentPhase.id,
          next_phase_id: nextPhase.id
        }
      );

      if (transactionError) {
        throw transactionError;
      }

      // Process commission distributions
      const commissionResult = await this.processPhaseTransitionCommissions(currentPhase, nextPhase);

      // Send notifications
      await this.sendPhaseTransitionNotifications(currentPhase, nextPhase);

      // Log transition
      await this.logPhaseTransition(currentPhase, nextPhase, commissionResult);

      console.log(`✅ Phase transition completed: ${currentPhase.phase_name} → ${nextPhase.phase_name}`);

      return {
        success: true,
        previousPhase: currentPhase,
        newPhase: nextPhase,
        transitionTime: new Date().toISOString(),
        affectedUsers: commissionResult.affectedUsers,
        commissionsProcessed: commissionResult.commissionsProcessed
      };

    } catch (error) {
      console.error('Error during phase transition:', error);
      return {
        success: false,
        error: error.message,
        transitionTime: new Date().toISOString(),
        affectedUsers: 0,
        commissionsProcessed: 0
      };
    } finally {
      this.transitionInProgress = false;
    }
  }

  /**
   * Process commission distributions for phase transition
   */
  async processPhaseTransitionCommissions(
    currentPhase: PhaseData, 
    nextPhase: PhaseData
  ): Promise<{ affectedUsers: number; commissionsProcessed: number }> {
    try {
      console.log('💰 Processing phase transition commissions...');

      // Get all users who made purchases in the current phase
      const { data: purchases, error: purchasesError } = await supabase
        .from('aureus_share_purchases')
        .select(`
          *,
          users!inner(id, username, referred_by)
        `)
        .eq('phase_id', currentPhase.id)
        .eq('status', 'active');

      if (purchasesError) {
        throw purchasesError;
      }

      let affectedUsers = 0;
      let commissionsProcessed = 0;

      // Process commissions for each purchase
      for (const purchase of purchases || []) {
        const commissions = await this.calculatePhaseTransitionCommissions(purchase, nextPhase);
        
        for (const commission of commissions) {
          await this.distributeCommission(commission);
          commissionsProcessed++;
        }
        
        affectedUsers++;
      }

      console.log(`✅ Processed ${commissionsProcessed} commissions for ${affectedUsers} users`);

      return { affectedUsers, commissionsProcessed };
    } catch (error) {
      console.error('Error processing phase transition commissions:', error);
      return { affectedUsers: 0, commissionsProcessed: 0 };
    }
  }

  /**
   * Calculate commission distributions for phase transition
   */
  async calculatePhaseTransitionCommissions(
    purchase: any,
    nextPhase: PhaseData
  ): Promise<CommissionDistribution[]> {
    const commissions: CommissionDistribution[] = [];
    
    try {
      // Get referral chain
      const referralChain = await this.getReferralChain(purchase.users.id);
      
      // Calculate base commission amounts (15% USDT + 15% Shares)
      const purchaseAmount = purchase.amount_usd;
      const baseUsdtCommission = purchaseAmount * 0.15; // 15% USDT
      const baseSharesValue = purchaseAmount * 0.15; // 15% in shares value
      const sharesAmount = Math.floor(baseSharesValue / nextPhase.price_per_share);

      // Distribute commissions through referral chain
      for (let level = 0; level < Math.min(referralChain.length, 5); level++) {
        const referrerId = referralChain[level];
        
        // Commission percentage decreases by level
        const levelMultiplier = this.getLevelMultiplier(level);
        
        commissions.push({
          userId: referrerId,
          usdtAmount: baseUsdtCommission * levelMultiplier,
          sharesAmount: Math.floor(sharesAmount * levelMultiplier),
          level: level + 1,
          referralChain: [...referralChain]
        });
      }

      return commissions;
    } catch (error) {
      console.error('Error calculating phase transition commissions:', error);
      return [];
    }
  }

  /**
   * Get referral chain for a user
   */
  async getReferralChain(userId: number): Promise<number[]> {
    const chain: number[] = [];
    let currentUserId = userId;

    try {
      for (let depth = 0; depth < 10; depth++) { // Max 10 levels to prevent infinite loops
        const { data: user, error } = await supabase
          .from('users')
          .select('referred_by')
          .eq('id', currentUserId)
          .single();

        if (error || !user?.referred_by) {
          break;
        }

        chain.push(user.referred_by);
        currentUserId = user.referred_by;
      }

      return chain;
    } catch (error) {
      console.error('Error getting referral chain:', error);
      return [];
    }
  }

  /**
   * Get commission multiplier by level
   */
  private getLevelMultiplier(level: number): number {
    const multipliers = [1.0, 0.5, 0.3, 0.2, 0.1]; // Level 1: 100%, Level 2: 50%, etc.
    return multipliers[level] || 0;
  }

  /**
   * Distribute commission to user
   */
  async distributeCommission(commission: CommissionDistribution): Promise<void> {
    try {
      // Add USDT commission
      if (commission.usdtAmount > 0) {
        await supabase.from('user_balances').upsert({
          user_id: commission.userId,
          balance_type: 'usdt_commission',
          amount: commission.usdtAmount,
          source: 'phase_transition',
          created_at: new Date().toISOString()
        });
      }

      // Add shares commission
      if (commission.sharesAmount > 0) {
        await supabase.from('user_balances').upsert({
          user_id: commission.userId,
          balance_type: 'shares_commission',
          amount: commission.sharesAmount,
          source: 'phase_transition',
          created_at: new Date().toISOString()
        });
      }

      // Log commission distribution
      await supabase.from('commission_distributions').insert({
        user_id: commission.userId,
        commission_type: 'phase_transition',
        usdt_amount: commission.usdtAmount,
        shares_amount: commission.sharesAmount,
        level: commission.level,
        referral_chain: commission.referralChain,
        distributed_at: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error distributing commission:', error);
    }
  }

  /**
   * Send notifications for phase transition
   */
  async sendPhaseTransitionNotifications(
    currentPhase: PhaseData,
    nextPhase: PhaseData
  ): Promise<void> {
    try {
      console.log('📢 Sending phase transition notifications...');

      // Get all active users
      const { data: users, error } = await supabase
        .from('users')
        .select('id, username, telegram_id, notification_preferences')
        .eq('is_active', true);

      if (error) {
        throw error;
      }

      // Send notifications to all users
      for (const user of users || []) {
        await this.sendUserPhaseTransitionNotification(user, currentPhase, nextPhase);
      }

      console.log(`✅ Sent phase transition notifications to ${users?.length || 0} users`);
    } catch (error) {
      console.error('Error sending phase transition notifications:', error);
    }
  }

  /**
   * Send phase transition notification to individual user
   */
  async sendUserPhaseTransitionNotification(
    user: any,
    currentPhase: PhaseData,
    nextPhase: PhaseData
  ): Promise<void> {
    try {
      const notification = {
        user_id: user.id,
        type: 'phase_transition',
        title: `🚀 New Phase Active: ${nextPhase.phase_name}`,
        message: `Phase ${currentPhase.phase_number} is now complete! Phase ${nextPhase.phase_number} is now active with shares at $${nextPhase.price_per_share} each. Don't miss out on this opportunity!`,
        data: {
          previous_phase: currentPhase,
          new_phase: nextPhase,
          price_increase: ((nextPhase.price_per_share - currentPhase.price_per_share) / currentPhase.price_per_share * 100).toFixed(1)
        },
        created_at: new Date().toISOString()
      };

      await supabase.from('user_notifications').insert(notification);

      // Send Telegram notification if user has Telegram ID
      if (user.telegram_id) {
        await this.sendTelegramPhaseNotification(user.telegram_id, currentPhase, nextPhase);
      }

    } catch (error) {
      console.error('Error sending user phase transition notification:', error);
    }
  }

  /**
   * Send Telegram notification for phase transition
   */
  async sendTelegramPhaseNotification(
    telegramId: number,
    currentPhase: PhaseData,
    nextPhase: PhaseData
  ): Promise<void> {
    try {
      // This would integrate with the Telegram bot
      // For now, we'll just log the notification
      console.log(`📱 Telegram notification sent to ${telegramId}: Phase ${currentPhase.phase_number} → ${nextPhase.phase_number}`);
    } catch (error) {
      console.error('Error sending Telegram phase notification:', error);
    }
  }

  /**
   * Log phase transition for audit purposes
   */
  async logPhaseTransition(
    currentPhase: PhaseData,
    nextPhase: PhaseData,
    commissionResult: { affectedUsers: number; commissionsProcessed: number }
  ): Promise<void> {
    try {
      await supabase.from('phase_transition_log').insert({
        previous_phase_id: currentPhase.id,
        new_phase_id: nextPhase.id,
        transition_type: 'automatic',
        shares_sold_in_previous: currentPhase.shares_sold,
        affected_users: commissionResult.affectedUsers,
        commissions_processed: commissionResult.commissionsProcessed,
        transition_completed_at: new Date().toISOString(),
        metadata: {
          previous_phase: currentPhase,
          new_phase: nextPhase,
          automation_enabled: this.automationEnabled
        }
      });

      console.log('📝 Phase transition logged successfully');
    } catch (error) {
      console.error('Error logging phase transition:', error);
    }
  }

  /**
   * Enable/disable automation
   */
  setAutomationEnabled(enabled: boolean): void {
    this.automationEnabled = enabled;
    console.log(`🔧 Phase automation ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get automation status
   */
  isAutomationEnabled(): boolean {
    return this.automationEnabled;
  }

  /**
   * Manual phase transition (admin override)
   */
  async manualPhaseTransition(currentPhaseId: number, nextPhaseId: number): Promise<PhaseTransitionResult> {
    try {
      const currentPhase = await this.getPhaseById(currentPhaseId);
      const nextPhase = await this.getPhaseById(nextPhaseId);

      if (!currentPhase || !nextPhase) {
        throw new Error('Invalid phase IDs provided');
      }

      console.log('🔧 Manual phase transition initiated');
      return await this.initiatePhaseTransition(currentPhase);
    } catch (error) {
      console.error('Error in manual phase transition:', error);
      return {
        success: false,
        error: error.message,
        transitionTime: new Date().toISOString(),
        affectedUsers: 0,
        commissionsProcessed: 0
      };
    }
  }

  /**
   * Get phase by ID
   */
  async getPhaseById(phaseId: number): Promise<PhaseData | null> {
    try {
      const { data, error } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('id', phaseId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error getting phase by ID:', error);
      return null;
    }
  }
}
