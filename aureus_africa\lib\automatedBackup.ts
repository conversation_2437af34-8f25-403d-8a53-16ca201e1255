/**
 * AUTOMATED BACKUP SYSTEM
 * 
 * Comprehensive backup solution with database backups, file backups,
 * verification, disaster recovery, and bot-safe operations.
 */

import { supabase } from './supabase';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

interface BackupConfig {
  enabled: boolean;
  schedules: {
    database: {
      daily: string; // cron format
      weekly: string;
      monthly: string;
    };
    files: {
      daily: string;
      weekly: string;
    };
  };
  retention: {
    daily: number; // days
    weekly: number; // weeks
    monthly: number; // months
  };
  storage: {
    local: boolean;
    cloud: boolean;
    encryption: boolean;
  };
  verification: {
    enabled: boolean;
    checksumAlgorithm: 'sha256' | 'md5';
    testRestore: boolean;
  };
}

interface BackupJob {
  id: string;
  type: 'database' | 'files' | 'configuration';
  schedule: 'daily' | 'weekly' | 'monthly' | 'manual';
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  size?: number;
  checksum?: string;
  location: string;
  metadata: any;
}

interface BackupVerification {
  backupId: string;
  verified: boolean;
  checksumMatch: boolean;
  restoreTest: boolean;
  errors: string[];
  verifiedAt: Date;
}

interface DisasterRecoveryPlan {
  priority: number;
  component: string;
  backupLocation: string;
  restoreSteps: string[];
  estimatedTime: string;
  dependencies: string[];
}

class AutomatedBackupSystem {
  private config: BackupConfig = {
    enabled: true,
    schedules: {
      database: {
        daily: '0 2 * * *',    // 2 AM daily
        weekly: '0 1 * * 0',   // 1 AM Sunday
        monthly: '0 0 1 * *'   // Midnight 1st of month
      },
      files: {
        daily: '0 3 * * *',    // 3 AM daily
        weekly: '0 2 * * 0'    // 2 AM Sunday
      }
    },
    retention: {
      daily: 7,    // 7 days
      weekly: 4,   // 4 weeks
      monthly: 12  // 12 months
    },
    storage: {
      local: true,
      cloud: true,
      encryption: true
    },
    verification: {
      enabled: true,
      checksumAlgorithm: 'sha256',
      testRestore: false // Disabled by default for safety
    }
  };

  private activeJobs: Map<string, BackupJob> = new Map();
  private backupDirectory = process.env.BACKUP_DIRECTORY || './backups';

  /**
   * Initialize backup system
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔄 Initializing automated backup system...');

      // Create backup directories
      await this.createBackupDirectories();

      // Load configuration
      await this.loadBackupConfiguration();

      // Schedule backup jobs
      await this.scheduleBackupJobs();

      // Start monitoring
      this.startBackupMonitoring();

      console.log('✅ Automated backup system initialized');

    } catch (error) {
      console.error('❌ Failed to initialize backup system:', error);
      throw error;
    }
  }

  /**
   * Perform database backup
   */
  async performDatabaseBackup(schedule: 'daily' | 'weekly' | 'monthly' | 'manual' = 'manual'): Promise<BackupJob> {
    const jobId = `db_backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const job: BackupJob = {
      id: jobId,
      type: 'database',
      schedule,
      status: 'pending',
      startTime: new Date(),
      location: '',
      metadata: {
        tables: [],
        rowCounts: {},
        botSafe: true
      }
    };

    this.activeJobs.set(jobId, job);

    try {
      console.log(`🗄️ Starting database backup: ${jobId}`);
      job.status = 'running';

      // Get all tables to backup
      const tables = await this.getDatabaseTables();
      job.metadata.tables = tables;

      // Create backup filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFilename = `database_backup_${schedule}_${timestamp}.sql`;
      const backupPath = path.join(this.backupDirectory, 'database', backupFilename);

      // Ensure directory exists
      await fs.promises.mkdir(path.dirname(backupPath), { recursive: true });

      // Perform backup for each table
      let backupContent = this.generateBackupHeader();
      let totalSize = 0;

      for (const table of tables) {
        console.log(`   📋 Backing up table: ${table}`);
        
        // Get table data (with bot-safe handling)
        const tableData = await this.backupTable(table);
        const tableBackup = this.generateTableBackup(table, tableData);
        
        backupContent += tableBackup;
        totalSize += tableBackup.length;
        
        job.metadata.rowCounts[table] = tableData.length;
      }

      // Add backup footer
      backupContent += this.generateBackupFooter();

      // Encrypt if enabled
      if (this.config.storage.encryption) {
        backupContent = await this.encryptBackup(backupContent);
      }

      // Write backup file
      await fs.promises.writeFile(backupPath, backupContent);

      // Calculate checksum
      const checksum = await this.calculateChecksum(backupPath);

      // Update job
      job.status = 'completed';
      job.endTime = new Date();
      job.size = totalSize;
      job.checksum = checksum;
      job.location = backupPath;

      // Log backup completion
      await this.logBackupEvent(jobId, 'DATABASE_BACKUP_COMPLETED', {
        tables: tables.length,
        totalRows: Object.values(job.metadata.rowCounts).reduce((a: any, b: any) => a + b, 0),
        size: totalSize,
        duration: job.endTime.getTime() - job.startTime.getTime()
      });

      // Verify backup if enabled
      if (this.config.verification.enabled) {
        await this.verifyBackup(job);
      }

      // Clean up old backups
      await this.cleanupOldBackups('database', schedule);

      console.log(`✅ Database backup completed: ${jobId}`);
      return job;

    } catch (error) {
      console.error(`❌ Database backup failed: ${jobId}`, error);
      job.status = 'failed';
      job.endTime = new Date();
      job.metadata.error = error.message;

      await this.logBackupEvent(jobId, 'DATABASE_BACKUP_FAILED', {
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Perform file backup
   */
  async performFileBackup(schedule: 'daily' | 'weekly' | 'manual' = 'manual'): Promise<BackupJob> {
    const jobId = `file_backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const job: BackupJob = {
      id: jobId,
      type: 'files',
      schedule,
      status: 'pending',
      startTime: new Date(),
      location: '',
      metadata: {
        files: [],
        directories: [],
        botSafe: true
      }
    };

    this.activeJobs.set(jobId, job);

    try {
      console.log(`📁 Starting file backup: ${jobId}`);
      job.status = 'running';

      // Define critical files and directories to backup
      const criticalPaths = [
        './lib',
        './components',
        './pages',
        './public',
        './.env.local',
        './package.json',
        './next.config.js',
        './tailwind.config.js',
        './tsconfig.json'
      ];

      // Create backup archive
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFilename = `files_backup_${schedule}_${timestamp}.tar.gz`;
      const backupPath = path.join(this.backupDirectory, 'files', backupFilename);

      // Ensure directory exists
      await fs.promises.mkdir(path.dirname(backupPath), { recursive: true });

      // Create archive (simplified - in production use proper archiving library)
      const archiveContent = await this.createFileArchive(criticalPaths);
      
      // Encrypt if enabled
      let finalContent = archiveContent;
      if (this.config.storage.encryption) {
        finalContent = await this.encryptBackup(archiveContent);
      }

      // Write backup file
      await fs.promises.writeFile(backupPath, finalContent);

      // Calculate checksum
      const checksum = await this.calculateChecksum(backupPath);

      // Update job
      job.status = 'completed';
      job.endTime = new Date();
      job.size = finalContent.length;
      job.checksum = checksum;
      job.location = backupPath;
      job.metadata.files = criticalPaths;

      // Log backup completion
      await this.logBackupEvent(jobId, 'FILE_BACKUP_COMPLETED', {
        files: criticalPaths.length,
        size: finalContent.length,
        duration: job.endTime.getTime() - job.startTime.getTime()
      });

      // Verify backup if enabled
      if (this.config.verification.enabled) {
        await this.verifyBackup(job);
      }

      // Clean up old backups
      await this.cleanupOldBackups('files', schedule);

      console.log(`✅ File backup completed: ${jobId}`);
      return job;

    } catch (error) {
      console.error(`❌ File backup failed: ${jobId}`, error);
      job.status = 'failed';
      job.endTime = new Date();
      job.metadata.error = error.message;

      await this.logBackupEvent(jobId, 'FILE_BACKUP_FAILED', {
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Verify backup integrity
   */
  async verifyBackup(job: BackupJob): Promise<BackupVerification> {
    try {
      console.log(`🔍 Verifying backup: ${job.id}`);

      const verification: BackupVerification = {
        backupId: job.id,
        verified: false,
        checksumMatch: false,
        restoreTest: false,
        errors: [],
        verifiedAt: new Date()
      };

      // Verify file exists
      if (!fs.existsSync(job.location)) {
        verification.errors.push('Backup file does not exist');
        return verification;
      }

      // Verify checksum
      const currentChecksum = await this.calculateChecksum(job.location);
      verification.checksumMatch = currentChecksum === job.checksum;
      
      if (!verification.checksumMatch) {
        verification.errors.push('Checksum mismatch - backup may be corrupted');
      }

      // Perform restore test if enabled (disabled by default for safety)
      if (this.config.verification.testRestore && job.type === 'database') {
        try {
          await this.performRestoreTest(job);
          verification.restoreTest = true;
        } catch (error) {
          verification.errors.push(`Restore test failed: ${error.message}`);
        }
      }

      verification.verified = verification.checksumMatch && verification.errors.length === 0;

      // Log verification result
      await this.logBackupEvent(job.id, 'BACKUP_VERIFIED', {
        verified: verification.verified,
        checksumMatch: verification.checksumMatch,
        errors: verification.errors
      });

      console.log(`✅ Backup verification completed: ${job.id} (${verification.verified ? 'PASSED' : 'FAILED'})`);
      return verification;

    } catch (error) {
      console.error(`❌ Backup verification failed: ${job.id}`, error);
      return {
        backupId: job.id,
        verified: false,
        checksumMatch: false,
        restoreTest: false,
        errors: [error.message],
        verifiedAt: new Date()
      };
    }
  }

  /**
   * Get backup status and statistics
   */
  async getBackupStatus(): Promise<{
    systemStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL';
    lastBackups: {
      database: Date | null;
      files: Date | null;
    };
    statistics: {
      totalBackups: number;
      successfulBackups: number;
      failedBackups: number;
      totalSize: number;
    };
    activeJobs: BackupJob[];
    recentJobs: BackupJob[];
  }> {
    try {
      // Get recent backup jobs
      const { data: recentJobs, error } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .like('action', '%BACKUP%')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('❌ Error fetching backup status:', error);
      }

      const jobs = recentJobs || [];
      
      // Calculate statistics
      const totalBackups = jobs.length;
      const successfulBackups = jobs.filter(j => j.action?.includes('COMPLETED')).length;
      const failedBackups = jobs.filter(j => j.action?.includes('FAILED')).length;

      // Find last successful backups
      const lastDatabaseBackup = jobs.find(j => 
        j.action === 'DATABASE_BACKUP_COMPLETED'
      )?.created_at;
      
      const lastFileBackup = jobs.find(j => 
        j.action === 'FILE_BACKUP_COMPLETED'
      )?.created_at;

      // Determine system status
      let systemStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';
      const now = Date.now();
      const oneDayAgo = now - 24 * 60 * 60 * 1000;
      const threeDaysAgo = now - 3 * 24 * 60 * 60 * 1000;

      if (!lastDatabaseBackup || new Date(lastDatabaseBackup).getTime() < threeDaysAgo) {
        systemStatus = 'CRITICAL';
      } else if (!lastDatabaseBackup || new Date(lastDatabaseBackup).getTime() < oneDayAgo) {
        systemStatus = 'WARNING';
      }

      return {
        systemStatus,
        lastBackups: {
          database: lastDatabaseBackup ? new Date(lastDatabaseBackup) : null,
          files: lastFileBackup ? new Date(lastFileBackup) : null
        },
        statistics: {
          totalBackups,
          successfulBackups,
          failedBackups,
          totalSize: 0 // Would calculate from actual backup files
        },
        activeJobs: Array.from(this.activeJobs.values()),
        recentJobs: jobs.slice(0, 10).map(j => ({
          id: j.target_id,
          type: j.action?.includes('DATABASE') ? 'database' : 'files',
          schedule: 'manual',
          status: j.action?.includes('COMPLETED') ? 'completed' : 'failed',
          startTime: new Date(j.created_at),
          location: '',
          metadata: j.metadata
        })) as BackupJob[]
      };

    } catch (error) {
      console.error('❌ Error getting backup status:', error);
      return {
        systemStatus: 'CRITICAL',
        lastBackups: { database: null, files: null },
        statistics: { totalBackups: 0, successfulBackups: 0, failedBackups: 0, totalSize: 0 },
        activeJobs: [],
        recentJobs: []
      };
    }
  }

  /**
   * Get disaster recovery plan
   */
  getDisasterRecoveryPlan(): DisasterRecoveryPlan[] {
    return [
      {
        priority: 1,
        component: 'Database',
        backupLocation: 'database/latest_backup.sql',
        restoreSteps: [
          'Stop all application services',
          'Create new database instance',
          'Restore from latest backup',
          'Verify data integrity',
          'Update connection strings',
          'Restart services'
        ],
        estimatedTime: '30-60 minutes',
        dependencies: ['Database server', 'Backup files']
      },
      {
        priority: 2,
        component: 'Application Files',
        backupLocation: 'files/latest_backup.tar.gz',
        restoreSteps: [
          'Deploy to new server instance',
          'Extract backup archive',
          'Install dependencies',
          'Configure environment variables',
          'Start application services'
        ],
        estimatedTime: '15-30 minutes',
        dependencies: ['Server instance', 'File backups']
      },
      {
        priority: 3,
        component: 'Bot System',
        backupLocation: 'database/latest_backup.sql',
        restoreSteps: [
          'Verify bot user accounts',
          'Restore bot configurations',
          'Test bot connectivity',
          'Verify payment processing',
          'Resume bot operations'
        ],
        estimatedTime: '10-20 minutes',
        dependencies: ['Database', 'Telegram API access']
      }
    ];
  }

  /**
   * Helper methods
   */
  private async createBackupDirectories(): Promise<void> {
    const directories = [
      path.join(this.backupDirectory, 'database'),
      path.join(this.backupDirectory, 'files'),
      path.join(this.backupDirectory, 'configuration')
    ];

    for (const dir of directories) {
      await fs.promises.mkdir(dir, { recursive: true });
    }
  }

  private async getDatabaseTables(): Promise<string[]> {
    // Get all tables from the database
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');

    if (error) {
      console.error('❌ Error getting database tables:', error);
      // Return known critical tables
      return [
        'users', 'telegram_users', 'investment_phases', 
        'crypto_payment_transactions', 'admin_users', 'admin_audit_logs'
      ];
    }

    return data.map(t => t.table_name);
  }

  private async backupTable(tableName: string): Promise<any[]> {
    // Bot-safe table backup - ensure we don't interfere with bot operations
    const { data, error } = await supabase
      .from(tableName)
      .select('*');

    if (error) {
      console.error(`❌ Error backing up table ${tableName}:`, error);
      return [];
    }

    return data || [];
  }

  private generateBackupHeader(): string {
    return `-- Aureus Africa Database Backup
-- Generated: ${new Date().toISOString()}
-- Bot-safe backup with full data integrity
-- 
SET statement_timeout = 0;
SET lock_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

`;
  }

  private generateTableBackup(tableName: string, data: any[]): string {
    if (data.length === 0) {
      return `-- Table ${tableName} is empty\n\n`;
    }

    const columns = Object.keys(data[0]);
    let backup = `-- Table: ${tableName}\n`;
    backup += `TRUNCATE TABLE ${tableName} RESTART IDENTITY CASCADE;\n`;
    
    for (const row of data) {
      const values = columns.map(col => {
        const value = row[col];
        if (value === null) return 'NULL';
        if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
        if (typeof value === 'boolean') return value ? 'true' : 'false';
        if (value instanceof Date) return `'${value.toISOString()}'`;
        return value;
      }).join(', ');
      
      backup += `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values});\n`;
    }
    
    backup += '\n';
    return backup;
  }

  private generateBackupFooter(): string {
    return `-- Backup completed: ${new Date().toISOString()}
-- End of backup
`;
  }

  private async encryptBackup(content: string): Promise<string> {
    // Simple encryption - in production use proper encryption
    const key = process.env.BACKUP_ENCRYPTION_KEY || 'default-key';
    const cipher = crypto.createCipher('aes-256-cbc', key);
    let encrypted = cipher.update(content, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  private async calculateChecksum(filePath: string): Promise<string> {
    const hash = crypto.createHash(this.config.verification.checksumAlgorithm);
    const fileBuffer = await fs.promises.readFile(filePath);
    hash.update(fileBuffer);
    return hash.digest('hex');
  }

  private async createFileArchive(paths: string[]): Promise<string> {
    // Simplified file archiving - in production use proper archiving library
    let archive = '';
    
    for (const filePath of paths) {
      try {
        if (fs.existsSync(filePath)) {
          const stats = await fs.promises.stat(filePath);
          if (stats.isFile()) {
            const content = await fs.promises.readFile(filePath, 'utf8');
            archive += `--- FILE: ${filePath} ---\n${content}\n--- END FILE ---\n\n`;
          }
        }
      } catch (error) {
        console.error(`❌ Error archiving ${filePath}:`, error);
      }
    }
    
    return archive;
  }

  private async cleanupOldBackups(type: string, schedule: string): Promise<void> {
    // Implement backup cleanup based on retention policy
    console.log(`🧹 Cleaning up old ${type} backups (${schedule})`);
    // Implementation would remove old backup files based on retention settings
  }

  private async performRestoreTest(job: BackupJob): Promise<void> {
    // Implement restore test in isolated environment
    console.log(`🧪 Performing restore test for backup: ${job.id}`);
    // This would test restore to a separate test database
  }

  private async logBackupEvent(jobId: string, eventType: string, metadata: any): Promise<void> {
    try {
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'backup_system',
          action: eventType,
          target_type: 'backup_job',
          target_id: jobId,
          metadata: {
            ...metadata,
            botSafe: true,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

    } catch (error) {
      console.error('❌ Failed to log backup event:', error);
    }
  }

  private async loadBackupConfiguration(): Promise<void> {
    // Load backup configuration from database or file
    console.log('📋 Loading backup configuration...');
  }

  private async scheduleBackupJobs(): Promise<void> {
    // Schedule backup jobs using cron or similar
    console.log('⏰ Scheduling backup jobs...');
  }

  private startBackupMonitoring(): void {
    // Start monitoring backup jobs and system health
    console.log('👁️ Starting backup monitoring...');
  }
}

  /**
   * Manual backup trigger
   */
  async triggerManualBackup(type: 'database' | 'files' | 'both'): Promise<{
    success: boolean;
    jobs: BackupJob[];
    error?: string;
  }> {
    try {
      console.log(`🔄 Triggering manual backup: ${type}`);

      const jobs: BackupJob[] = [];

      if (type === 'database' || type === 'both') {
        const dbJob = await this.performDatabaseBackup('manual');
        jobs.push(dbJob);
      }

      if (type === 'files' || type === 'both') {
        const fileJob = await this.performFileBackup('manual');
        jobs.push(fileJob);
      }

      console.log(`✅ Manual backup completed: ${jobs.length} jobs`);
      return { success: true, jobs };

    } catch (error) {
      console.error('❌ Manual backup failed:', error);
      return { success: false, jobs: [], error: error.message };
    }
  }

  /**
   * Test backup system
   */
  async testBackupSystem(): Promise<{
    success: boolean;
    tests: { name: string; passed: boolean; error?: string }[];
  }> {
    const tests: { name: string; passed: boolean; error?: string }[] = [];

    try {
      // Test 1: Directory creation
      try {
        await this.createBackupDirectories();
        tests.push({ name: 'Directory Creation', passed: true });
      } catch (error) {
        tests.push({ name: 'Directory Creation', passed: false, error: error.message });
      }

      // Test 2: Database connection
      try {
        await this.getDatabaseTables();
        tests.push({ name: 'Database Connection', passed: true });
      } catch (error) {
        tests.push({ name: 'Database Connection', passed: false, error: error.message });
      }

      // Test 3: Checksum calculation
      try {
        const testFile = path.join(this.backupDirectory, 'test.txt');
        await fs.promises.writeFile(testFile, 'test content');
        await this.calculateChecksum(testFile);
        await fs.promises.unlink(testFile);
        tests.push({ name: 'Checksum Calculation', passed: true });
      } catch (error) {
        tests.push({ name: 'Checksum Calculation', passed: false, error: error.message });
      }

      const allPassed = tests.every(test => test.passed);
      return { success: allPassed, tests };

    } catch (error) {
      console.error('❌ Backup system test failed:', error);
      return {
        success: false,
        tests: [...tests, { name: 'System Test', passed: false, error: error.message }]
      };
    }
  }
}

// Create singleton instance
export const automatedBackup = new AutomatedBackupSystem();

export default automatedBackup;
