import React, { useState, useEffect } from 'react'
import { notificationService, UserNotification } from '../../lib/notificationService'
import { supabase, getServiceRoleClient } from '../../lib/supabase'

interface NotificationCenterProps {
  userId: number
  className?: string
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ userId, className = '' }) => {
  const [notifications, setNotifications] = useState<UserNotification[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({ total: 0, unread: 0, by_type: {} })
  const [activeFilter, setActiveFilter] = useState<string>('all')
  const [showUnreadOnly, setShowUnreadOnly] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info', text: string } | null>(null)

  const ITEMS_PER_PAGE = 10

  useEffect(() => {
    loadNotifications()
    loadStats()
  }, [userId, activeFilter, showUnreadOnly, currentPage])

  const loadNotifications = async () => {
    setLoading(true)
    setMessage(null)

    console.log('🔔 Loading notifications for user:', userId);

    try {
      // First try the notification service
      const result = await notificationService.getUserNotifications(userId, {
        limit: ITEMS_PER_PAGE,
        offset: (currentPage - 1) * ITEMS_PER_PAGE,
        type: activeFilter === 'all' ? undefined : activeFilter,
        unread_only: showUnreadOnly,
        include_archived: false
      })

      console.log('📋 Notification service result:', result);
      setNotifications(result.notifications)
      setTotalCount(result.total_count)

      if (result.notifications.length === 0) {
        console.log('ℹ️ No notifications found, this might be expected for new users');
        setMessage({
          type: 'info',
          text: 'No notifications yet. Notifications will appear here when you make purchases, earn commissions, or receive system updates.'
        });
      }
    } catch (error) {
      console.error('❌ Error loading notifications:', error)

      // Try direct database query as fallback using service role client
      console.log('🔄 Attempting direct database query as fallback...');
      try {
        const serviceClient = getServiceRoleClient()
        const { data: directNotifications, error: directError } = await serviceClient
          .from('user_notifications')
          .select('*')
          .eq('user_id', userId)
          .eq('is_archived', false)
          .order('created_at', { ascending: false })
          .limit(ITEMS_PER_PAGE);

        if (directError) {
          console.error('❌ Direct query also failed:', directError);
          setMessage({
            type: 'error',
            text: `Database error: ${directError.message}. The notification system may need to be set up.`
          });
        } else {
          console.log('✅ Direct query successful:', directNotifications);
          setNotifications(directNotifications || []);
          setTotalCount(directNotifications?.length || 0);

          if (!directNotifications || directNotifications.length === 0) {
            setMessage({
              type: 'info',
              text: 'No notifications found. This is normal for new accounts.'
            });
          }
        }
      } catch (fallbackError) {
        console.error('❌ Fallback query failed:', fallbackError);
        setMessage({
          type: 'error',
          text: 'Unable to load notifications. Please contact support if this persists.'
        });
      }
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const notificationStats = await notificationService.getNotificationStats(userId)
      setStats(notificationStats)
    } catch (error) {
      console.error('Error loading notification stats:', error)
    }
  }

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const success = await notificationService.markAsRead(notificationId, userId)
      if (success) {
        setNotifications(prev => 
          prev.map(n => 
            n.id === notificationId 
              ? { ...n, is_read: true, read_at: new Date().toISOString() }
              : n
          )
        )
        setStats(prev => ({ ...prev, unread: Math.max(0, prev.unread - 1) }))
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      const success = await notificationService.markAllAsRead(userId)
      if (success) {
        setNotifications(prev => 
          prev.map(n => ({ ...n, is_read: true, read_at: new Date().toISOString() }))
        )
        setStats(prev => ({ ...prev, unread: 0 }))
        setMessage({ type: 'success', text: 'All notifications marked as read' })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      setMessage({ type: 'error', text: 'Failed to mark all notifications as read' })
    }
  }

  const handleArchive = async (notificationId: string) => {
    try {
      const success = await notificationService.archiveNotification(notificationId, userId)
      if (success) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId))
        setTotalCount(prev => prev - 1)
        setMessage({ type: 'success', text: 'Notification archived' })
      }
    } catch (error) {
      console.error('Error archiving notification:', error)
      setMessage({ type: 'error', text: 'Failed to archive notification' })
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info': return 'ℹ️'
      case 'success': return '✅'
      case 'warning': return '⚠️'
      case 'error': return '❌'
      case 'payment': return '💳'
      case 'commission': return '💰'
      case 'system': return '📢'
      case 'payment_approved': return '✅'
      case 'payment_rejected': return '❌'
      case 'commission_earned': return '💰'
      case 'referral': return '👥'
      case 'phase_change': return '📈'
      case 'withdrawal': return '💸'
      default: return '📬'
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'info': return 'text-blue-400 bg-blue-500/20 border-blue-500/30'
      case 'success': return 'text-green-400 bg-green-500/20 border-green-500/30'
      case 'warning': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
      case 'error': return 'text-red-400 bg-red-500/20 border-red-500/30'
      case 'payment': return 'text-green-400 bg-green-500/20 border-green-500/30'
      case 'commission': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
      case 'system': return 'text-purple-400 bg-purple-500/20 border-purple-500/30'
      case 'payment_approved': return 'text-green-400 bg-green-500/20 border-green-500/30'
      case 'payment_rejected': return 'text-red-400 bg-red-500/20 border-red-500/30'
      case 'commission_earned': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
      case 'referral': return 'text-blue-400 bg-blue-500/20 border-blue-500/30'
      case 'phase_change': return 'text-orange-400 bg-orange-500/20 border-orange-500/30'
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30'
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    return date.toLocaleDateString()
  }

  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE)

  const filterOptions = [
    { key: 'all', label: 'All Notifications', count: stats.total },
    { key: 'info', label: 'Information', count: stats.by_type.info || 0 },
    { key: 'success', label: 'Success', count: stats.by_type.success || 0 },
    { key: 'warning', label: 'Warnings', count: stats.by_type.warning || 0 },
    { key: 'error', label: 'Errors', count: stats.by_type.error || 0 },
    { key: 'payment', label: 'Payments', count: stats.by_type.payment || 0 },
    { key: 'commission', label: 'Commissions', count: stats.by_type.commission || 0 },
    { key: 'system', label: 'System', count: stats.by_type.system || 0 }
  ]

  return (
    <div className={`notification-center ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-white">Notifications</h2>
          <p className="text-gray-400">
            {stats.unread > 0 ? `${stats.unread} unread` : 'All caught up!'} • {stats.total} total
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {stats.unread > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
            >
              Mark All Read
            </button>
          )}
          <button
            onClick={loadNotifications}
            disabled={loading}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`p-4 rounded-lg border mb-6 ${
          message.type === 'success' ? 'bg-green-900/20 border-green-500/30 text-green-300' :
          message.type === 'error' ? 'bg-red-900/20 border-red-500/30 text-red-300' :
          'bg-blue-900/20 border-blue-500/30 text-blue-300'
        }`}>
          {message.text}
        </div>
      )}

      {/* Filters */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-4 mb-6">
        <div className="flex flex-wrap gap-2 mb-4">
          {filterOptions.map((option) => (
            <button
              key={option.key}
              onClick={() => {
                setActiveFilter(option.key)
                setCurrentPage(1)
              }}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeFilter === option.key
                  ? 'bg-yellow-500 text-black'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {option.label} {option.count > 0 && `(${option.count})`}
            </button>
          ))}
        </div>
        
        <div className="flex items-center space-x-4">
          <label className="flex items-center space-x-2 text-sm text-gray-300">
            <input
              type="checkbox"
              checked={showUnreadOnly}
              onChange={(e) => {
                setShowUnreadOnly(e.target.checked)
                setCurrentPage(1)
              }}
              className="rounded border-gray-600 bg-gray-700 text-yellow-500 focus:ring-yellow-500"
            />
            <span>Show unread only</span>
          </label>
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading notifications...</p>
          </div>
        ) : notifications.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📬</div>
            <h3 className="text-xl font-semibold text-white mb-2">No notifications found</h3>
            <p className="text-gray-400">
              {showUnreadOnly ? 'No unread notifications' : 'You have no notifications yet'}
            </p>
          </div>
        ) : (
          notifications.map((notification) => (
            <div
              key={notification.id}
              className={`bg-gray-800 rounded-lg border p-4 transition-all hover:bg-gray-750 ${
                notification.is_read ? 'border-gray-700' : 'border-yellow-500/30 bg-gray-800/80'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className={`p-2 rounded-lg ${getNotificationColor(notification.type)}`}>
                    <span className="text-lg">{getNotificationIcon(notification.type)}</span>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-semibold text-white truncate">{notification.title}</h4>
                      {!notification.is_read && (
                        <span className="w-2 h-2 bg-yellow-500 rounded-full flex-shrink-0"></span>
                      )}
                    </div>
                    
                    <p className="text-gray-300 text-sm whitespace-pre-line mb-2">
                      {notification.message}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-400">
                      <span>{formatTimeAgo(notification.created_at)}</span>
                      <span className="capitalize">{notification.type.replace('_', ' ')}</span>
                      {notification.priority !== 'normal' && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          notification.priority === 'high' ? 'bg-orange-500/20 text-orange-400' :
                          notification.priority === 'urgent' ? 'bg-red-500/20 text-red-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {notification.priority}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  {!notification.is_read && (
                    <button
                      onClick={() => handleMarkAsRead(notification.id)}
                      className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
                      title="Mark as read"
                    >
                      ✓
                    </button>
                  )}
                  <button
                    onClick={() => handleArchive(notification.id)}
                    className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
                    title="Archive"
                  >
                    🗄️
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-700">
          <p className="text-sm text-gray-400">
            Showing {((currentPage - 1) * ITEMS_PER_PAGE) + 1} to {Math.min(currentPage * ITEMS_PER_PAGE, totalCount)} of {totalCount} notifications
          </p>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <span className="px-3 py-2 text-gray-300">
              Page {currentPage} of {totalPages}
            </span>
            
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
