#!/usr/bin/env node

/**
 * Quick Competition Setup Script
 * Sets up the Gold Diggers Club competition system with default data
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables:');
  console.error('   REACT_APP_SUPABASE_URL:', supabaseUrl ? '✅' : '❌');
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function quickSetup() {
  console.log('🏆 Quick Competition Setup');
  console.log('==========================');

  try {
    // Step 1: Check if competition tables exist
    console.log('\n📋 Checking competition tables...');
    
    const { data: tables, error: tablesError } = await supabase
      .from('competitions')
      .select('id')
      .limit(1);

    if (tablesError) {
      console.log('❌ Competition tables not found. Please run the full setup script first:');
      console.log('   node scripts/setup-competition-system.js');
      return;
    }

    // Step 2: Check for existing competition
    console.log('✅ Competition tables found');
    console.log('\n🔍 Checking for existing competitions...');
    
    const { data: existingCompetitions, error: compError } = await supabase
      .from('competitions')
      .select('*')
      .eq('is_active', true);

    if (compError) {
      console.error('❌ Error checking competitions:', compError);
      return;
    }

    if (existingCompetitions && existingCompetitions.length > 0) {
      console.log('✅ Found existing active competitions:');
      existingCompetitions.forEach(comp => {
        console.log(`   - ${comp.name} (${comp.status})`);
      });
      console.log('\n🎯 Competition system is already set up!');
      return;
    }

    // Step 3: Get current phase
    console.log('📊 Getting current investment phase...');
    
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single();

    if (phaseError || !currentPhase) {
      console.log('⚠️ No active phase found, using default phase 19');
    }

    const phaseId = currentPhase?.id || 19;
    const phaseName = currentPhase?.phase_name || 'Phase 19';

    // Step 4: Create competition
    console.log(`\n🏆 Creating competition for ${phaseName}...`);
    
    const { data: newCompetition, error: createError } = await supabase
      .from('competitions')
      .insert({
        name: `Gold Diggers Club - ${phaseName}`,
        description: 'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
        phase_id: phaseId,
        start_date: new Date().toISOString(),
        minimum_qualification_amount: 2500.00,
        total_prize_pool: 150000.00,
        status: 'active',
        is_active: true
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Error creating competition:', createError);
      return;
    }

    console.log('✅ Competition created:', newCompetition.name);

    // Step 5: Create prize tiers
    console.log('\n🏅 Creating prize tiers...');
    
    const prizeTiers = [
      { tier_name: '1st Place', tier_rank_start: 1, tier_rank_end: 1, prize_amount: 60000, display_order: 1, emoji: '🥇' },
      { tier_name: '2nd Place', tier_rank_start: 2, tier_rank_end: 2, prize_amount: 30000, display_order: 2, emoji: '🥈' },
      { tier_name: '3rd Place', tier_rank_start: 3, tier_rank_end: 3, prize_amount: 18000, display_order: 3, emoji: '🥉' },
      { tier_name: '4th Place', tier_rank_start: 4, tier_rank_end: 4, prize_amount: 6000, display_order: 4, emoji: '🏆' },
      { tier_name: '5th Place', tier_rank_start: 5, tier_rank_end: 5, prize_amount: 6000, display_order: 5, emoji: '🏆' },
      { tier_name: '6th Place', tier_rank_start: 6, tier_rank_end: 6, prize_amount: 6000, display_order: 6, emoji: '🏆' },
      { tier_name: '7th Place', tier_rank_start: 7, tier_rank_end: 7, prize_amount: 6000, display_order: 7, emoji: '🏆' },
      { tier_name: '8th Place', tier_rank_start: 8, tier_rank_end: 8, prize_amount: 6000, display_order: 8, emoji: '🏆' },
      { tier_name: '9th Place', tier_rank_start: 9, tier_rank_end: 9, prize_amount: 6000, display_order: 9, emoji: '🏆' },
      { tier_name: '10th Place', tier_rank_start: 10, tier_rank_end: 10, prize_amount: 6000, display_order: 10, emoji: '🏆' }
    ];

    const tiersToInsert = prizeTiers.map(tier => ({
      ...tier,
      competition_id: newCompetition.id
    }));

    const { error: tiersError } = await supabase
      .from('competition_prize_tiers')
      .insert(tiersToInsert);

    if (tiersError) {
      console.error('❌ Error creating prize tiers:', tiersError);
      return;
    }

    console.log('✅ Prize tiers created successfully');

    // Success message
    console.log('\n🎉 Competition setup completed successfully!');
    console.log('=====================================');
    console.log(`Competition: ${newCompetition.name}`);
    console.log(`Prize Pool: $${newCompetition.total_prize_pool.toLocaleString()}`);
    console.log(`Status: ${newCompetition.status}`);
    console.log('\n🌐 The website should now show the dynamic competition data!');
    console.log('💡 Clear your browser cache if you still see old data.');

  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

// Run the setup
quickSetup();
