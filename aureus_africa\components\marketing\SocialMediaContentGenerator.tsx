import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface ContentTemplate {
  id: string;
  platform: string;
  type: 'post' | 'story' | 'reel' | 'tweet' | 'linkedin';
  title: string;
  template: string;
  hashtags: string[];
  characterLimit: number;
  imageRequired: boolean;
}

interface GeneratedContent {
  platform: string;
  type: string;
  content: string;
  hashtags: string;
  characterCount: number;
  referralLink: string;
  imageUrl?: string;
}

interface SocialMediaContentGeneratorProps {
  user: any;
  getReferralUsername: (user: any) => string;
  currentPhase?: any;
}

export const SocialMediaContentGenerator: React.FC<SocialMediaContentGeneratorProps> = ({
  user,
  getReferralUsername,
  currentPhase
}) => {
  const [selectedPlatform, setSelectedPlatform] = useState<string>('facebook');
  const [selectedType, setSelectedType] = useState<string>('post');
  const [customMessage, setCustomMessage] = useState<string>('');
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [templates, setTemplates] = useState<ContentTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [contentVariations, setContentVariations] = useState<number>(3);

  const platforms = [
    { id: 'facebook', name: 'Facebook', icon: '📘', color: '#1877f2' },
    { id: 'instagram', name: 'Instagram', icon: '📷', color: '#e4405f' },
    { id: 'twitter', name: 'Twitter/X', icon: '🐦', color: '#1da1f2' },
    { id: 'linkedin', name: 'LinkedIn', icon: '💼', color: '#0077b5' },
    { id: 'tiktok', name: 'TikTok', icon: '🎵', color: '#000000' },
    { id: 'whatsapp', name: 'WhatsApp', icon: '💬', color: '#25d366' },
    { id: 'telegram', name: 'Telegram', icon: '✈️', color: '#0088cc' },
    { id: 'youtube', name: 'YouTube', icon: '📺', color: '#ff0000' }
  ];

  const contentTypes = {
    facebook: ['post', 'story', 'reel'],
    instagram: ['post', 'story', 'reel', 'igtv'],
    twitter: ['tweet', 'thread'],
    linkedin: ['post', 'article'],
    tiktok: ['video'],
    whatsapp: ['message', 'status'],
    telegram: ['message', 'channel_post'],
    youtube: ['short', 'video_description']
  };

  useEffect(() => {
    loadContentTemplates();
  }, [selectedPlatform, selectedType]);

  const loadContentTemplates = async () => {
    // In a real implementation, these would come from a database
    const mockTemplates: ContentTemplate[] = [
      {
        id: 'gold_opportunity',
        platform: selectedPlatform,
        type: selectedType as any,
        title: '🏆 Gold Mining Opportunity',
        template: `🌟 EXCLUSIVE GOLD MINING OPPORTUNITY! 🌟

💰 Own real shares in African gold mines
📈 Current price: $${currentPhase?.price_per_share || '25.00'} per share
🚀 ${currentPhase?.phase_name || 'Phase 1'} - Limited time offer!

✨ Why choose Aureus Africa Holdings?
• Real gold mining operations
• Transparent blockchain tracking
• Regular dividend payments
• Professional management team

🔥 Don't miss out on this life-changing opportunity!

{REFERRAL_LINK}

#GoldMining #Investment #AureusAfrica #GoldShares #Mining #Opportunity`,
        hashtags: ['#GoldMining', '#Investment', '#AureusAfrica', '#GoldShares', '#Mining', '#Opportunity'],
        characterLimit: selectedPlatform === 'twitter' ? 280 : 2200,
        imageRequired: true
      },
      {
        id: 'success_story',
        platform: selectedPlatform,
        type: selectedType as any,
        title: '📈 Success Story',
        template: `🎉 AMAZING RESULTS WITH AUREUS AFRICA! 🎉

Just received my latest dividend payment! 💰

Here's what I love about Aureus Africa Holdings:
✅ Consistent returns from real gold mining
✅ Transparent operations you can trust
✅ Easy to get started with any amount
✅ Professional team with proven track record

Ready to start your gold mining journey?

{REFERRAL_LINK}

#Success #GoldMining #Dividends #AureusAfrica #PassiveIncome`,
        hashtags: ['#Success', '#GoldMining', '#Dividends', '#AureusAfrica', '#PassiveIncome'],
        characterLimit: selectedPlatform === 'twitter' ? 280 : 2200,
        imageRequired: false
      },
      {
        id: 'educational',
        platform: selectedPlatform,
        type: selectedType as any,
        title: '🎓 Educational Content',
        template: `📚 DID YOU KNOW? Gold Mining Facts! 📚

🏆 Gold has been a store of value for over 4,000 years
📊 Only 190,000 tonnes of gold have ever been mined
🌍 South Africa produces 4.2% of world's gold supply
💎 Gold mining creates jobs and economic growth

With Aureus Africa Holdings, you can:
• Own shares in real gold mining operations
• Receive regular dividend payments
• Be part of Africa's mining success story

Learn more and get started:
{REFERRAL_LINK}

#GoldFacts #Mining #Education #AureusAfrica #GoldMining`,
        hashtags: ['#GoldFacts', '#Mining', '#Education', '#AureusAfrica', '#GoldMining'],
        characterLimit: selectedPlatform === 'twitter' ? 280 : 2200,
        imageRequired: true
      },
      {
        id: 'urgency',
        platform: selectedPlatform,
        type: selectedType as any,
        title: '⏰ Limited Time Offer',
        template: `⚠️ LAST CHANCE - ${currentPhase?.phase_name || 'Phase 1'} ENDING SOON! ⚠️

🔥 Only ${currentPhase?.total_shares_available - currentPhase?.shares_sold || '10,000'} shares remaining!

💰 Current price: $${currentPhase?.price_per_share || '25.00'} per share
📈 Next phase price: $${(parseFloat(currentPhase?.price_per_share || '25') * 1.2).toFixed(2)} per share

⏰ Don't wait - prices increase with each phase!

Secure your shares now:
{REFERRAL_LINK}

#LastChance #GoldMining #LimitedOffer #AureusAfrica #InvestNow`,
        hashtags: ['#LastChance', '#GoldMining', '#LimitedOffer', '#AureusAfrica', '#InvestNow'],
        characterLimit: selectedPlatform === 'twitter' ? 280 : 2200,
        imageRequired: true
      }
    ];

    setTemplates(mockTemplates);
    if (mockTemplates.length > 0 && !selectedTemplate) {
      setSelectedTemplate(mockTemplates[0].id);
    }
  };

  const generateReferralLink = (campaignId: string) => {
    const username = getReferralUsername(user);
    return `https://aureus.africa/register?ref=${username}&campaign=${campaignId}&platform=${selectedPlatform}`;
  };

  const createCreativeVariation = (baseContent: string, variationIndex: number, platform: string, template: any): string => {
    if (variationIndex === 0) return baseContent; // Keep first version as original

    const creativeTechniques = [
      // Variation 1: Question-based approach
      () => {
        const questions = [
          "Ready to secure your financial future? 🤔",
          "What if I told you there's a golden opportunity waiting? 💭",
          "Ever wondered how to turn $25 into real wealth? 🧐",
          "Looking for a game-changing opportunity? 🎯"
        ];
        return `${questions[Math.floor(Math.random() * questions.length)]}\n\n${baseContent}`;
      },

      // Variation 2: Story/testimonial approach
      () => {
        const stories = [
          "Just like Sarah from Lagos who started with one share and now owns 50+ shares...",
          "Remember when Bitcoin was $25? This is your second chance with gold shares...",
          "My friend called me crazy for buying gold shares at $25. Now he's asking how to join...",
          "Three months ago, I was skeptical. Today, I'm grateful I took action..."
        ];
        return `${stories[Math.floor(Math.random() * stories.length)]}\n\n${baseContent}`;
      },

      // Variation 3: Urgency/scarcity approach
      () => {
        const urgencyLines = [
          "⏰ PHASE 1 CLOSING SOON - Only days left at $25/share!",
          "🚨 LIMITED TIME: Secure your position before prices jump to $50!",
          "⚡ FINAL CALL: Phase 1 pricing ends this month!",
          "🔥 LAST CHANCE: Get in at ground floor pricing!"
        ];
        return `${urgencyLines[Math.floor(Math.random() * urgencyLines.length)]}\n\n${baseContent}`;
      },

      // Variation 4: Social proof approach
      () => {
        const socialProof = [
          "Join 10,000+ smart holders already building wealth with Aureus Africa! 👥",
          "While others wait, smart holders are already securing their future... 🧠",
          "The community is growing fast - don't be the last to join! 🌍",
          "Thousands have already started their journey to financial freedom... 🚀"
        ];
        return `${socialProof[Math.floor(Math.random() * socialProof.length)]}\n\n${baseContent}`;
      },

      // Variation 5: Benefit-focused approach
      () => {
        const benefits = [
          "💰 Real gold backing + 💎 Blockchain transparency + 📈 Growth potential = Your winning formula!",
          "🏆 Own real African gold mines + 💸 Earn regular dividends + 🔄 Trade anytime!",
          "✨ Physical gold security + 🌐 Digital convenience + 📊 Professional management!",
          "🎯 Diversify your portfolio + 🛡️ Hedge against inflation + 🌟 Support African development!"
        ];
        return `${benefits[Math.floor(Math.random() * benefits.length)]}\n\n${baseContent}`;
      }
    ];

    // Apply the technique based on variation index
    const technique = creativeTechniques[(variationIndex - 1) % creativeTechniques.length];
    return technique();
  };

  const generateVariedHashtags = (baseHashtags: string[], variationIndex: number, platform: string): string => {
    // Additional hashtag pools for variety
    const goldHashtags = ['#GoldShares', '#PreciousMetals', '#GoldMining', '#AfricanGold', '#RealGold', '#GoldBacked'];
    const opportunityHashtags = ['#Opportunity', '#WealthBuilding', '#SmartChoice', '#GrowthPotential', '#FinancialFreedom'];
    const urgencyHashtags = ['#LimitedTime', '#ActNow', '#DontMiss', '#LastChance', '#ExclusiveOffer'];
    const communityHashtags = ['#JoinUs', '#Community', '#Together', '#TeamWork', '#Success'];

    // Start with base hashtags
    let selectedHashtags = [...baseHashtags];

    // Add variation-specific hashtags
    switch (variationIndex % 4) {
      case 1:
        selectedHashtags.push(...goldHashtags.slice(0, 2));
        break;
      case 2:
        selectedHashtags.push(...opportunityHashtags.slice(0, 2));
        break;
      case 3:
        selectedHashtags.push(...urgencyHashtags.slice(0, 2));
        break;
      case 0:
        selectedHashtags.push(...communityHashtags.slice(0, 2));
        break;
    }

    // Platform-specific optimization
    if (platform === 'twitter') {
      selectedHashtags = selectedHashtags.slice(0, 8); // Twitter limit
    } else if (platform === 'instagram') {
      selectedHashtags = selectedHashtags.slice(0, 15); // Instagram sweet spot
    }

    // Remove duplicates and return
    const uniqueHashtags = [...new Set(selectedHashtags)];
    return uniqueHashtags.join(' ');
  };

  const generateContent = async () => {
    setIsGenerating(true);
    try {
      const template = templates.find(t => t.id === selectedTemplate);
      if (!template) return;

      const referralLink = generateReferralLink(`${selectedPlatform}_${selectedType}_${template.id}`);
      const variations: GeneratedContent[] = [];

      // Generate multiple variations
      for (let i = 0; i < contentVariations; i++) {
        let content = template.template;
        
        // Replace placeholders
        content = content.replace('{REFERRAL_LINK}', referralLink);
        content = content.replace('{USER_NAME}', user.first_name || 'Friend');
        content = content.replace('{CURRENT_PRICE}', currentPhase?.price_per_share || '25.00');
        content = content.replace('{PHASE_NAME}', currentPhase?.phase_name || 'Phase 1');

        // Add custom message if provided
        if (customMessage.trim()) {
          content = `${customMessage}\n\n${content}`;
        }

        // Create truly creative variations
        content = createCreativeVariation(content, i, selectedPlatform, template);

        // Optimize for platform
        content = optimizeForPlatform(content, selectedPlatform, template.characterLimit);

        const hashtags = generateVariedHashtags(template.hashtags, i, selectedPlatform);
        const finalContent = `${content}\n\n${hashtags}`;

        variations.push({
          platform: selectedPlatform,
          type: selectedType,
          content: finalContent,
          hashtags,
          characterCount: finalContent.length,
          referralLink,
          imageUrl: template.imageRequired ? generateImageUrl(template.id, i) : undefined
        });
      }

      setGeneratedContent(variations);

      // Track content generation
      await trackContentGeneration(selectedPlatform, selectedType, template.id);

    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const optimizeForPlatform = (content: string, platform: string, limit: number): string => {
    if (content.length <= limit) return content;

    // Platform-specific optimization
    switch (platform) {
      case 'twitter':
        // Shorten for Twitter
        const lines = content.split('\n');
        let optimized = '';
        for (const line of lines) {
          if ((optimized + line).length > limit - 50) break; // Leave room for hashtags
          optimized += line + '\n';
        }
        return optimized.trim();
      
      case 'instagram':
        // Instagram allows longer content but optimize for engagement
        return content.substring(0, limit - 100) + '...\n\nSee link in bio! 👆';
      
      default:
        return content.substring(0, limit - 50) + '...';
    }
  };

  const generateImageUrl = (templateId: string, variation: number): string => {
    // In a real implementation, this would generate actual images
    return `/api/marketing/generate-image?template=${templateId}&variation=${variation}&platform=${selectedPlatform}`;
  };

  const trackContentGeneration = async (platform: string, type: string, templateId: string) => {
    try {
      await supabase.from('marketing_content_generated').insert({
        user_id: user.database_user?.id,
        platform,
        content_type: type,
        template_id: templateId,
        generated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error tracking content generation:', error);
    }
  };

  const copyToClipboard = async (content: string, index: number) => {
    try {
      await navigator.clipboard.writeText(content);
      // Show success feedback
      const button = document.getElementById(`copy-btn-${index}`);
      if (button) {
        const originalText = button.textContent;
        button.textContent = '✅ Copied!';
        button.style.backgroundColor = '#10b981';
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = '';
        }, 2000);
      }
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  const shareToSocialMedia = (content: GeneratedContent) => {
    const encodedContent = encodeURIComponent(content.content);
    const encodedUrl = encodeURIComponent(content.referralLink);
    
    let shareUrl = '';
    
    switch (content.platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedContent}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodedContent}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}&summary=${encodedContent}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodedContent}`;
        break;
      case 'telegram':
        shareUrl = `https://t.me/share/url?url=${encodedUrl}&text=${encodedContent}`;
        break;
      default:
        // Copy to clipboard as fallback
        copyToClipboard(content.content, 0);
        return;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400');
  };

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151'
    }}>
      <div style={{ marginBottom: '24px' }}>
        <h3 style={{
          fontSize: '20px',
          fontWeight: 'bold',
          color: '#f59e0b',
          marginBottom: '8px',
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          ✨ AI Content Generator
        </h3>
        <p style={{ color: '#9ca3af', fontSize: '14px', margin: 0 }}>
          Create engaging social media content optimized for each platform
        </p>
      </div>

      {/* Platform Selection */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px', fontWeight: '600' }}>
          Select Platform
        </label>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '8px'
        }}>
          {platforms.map(platform => (
            <button
              key={platform.id}
              onClick={() => {
                setSelectedPlatform(platform.id);
                setSelectedType(contentTypes[platform.id as keyof typeof contentTypes][0]);
              }}
              style={{
                padding: '12px 8px',
                backgroundColor: selectedPlatform === platform.id 
                  ? `${platform.color}20` 
                  : 'rgba(55, 65, 81, 0.5)',
                border: selectedPlatform === platform.id 
                  ? `2px solid ${platform.color}` 
                  : '1px solid #4b5563',
                borderRadius: '8px',
                color: selectedPlatform === platform.id ? platform.color : '#d1d5db',
                fontSize: '12px',
                fontWeight: '600',
                cursor: 'pointer',
                textAlign: 'center',
                transition: 'all 0.2s ease'
              }}
            >
              <div style={{ fontSize: '16px', marginBottom: '4px' }}>{platform.icon}</div>
              {platform.name}
            </button>
          ))}
        </div>
      </div>

      {/* Content Type Selection */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px', fontWeight: '600' }}>
          Content Type
        </label>
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          {contentTypes[selectedPlatform as keyof typeof contentTypes]?.map(type => (
            <button
              key={type}
              onClick={() => setSelectedType(type)}
              style={{
                padding: '8px 16px',
                backgroundColor: selectedType === type 
                  ? 'rgba(59, 130, 246, 0.2)' 
                  : 'rgba(55, 65, 81, 0.5)',
                border: selectedType === type 
                  ? '2px solid #3b82f6' 
                  : '1px solid #4b5563',
                borderRadius: '6px',
                color: selectedType === type ? '#60a5fa' : '#d1d5db',
                fontSize: '12px',
                fontWeight: '600',
                cursor: 'pointer',
                textTransform: 'capitalize'
              }}
            >
              {type.replace('_', ' ')}
            </button>
          ))}
        </div>
      </div>

      {/* Template Selection */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px', fontWeight: '600' }}>
          Content Template
        </label>
        <select
          value={selectedTemplate}
          onChange={(e) => setSelectedTemplate(e.target.value)}
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            border: '1px solid #4b5563',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px'
          }}
        >
          {templates.map(template => (
            <option key={template.id} value={template.id}>
              {template.title}
            </option>
          ))}
        </select>
      </div>

      {/* Custom Message */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '8px', fontSize: '14px', fontWeight: '600' }}>
          Custom Message (Optional)
        </label>
        <textarea
          value={customMessage}
          onChange={(e) => setCustomMessage(e.target.value)}
          placeholder="Add your personal touch to the content..."
          rows={3}
          style={{
            width: '100%',
            padding: '12px',
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            border: '1px solid #4b5563',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px',
            resize: 'vertical'
          }}
        />
      </div>

      {/* Generation Settings */}
      <div style={{ display: 'flex', gap: '16px', marginBottom: '24px', alignItems: 'center' }}>
        <div>
          <label style={{ display: 'block', color: '#f3f4f6', marginBottom: '4px', fontSize: '12px' }}>
            Variations
          </label>
          <select
            value={contentVariations}
            onChange={(e) => setContentVariations(parseInt(e.target.value))}
            style={{
              padding: '8px 12px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              border: '1px solid #4b5563',
              borderRadius: '6px',
              color: 'white',
              fontSize: '14px'
            }}
          >
            <option value={1}>1 Version</option>
            <option value={3}>3 Versions</option>
            <option value={5}>5 Versions</option>
          </select>
        </div>

        <button
          onClick={generateContent}
          disabled={isGenerating || !selectedTemplate}
          style={{
            padding: '12px 24px',
            backgroundColor: isGenerating ? 'rgba(55, 65, 81, 0.5)' : '#f59e0b',
            border: 'none',
            borderRadius: '8px',
            color: 'white',
            fontSize: '14px',
            fontWeight: '600',
            cursor: isGenerating ? 'not-allowed' : 'pointer',
            marginTop: '16px'
          }}
        >
          {isGenerating ? '⏳ Generating...' : '✨ Generate Content'}
        </button>
      </div>

      {/* Generated Content */}
      {generatedContent.length > 0 && (
        <div>
          <h4 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
            📝 Generated Content ({generatedContent.length} variations)
          </h4>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {generatedContent.map((content, index) => (
              <div key={index} style={{
                backgroundColor: 'rgba(55, 65, 81, 0.5)',
                borderRadius: '8px',
                padding: '16px',
                border: '1px solid #4b5563'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ fontSize: '16px' }}>
                      {platforms.find(p => p.id === content.platform)?.icon}
                    </span>
                    <span style={{ color: '#f3f4f6', fontSize: '14px', fontWeight: '600' }}>
                      Version {index + 1}
                    </span>
                    <span style={{ 
                      color: content.characterCount > (templates.find(t => t.id === selectedTemplate)?.characterLimit || 2200) 
                        ? '#ef4444' : '#10b981', 
                      fontSize: '12px' 
                    }}>
                      {content.characterCount} chars
                    </span>
                  </div>
                  
                  <div style={{ display: 'flex', gap: '8px' }}>
                    <button
                      id={`copy-btn-${index}`}
                      onClick={() => copyToClipboard(content.content, index)}
                      style={{
                        padding: '6px 12px',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        border: '1px solid #3b82f6',
                        borderRadius: '6px',
                        color: '#60a5fa',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      📋 Copy
                    </button>
                    
                    <button
                      onClick={() => shareToSocialMedia(content)}
                      style={{
                        padding: '6px 12px',
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        border: '1px solid #10b981',
                        borderRadius: '6px',
                        color: '#10b981',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      📤 Share
                    </button>
                  </div>
                </div>
                
                <div style={{
                  backgroundColor: 'rgba(17, 24, 39, 0.8)',
                  borderRadius: '6px',
                  padding: '12px',
                  marginBottom: '12px'
                }}>
                  <pre style={{
                    color: '#d1d5db',
                    fontSize: '13px',
                    lineHeight: '1.5',
                    margin: 0,
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'inherit'
                  }}>
                    {content.content}
                  </pre>
                </div>
                
                {content.imageUrl && (
                  <div style={{
                    backgroundColor: 'rgba(75, 85, 99, 0.3)',
                    borderRadius: '6px',
                    padding: '8px',
                    fontSize: '12px',
                    color: '#9ca3af'
                  }}>
                    🖼️ Suggested image: {content.imageUrl}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
