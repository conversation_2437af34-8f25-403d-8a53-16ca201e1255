import { useState, useEffect, useCallback, useMemo } from 'react';
import { GalleryService } from '../lib/galleryService';
import type { 
  GalleryImage, 
  GalleryCategory, 
  GalleryFilters, 
  GalleryQueryOptions,
  UseGalleryImagesResult,
  UseGalleryCategoriesResult 
} from '../types/gallery';

export const useGalleryImages = (
  initialFilters: GalleryFilters = {},
  initialOptions: Omit<GalleryQueryOptions, 'filters'> = {}
): UseGalleryImagesResult => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<GalleryFilters>(initialFilters);

  // Memoize the options to prevent infinite re-renders
  const stableOptions = useMemo(() => initialOptions, [
    JSON.stringify(initialOptions)
  ]);

  const fetchImages = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const options: GalleryQueryOptions = {
        ...stableOptions,
        filters
      };

      // Create a timeout promise for better error handling
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Loading timeout - please check database connection')), 3000);
      });

      console.log('Fetching images with options:', options);

      // Race between the actual fetch and timeout
      const result = await Promise.race([
        GalleryService.getImages(options),
        timeoutPromise
      ]) as any;

      console.log('Fetch result:', result);
      console.log('Images found:', result.images?.length || 0);
      setImages(result.images);
    } catch (err: any) {
      console.warn('Gallery images fetch failed:', err);
      // Set error message for debugging with better error handling
      setError(err instanceof Error ? err.message : 'Failed to load gallery images');
      setImages([]);
    } finally {
      setLoading(false);
    }
  }, [filters, stableOptions]);

  useEffect(() => {
    fetchImages();

    // Add timeout to prevent infinite loading - reduced timeout for faster failure
    const timeout = setTimeout(() => {
      if (loading) {
        console.warn('Gallery loading timeout - stopping loading state');
        console.warn('Current filters:', filters);
        console.warn('Current options:', stableOptions);
        setLoading(false);
        setError('Loading timeout - please check database connection');
      }
    }, 3000); // 3 second timeout - fail faster

    return () => clearTimeout(timeout);
  }, [fetchImages]);

  const refetch = useCallback(() => {
    return fetchImages();
  }, [fetchImages]);

  const createImage = useCallback(async (data: any) => {
    const newImage = await GalleryService.createImage(data);
    await refetch();
    return newImage;
  }, [refetch]);

  const updateImage = useCallback(async (data: any) => {
    const updatedImage = await GalleryService.updateImage(data);
    await refetch();
    return updatedImage;
  }, [refetch]);

  const deleteImage = useCallback(async (id: string) => {
    await GalleryService.deleteImage(id);
    await refetch();
  }, [refetch]);

  const uploadImage = useCallback(async (file: File, metadata: any) => {
    const uploadResult = await GalleryService.uploadImage(file);
    const imageData = {
      ...metadata,
      image_url: uploadResult.url,
      file_size: uploadResult.file_size,
      width: uploadResult.width,
      height: uploadResult.height
    };
    return await createImage(imageData);
  }, [createImage]);

  const updateFilters = useCallback((newFilters: Partial<GalleryFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  return {
    images,
    loading,
    error,
    refetch,
    createImage,
    updateImage,
    deleteImage,
    uploadImage,
    filters,
    updateFilters
  } as UseGalleryImagesResult & { filters: GalleryFilters; updateFilters: (filters: Partial<GalleryFilters>) => void };
};

export const useGalleryCategories = (): UseGalleryCategoriesResult => {
  const [categories, setCategories] = useState<GalleryCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await GalleryService.getCategories();
      setCategories(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch categories');
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const refetch = useCallback(() => {
    return fetchCategories();
  }, [fetchCategories]);

  const createCategory = useCallback(async (data: any) => {
    const newCategory = await GalleryService.createCategory(data);
    await refetch();
    return newCategory;
  }, [refetch]);

  const updateCategory = useCallback(async (data: any) => {
    const updatedCategory = await GalleryService.updateCategory(data);
    await refetch();
    return updatedCategory;
  }, [refetch]);

  const deleteCategory = useCallback(async (id: string) => {
    await GalleryService.deleteCategory(id);
    await refetch();
  }, [refetch]);

  return {
    categories,
    loading,
    error,
    refetch,
    createCategory,
    updateCategory,
    deleteCategory
  };
};

// Specialized hook for featured images
export const useFeaturedImages = (limit: number = 6) => {
  return useGalleryImages(
    { is_featured: true },
    { 
      pagination: { page: 1, limit },
      sort: { field: 'display_order', direction: 'asc' }
    }
  );
};

// Specialized hook for category images
export const useCategoryImages = (categoryId: string, limit?: number) => {
  const options: Omit<GalleryQueryOptions, 'filters'> = {
    sort: { field: 'display_order', direction: 'asc' }
  };
  
  if (limit) {
    options.pagination = { page: 1, limit };
  }

  return useGalleryImages(
    { category_id: categoryId },
    options
  );
};

// Hook for gallery search
export const useGallerySearch = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  const filters: GalleryFilters = {
    ...(searchTerm && { search: searchTerm }),
    ...(selectedCategory && { category_id: selectedCategory }),
    ...(showFeaturedOnly && { is_featured: true })
  };

  const galleryResult = useGalleryImages(filters);

  return {
    ...galleryResult,
    searchTerm,
    setSearchTerm,
    selectedCategory,
    setSelectedCategory,
    showFeaturedOnly,
    setShowFeaturedOnly,
    clearFilters: () => {
      setSearchTerm('');
      setSelectedCategory('');
      setShowFeaturedOnly(false);
    }
  };
};
