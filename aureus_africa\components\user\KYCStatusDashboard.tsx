import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../../lib/supabase';

interface KYCInformation {
  id: string;
  user_id: number;
  first_name: string;
  last_name: string;
  full_legal_name: string;
  id_type: 'national_id' | 'passport';
  phone_number: string;
  email_address: string;
  street_address: string;
  city: string;
  postal_code: string;
  country_code: string;
  country_name: string;
  data_consent_given: boolean;
  privacy_policy_accepted: boolean;
  kyc_status: 'pending' | 'completed' | 'rejected' | 'expired';
  kyc_completed_at: string;
  certificate_requested: boolean;
  certificate_generated_at?: string;
  certificate_sent_at?: string;
  created_at: string;
  updated_at: string;
}

interface KYCStatusDashboardProps {
  userId: number;
  onStartKYC?: () => void;
  onUpdateKYC?: () => void;
}

export const KYCStatusDashboard: React.FC<KYCStatusDashboardProps> = ({
  userId,
  onStartKYC,
  onUpdateKYC
}) => {
  const [kycData, setKycData] = useState<KYCInformation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [certificateTimeline, setCertificateTimeline] = useState<string>('');

  useEffect(() => {
    loadKYCData();
  }, [userId]);

  const loadKYCData = async () => {
    try {
      setLoading(true);
      setError('');

      // Fetch KYC information using service role client
      const serviceClient = getServiceRoleClient()
      const { data: kyc, error: kycError } = await serviceClient
        .from('kyc_information')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (kycError && kycError.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw kycError;
      }

      setKycData(kyc);

      // Get certificate timeline if KYC is completed
      if (kyc && kyc.kyc_status === 'completed') {
        const { data: timeline } = await serviceClient
          .rpc('get_certificate_timeline');
        setCertificateTimeline(timeline || '48 hours');
      }

    } catch (error) {
      console.error('Error loading KYC data:', error);
      setError('Failed to load KYC information');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#10b981';
      case 'pending': return '#f59e0b';
      case 'rejected': return '#ef4444';
      case 'expired': return '#6b7280';
      default: return '#9ca3af';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'pending': return '⏳';
      case 'rejected': return '❌';
      case 'expired': return '⏰';
      default: return '📋';
    }
  };

  const getCompletionPercentage = () => {
    if (!kycData) return 0;
    if (kycData.kyc_status === 'completed') return 100;
    if (kycData.kyc_status === 'pending') return 75;
    return 25;
  };

  const requiredSteps = [
    { id: 'personal_info', name: 'Personal Information', completed: !!kycData },
    { id: 'id_verification', name: 'ID Verification', completed: !!kycData?.id_type },
    { id: 'address_verification', name: 'Address Verification', completed: !!kycData?.street_address },
    { id: 'consent', name: 'Privacy Consent', completed: !!kycData?.data_consent_given },
    { id: 'review', name: 'Review & Approval', completed: kycData?.kyc_status === 'completed' }
  ];

  if (loading) {
    return (
      <div style={{
        backgroundColor: 'rgba(31, 41, 55, 0.9)',
        borderRadius: '12px',
        padding: '24px',
        border: '1px solid #374151',
        textAlign: 'center'
      }}>
        <div style={{ color: '#9ca3af' }}>Loading KYC status...</div>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'rgba(31, 41, 55, 0.9)',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid #374151',
      marginBottom: '24px'
    }}>
      {/* Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <h2 style={{
          fontSize: '20px',
          fontWeight: 'bold',
          color: '#f59e0b',
          margin: 0,
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          🆔 KYC Verification Status
        </h2>
        
        {kycData && (
          <div style={{
            padding: '8px 16px',
            backgroundColor: `${getStatusColor(kycData.kyc_status)}20`,
            border: `1px solid ${getStatusColor(kycData.kyc_status)}`,
            borderRadius: '20px',
            color: getStatusColor(kycData.kyc_status),
            fontSize: '14px',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            gap: '6px'
          }}>
            {getStatusIcon(kycData.kyc_status)}
            {kycData.kyc_status.toUpperCase()}
          </div>
        )}
      </div>

      {error && (
        <div style={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '8px',
          padding: '12px',
          marginBottom: '20px',
          color: '#f87171'
        }}>
          ❌ {error}
        </div>
      )}

      {!kycData ? (
        // No KYC Data - Show Getting Started
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📋</div>
          <h3 style={{ color: 'white', fontSize: '24px', marginBottom: '12px' }}>
            Complete Your KYC Verification
          </h3>
          <p style={{ color: '#9ca3af', fontSize: '16px', marginBottom: '24px', lineHeight: '1.6' }}>
            To receive your share certificates and comply with regulatory requirements, 
            please complete the KYC (Know Your Customer) verification process.
          </p>
          
          <div style={{
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            border: '1px solid rgba(59, 130, 246, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '24px',
            textAlign: 'left'
          }}>
            <h4 style={{ color: '#60a5fa', fontSize: '16px', marginBottom: '12px' }}>
              📝 What You'll Need:
            </h4>
            <ul style={{ color: '#d1d5db', fontSize: '14px', lineHeight: '1.6', margin: 0, paddingLeft: '20px' }}>
              <li>Government-issued ID (National ID or Passport)</li>
              <li>Proof of address (Utility bill or Bank statement)</li>
              <li>Personal contact information</li>
              <li>5-10 minutes to complete the process</li>
            </ul>
          </div>

          <button
            onClick={onStartKYC}
            style={{
              padding: '16px 32px',
              backgroundColor: '#3b82f6',
              border: 'none',
              borderRadius: '12px',
              color: 'white',
              fontSize: '16px',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            🚀 Start KYC Verification
          </button>
        </div>
      ) : (
        // KYC Data Exists - Show Status
        <>
          {/* Progress Bar */}
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <span style={{ color: '#9ca3af', fontSize: '14px' }}>Verification Progress</span>
              <span style={{ color: '#f59e0b', fontSize: '14px', fontWeight: '600' }}>
                {getCompletionPercentage()}%
              </span>
            </div>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: 'rgba(55, 65, 81, 0.5)',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: `${getCompletionPercentage()}%`,
                height: '100%',
                backgroundColor: getStatusColor(kycData.kyc_status),
                borderRadius: '4px',
                transition: 'width 0.3s ease'
              }} />
            </div>
          </div>

          {/* Verification Steps */}
          <div style={{ marginBottom: '24px' }}>
            <h3 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '16px' }}>
              Verification Steps
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {requiredSteps.map((step, index) => (
                <div key={step.id} style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  padding: '12px',
                  backgroundColor: step.completed ? 'rgba(16, 185, 129, 0.1)' : 'rgba(55, 65, 81, 0.5)',
                  border: step.completed ? '1px solid rgba(16, 185, 129, 0.3)' : '1px solid #4b5563',
                  borderRadius: '8px'
                }}>
                  <div style={{
                    width: '24px',
                    height: '24px',
                    borderRadius: '50%',
                    backgroundColor: step.completed ? '#10b981' : '#6b7280',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    {step.completed ? '✓' : index + 1}
                  </div>
                  <span style={{
                    color: step.completed ? '#10b981' : '#9ca3af',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* KYC Information Summary */}
          <div style={{
            backgroundColor: 'rgba(55, 65, 81, 0.5)',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '24px'
          }}>
            <h3 style={{ color: '#f3f4f6', fontSize: '16px', fontWeight: '600', marginBottom: '12px' }}>
              📋 Verification Details
            </h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '12px',
              fontSize: '14px'
            }}>
              <div>
                <span style={{ color: '#9ca3af' }}>Full Name:</span>
                <div style={{ color: '#f3f4f6', fontWeight: '500' }}>{kycData.full_legal_name}</div>
              </div>
              <div>
                <span style={{ color: '#9ca3af' }}>ID Type:</span>
                <div style={{ color: '#f3f4f6', fontWeight: '500' }}>
                  {kycData.id_type === 'national_id' ? 'National ID' : 'Passport'}
                </div>
              </div>
              <div>
                <span style={{ color: '#9ca3af' }}>Country:</span>
                <div style={{ color: '#f3f4f6', fontWeight: '500' }}>{kycData.country_name}</div>
              </div>
              <div>
                <span style={{ color: '#9ca3af' }}>Completed:</span>
                <div style={{ color: '#f3f4f6', fontWeight: '500' }}>
                  {new Date(kycData.kyc_completed_at).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>

          {/* Certificate Status */}
          {kycData.kyc_status === 'completed' && (
            <div style={{
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              border: '1px solid rgba(16, 185, 129, 0.3)',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '16px'
            }}>
              <h3 style={{ color: '#10b981', fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                📜 Share Certificate Status
              </h3>
              <p style={{ color: '#d1d5db', fontSize: '14px', marginBottom: '12px' }}>
                {kycData.certificate_sent_at 
                  ? `Certificate sent on ${new Date(kycData.certificate_sent_at).toLocaleDateString()}`
                  : kycData.certificate_generated_at
                  ? `Certificate generated on ${new Date(kycData.certificate_generated_at).toLocaleDateString()}`
                  : kycData.certificate_requested
                  ? `Certificate generation in progress (${certificateTimeline})`
                  : 'Certificate will be generated within 48 hours'
                }
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
            {kycData.kyc_status === 'rejected' && (
              <button
                onClick={onUpdateKYC}
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#f59e0b',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                🔄 Update KYC Information
              </button>
            )}
            
            {kycData.kyc_status === 'completed' && !kycData.certificate_requested && (
              <button
                style={{
                  padding: '12px 24px',
                  backgroundColor: '#10b981',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}
              >
                📜 Request Certificate
              </button>
            )}
          </div>
        </>
      )}
    </div>
  );
};
