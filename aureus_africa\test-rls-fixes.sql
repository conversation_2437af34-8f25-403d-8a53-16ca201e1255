-- TEST RLS POLICY FIXES
-- Run these queries to verify the fixes work correctly

-- =====================================================
-- STEP 1: TEST BASIC FUNCTION AVAILABILITY
-- =====================================================

-- Test 1: Check if functions exist
SELECT 
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_schema = 'public'
    AND routine_name IN ('is_admin_user', 'get_current_user_id', 'is_current_user_admin')
ORDER BY routine_name;

-- =====================================================
-- STEP 2: TEST FUNCTION EXECUTION
-- =====================================================

-- Test 2: Test is_admin_user function with a known admin email
-- Replace '<EMAIL>' with an actual admin email
SELECT 
    'is_admin_user test' as test_name,
    public.is_admin_user('<EMAIL>') as result,
    'Should return true for valid admin' as expected;

-- Test 3: Test is_admin_user with non-admin email
SELECT 
    'is_admin_user non-admin test' as test_name,
    public.is_admin_user('<EMAIL>') as result,
    'Should return false for non-admin' as expected;

-- Test 4: Test get_current_user_id function
SELECT 
    'get_current_user_id test' as test_name,
    public.get_current_user_id() as result,
    'Should return user ID or NULL' as expected;

-- Test 5: Test is_current_user_admin function
SELECT 
    'is_current_user_admin test' as test_name,
    public.is_current_user_admin() as result,
    'Should return boolean' as expected;

-- =====================================================
-- STEP 3: TEST AUTH FUNCTIONS
-- =====================================================

-- Test 6: Check auth.uid() and auth.role()
SELECT 
    'auth functions test' as test_name,
    auth.uid() as auth_uid,
    auth.role() as auth_role,
    'Check if auth functions work' as note;

-- =====================================================
-- STEP 4: TEST TABLE ACCESS
-- =====================================================

-- Test 7: Test basic table access
SELECT 
    'users table access' as test_name,
    count(*) as user_count,
    'Should return number of users' as expected
FROM public.users;

-- Test 8: Test admin_users table access
SELECT 
    'admin_users table access' as test_name,
    count(*) as admin_count,
    'Should return number of admin users' as expected
FROM public.admin_users;

-- Test 9: Test commission_balances access
SELECT 
    'commission_balances access' as test_name,
    count(*) as balance_count,
    'Should return number of commission balance records' as expected
FROM public.commission_balances;

-- =====================================================
-- STEP 5: TEST SPECIFIC POLICY SCENARIOS
-- =====================================================

-- Test 10: Test service role access (this should work)
-- This query tests if service role can access tables
SELECT 
    'service_role access test' as test_name,
    (SELECT count(*) FROM public.users) as users_count,
    (SELECT count(*) FROM public.admin_users) as admin_count,
    'Service role should have full access' as expected;

-- =====================================================
-- STEP 6: IDENTIFY REMAINING ISSUES
-- =====================================================

-- Test 11: Check for tables that might still have issues
SELECT 
    t.tablename,
    t.rowsecurity as rls_enabled,
    COALESCE(p.policy_count, 0) as policy_count,
    CASE 
        WHEN t.rowsecurity = true AND COALESCE(p.policy_count, 0) = 0 THEN 'BLOCKED - No policies'
        WHEN COALESCE(p.policy_count, 0) > 3 THEN 'WARNING - Many policies'
        ELSE 'OK'
    END as status
FROM pg_tables t
LEFT JOIN (
    SELECT schemaname, tablename, count(*) as policy_count
    FROM pg_policies 
    WHERE schemaname = 'public'
    GROUP BY schemaname, tablename
) p ON t.schemaname = p.schemaname AND t.tablename = p.tablename
WHERE t.schemaname = 'public'
ORDER BY 
    CASE 
        WHEN t.rowsecurity = true AND COALESCE(p.policy_count, 0) = 0 THEN 1
        WHEN COALESCE(p.policy_count, 0) > 3 THEN 2
        ELSE 3
    END,
    t.tablename;
