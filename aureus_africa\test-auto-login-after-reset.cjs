#!/usr/bin/env node

/**
 * TEST AUTO-LOGIN AFTER PASSWORD RESET
 * 
 * This script tests the complete password reset flow with auto-login:
 * 1. Set up test PIN for password reset
 * 2. Verify PIN and reset password
 * 3. Verify auto-login works with new password
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const serviceClient = createClient(supabaseUrl, supabaseServiceKey);

async function testAutoLoginAfterReset() {
  console.log('🧪 Testing auto-login after password reset...\n');
  
  const testEmail = '<EMAIL>';
  const testPin = '999888';
  const newPassword = 'NewTestPassword123!';
  
  try {
    console.log('1️⃣ Setting up test PIN for password reset...');
    
    // Set up test PIN (same as test-frontend-pin-reset.cjs)
    const pinHash = require('bcryptjs').hashSync(testPin, 10);
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 60); // 1 hour
    
    const resetData = `PIN:${pinHash}:0`;
    const { error: setupError } = await serviceClient
      .from('users')
      .update({
        reset_token: resetData,
        reset_token_expires: expiresAt.toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('email', testEmail);
    
    if (setupError) {
      console.error('❌ Failed to set up test PIN:', setupError);
      return false;
    }
    
    console.log('✅ Test PIN set up successfully');
    
    console.log('\n2️⃣ Testing PIN verification...');
    
    // Test PIN verification
    const pinResponse = await fetch('http://localhost:8002/api/password-reset', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: testEmail, pin: testPin })
    });
    
    const pinResult = await pinResponse.json();
    
    if (!pinResult.success) {
      console.error('❌ PIN verification failed:', pinResult.message);
      return false;
    }
    
    console.log('✅ PIN verification successful');
    
    console.log('\n3️⃣ Testing password reset...');
    
    // Test password reset
    const resetResponse = await fetch('http://localhost:8002/api/password-reset', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: testEmail, newPassword })
    });
    
    const resetResult = await resetResponse.json();
    
    if (!resetResult.success) {
      console.error('❌ Password reset failed:', resetResult.message);
      return false;
    }
    
    console.log('✅ Password reset successful');
    
    console.log('\n4️⃣ Testing auto-login with new password...');
    
    // Test login with new password (simulate what the frontend does)
    const { signInWithEmailEnhanced } = await import('./lib/supabase.ts');
    const { user, error: loginError } = await signInWithEmailEnhanced(testEmail, newPassword);
    
    if (loginError) {
      console.error('❌ Auto-login failed:', loginError.message);
      return false;
    }
    
    if (!user) {
      console.error('❌ Auto-login failed: No user returned');
      return false;
    }
    
    console.log('✅ Auto-login successful!');
    console.log('👤 User data:', {
      id: user.id,
      email: user.email,
      username: user.username
    });
    
    console.log('\n🎉 Complete password reset with auto-login test PASSED!');
    
    console.log('\n🔗 Frontend Testing Instructions:');
    console.log('1. Go to: http://localhost:8004/login');
    console.log('2. Click "Login (Web)" tab');
    console.log('3. Click "Forgot your password?"');
    console.log(`4. Enter email: ${testEmail}`);
    console.log(`5. Enter PIN: ${testPin}`);
    console.log(`6. Set new password: ${newPassword}`);
    console.log('7. Click "Update Password"');
    console.log('8. Should see confirmation and auto-login to dashboard');
    
    return true;
    
  } catch (error) {
    console.error('❌ Auto-login test failed:', error);
    return false;
  }
}

// Run the test
testAutoLoginAfterReset().then(success => {
  console.log(`\n${success ? '🎉' : '💥'} Test ${success ? 'PASSED' : 'FAILED'}`);
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
