#!/usr/bin/env node

/**
 * FIX ADMIN TELEGRAM USER MANAGEMENT
 * 
 * This script provides a comprehensive fix for the admin user management
 * system to properly handle Telegram users by updating both users and
 * telegram_users tables when passwords are changed.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

const fixAdminTelegramUserManagement = async () => {
  try {
    console.log('🔧 Fixing Admin Telegram User Management...\n');

    // Step 1: Check current state
    console.log('📋 Step 1: Analyzing current user data...');
    
    const telegramId = '1393852532';
    
    // Check telegram_users table
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (telegramError) {
      console.error('❌ Error finding telegram user:', telegramError);
      return;
    }

    console.log('📋 Current Telegram User:');
    console.log(`   Telegram ID: ${telegramUser.telegram_id}`);
    console.log(`   Username: ${telegramUser.username}`);
    console.log(`   First Name: ${telegramUser.first_name}`);
    console.log(`   User ID (linked): ${telegramUser.user_id || 'Not linked'}`);
    console.log(`   Password Hash: ${telegramUser.password_hash ? 'SET' : 'NOT SET'}`);

    // Check if there's a linked user in users table
    let linkedUser = null;
    if (telegramUser.user_id) {
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', telegramUser.user_id)
        .single();

      if (!userError && userData) {
        linkedUser = userData;
        console.log('\n📋 Linked User in users table:');
        console.log(`   ID: ${userData.id}`);
        console.log(`   Username: ${userData.username}`);
        console.log(`   Email: ${userData.email}`);
        console.log(`   Password Hash: ${userData.password_hash ? 'SET' : 'NOT SET'}`);
      }
    }

    // Step 2: Set the password that admin tried to set
    const newPassword = 'Gunst0n5o0!@#';
    console.log(`\n📋 Step 2: Setting password: "${newPassword}"`);
    const hashedPassword = await hashPassword(newPassword);
    console.log('✅ Password hashed successfully');

    // Step 3: Update telegram_users table
    console.log('\n📋 Step 3: Updating telegram_users table...');
    
    const { error: telegramUpdateError } = await supabase
      .from('telegram_users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('telegram_id', telegramId);

    if (telegramUpdateError) {
      console.error('❌ Error updating telegram_users:', telegramUpdateError);
      
      if (telegramUpdateError.message.includes('password_hash')) {
        console.log('\n⚠️ The password_hash column does not exist in telegram_users table.');
        console.log('Please run the fix-telegram-password-issue.js script first to add the column.');
        return;
      }
    } else {
      console.log('✅ telegram_users table updated successfully');
    }

    // Step 4: Update users table if linked
    if (linkedUser) {
      console.log('\n📋 Step 4: Updating linked user in users table...');
      
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          password_hash: hashedPassword,
          updated_at: new Date().toISOString()
        })
        .eq('id', linkedUser.id);

      if (userUpdateError) {
        console.error('❌ Error updating users table:', userUpdateError);
      } else {
        console.log('✅ users table updated successfully');
      }
    } else {
      console.log('\n📋 Step 4: No linked user found, skipping users table update');
    }

    // Step 5: Verify the fix
    console.log('\n📋 Step 5: Verifying the fix...');
    
    const { data: verifyData, error: verifyError } = await supabase
      .from('telegram_users')
      .select('telegram_id, username, first_name, password_hash')
      .eq('telegram_id', telegramId)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying fix:', verifyError);
      return;
    }

    console.log('📋 Final Telegram User State:');
    console.log(`   Telegram ID: ${verifyData.telegram_id}`);
    console.log(`   Username: ${verifyData.username}`);
    console.log(`   First Name: ${verifyData.first_name}`);
    console.log(`   Password Hash: ${verifyData.password_hash ? 'SET ✅' : 'NOT SET ❌'}`);

    if (verifyData.password_hash) {
      // Test password verification
      const isValid = await bcrypt.compare(newPassword, verifyData.password_hash);
      console.log(`   Password Verification: ${isValid ? '✅ SUCCESS' : '❌ FAILED'}`);
      
      if (isValid) {
        console.log('\n🎉 Fix completed successfully!');
        console.log('The user should now be able to login with the password.');
        console.log('\n📋 Next Steps:');
        console.log('1. Test login with Telegram ID: 1393852532');
        console.log('2. Test password: Gunst0n5o0!@#');
        console.log('3. Update UserEditModal.tsx to handle Telegram users properly');
      }
    }

  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
};

fixAdminTelegramUserManagement();
