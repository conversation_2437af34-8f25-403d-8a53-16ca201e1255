// API endpoint to check Telegram connection status
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { user_id } = req.query;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Check if user has a linked Telegram account
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('user_id', parseInt(user_id))
      .single();

    if (telegramError && telegramError.code !== 'PGRST116') {
      console.error('Error checking Telegram connection:', telegramError);
      return res.status(500).json({ error: 'Database error' });
    }

    // Return connection status
    res.status(200).json({
      success: true,
      isConnected: !!telegramUser,
      telegramUser: telegramUser || null
    });

  } catch (error) {
    console.error('Telegram connection check error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
