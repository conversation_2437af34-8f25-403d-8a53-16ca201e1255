const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Required: REACT_APP_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runNotificationMigration() {
  console.log('🚀 Starting notification system migration...');

  try {
    // Read the SQL migration file
    const sqlPath = path.join(__dirname, 'notification-system-schema.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // Split SQL commands by semicolon and filter out empty ones
    const sqlCommands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));

    console.log(`📋 Found ${sqlCommands.length} SQL commands to execute`);

    // Execute each SQL command
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i];
      console.log(`\n⚡ Executing command ${i + 1}/${sqlCommands.length}:`);
      console.log(command.substring(0, 100) + (command.length > 100 ? '...' : ''));

      const { error } = await supabase.rpc('exec_sql', {
        sql: command
      });

      if (error) {
        console.error(`❌ Error executing command ${i + 1}:`, error);
        // Continue with other commands instead of stopping
      } else {
        console.log(`✅ Command ${i + 1} executed successfully`);
      }
    }

    // Verify the migration by checking table structure
    console.log('\n🔍 Verifying notification tables...');
    
    const tables = ['user_notifications', 'notification_templates', 'notification_preferences', 'notification_delivery_log'];
    
    for (const tableName of tables) {
      const { data: columns, error: columnsError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', tableName)
        .eq('table_schema', 'public');

      if (columnsError) {
        console.error(`❌ Error verifying ${tableName} table:`, columnsError);
      } else if (columns && columns.length > 0) {
        console.log(`✅ ${tableName} table created successfully (${columns.length} columns)`);
      } else {
        console.log(`⚠️ ${tableName} table not found or has no columns`);
      }
    }

    // Test notification template insertion
    console.log('\n🧪 Testing notification templates...');
    const { data: templates, error: templateError } = await supabase
      .from('notification_templates')
      .select('template_key, notification_type')
      .limit(5);

    if (templateError) {
      console.error('❌ Error testing notification templates:', templateError);
    } else {
      console.log(`✅ Notification templates test successful (${templates.length} templates found)`);
      templates.forEach(template => {
        console.log(`  • ${template.template_key} (${template.notification_type})`);
      });
    }

    // Test creating a sample notification
    console.log('\n🧪 Testing notification creation...');
    
    // Get a test user
    const { data: testUser, error: userError } = await supabase
      .from('users')
      .select('id, username')
      .limit(1)
      .single();

    if (userError || !testUser) {
      console.log('⚠️ No test user found, skipping notification creation test');
    } else {
      const { data: testNotification, error: notificationError } = await supabase
        .from('user_notifications')
        .insert({
          user_id: testUser.id,
          notification_type: 'system',
          title: 'Test Notification',
          message: 'This is a test notification created during migration.',
          metadata: { test: true, migration_date: new Date().toISOString() }
        })
        .select()
        .single();

      if (notificationError) {
        console.error('❌ Error creating test notification:', notificationError);
      } else {
        console.log(`✅ Test notification created successfully for user ${testUser.username}`);
        
        // Clean up test notification
        await supabase
          .from('user_notifications')
          .delete()
          .eq('id', testNotification.id);
        console.log('🧹 Test notification cleaned up');
      }
    }

    console.log('\n🎉 Notification system migration completed successfully!');
    console.log('\n📋 Migration Summary:');
    console.log('✅ Created user_notifications table');
    console.log('✅ Created notification_templates table with default templates');
    console.log('✅ Created notification_preferences table');
    console.log('✅ Created notification_delivery_log table');
    console.log('✅ Created indexes for optimal performance');
    console.log('✅ Created user_notification_summary view');
    console.log('✅ Set up proper permissions');
    console.log('\n🚀 The notification system is now ready for use!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runNotificationMigration();
