-- Create audit logs table for comprehensive admin action tracking
-- This table will store all administrative actions for security and compliance

CREATE TABLE IF NOT EXISTS admin_audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Admin Information
  admin_email VARCHAR(255) NOT NULL,
  admin_user_id UUID,
  admin_role VARCHAR(50),
  
  -- Action Details
  action VARCHAR(100) NOT NULL,
  target_type VARCHAR(50) NOT NULL, -- 'user', 'payment', 'sponsor', 'gallery', 'system'
  target_id VARCHAR(255) NOT NULL,
  
  -- Change Details
  old_values JSONB DEFAULT '{}'::jsonb,
  new_values JSONB DEFAULT '{}'::jsonb,
  details JSONB DEFAULT '{}'::jsonb,
  
  -- Request Information
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(255),
  
  -- Timestamps
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Status
  status VARCHAR(20) DEFAULT 'success', -- 'success', 'failed', 'partial'
  error_message TEXT,
  
  -- Indexes for performance
  CONSTRAINT admin_audit_logs_action_check CHECK (action IN (
    'CREATE_USER', 'UPDATE_USER', 'DELETE_USER', 'ACTIVATE_USER', 'DEACTIVATE_USER',
    'APPROVE_PAYMENT', 'REJECT_PAYMENT', 'UPDATE_PAYMENT',
    'CREATE_SPONSOR', 'UPDATE_SPONSOR', 'DELETE_SPONSOR',
    'UPLOAD_GALLERY', 'DELETE_GALLERY',
    'LOGIN', 'LOGOUT', 'PERMISSION_CHANGE',
    'SYSTEM_CONFIG', 'DATABASE_QUERY'
  )),
  
  CONSTRAINT admin_audit_logs_target_type_check CHECK (target_type IN (
    'user', 'payment', 'sponsor', 'gallery', 'admin', 'system'
  )),
  
  CONSTRAINT admin_audit_logs_status_check CHECK (status IN (
    'success', 'failed', 'partial'
  ))
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_admin_email ON admin_audit_logs(admin_email);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_action ON admin_audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_target_type ON admin_audit_logs(target_type);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_target_id ON admin_audit_logs(target_id);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_timestamp ON admin_audit_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_status ON admin_audit_logs(status);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_admin_timestamp ON admin_audit_logs(admin_email, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_target_timestamp ON admin_audit_logs(target_type, target_id, timestamp DESC);

-- Add RLS (Row Level Security) policies if needed
-- ALTER TABLE admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows admins to view all logs
-- CREATE POLICY admin_audit_logs_admin_access ON admin_audit_logs
--   FOR ALL TO authenticated
--   USING (auth.jwt() ->> 'email' IN (
--     SELECT email FROM admin_users WHERE is_active = true
--   ));

-- Add comments for documentation
COMMENT ON TABLE admin_audit_logs IS 'Comprehensive audit trail for all administrative actions';
COMMENT ON COLUMN admin_audit_logs.admin_email IS 'Email of the admin who performed the action';
COMMENT ON COLUMN admin_audit_logs.action IS 'Type of action performed (CREATE_USER, UPDATE_USER, etc.)';
COMMENT ON COLUMN admin_audit_logs.target_type IS 'Type of entity being acted upon';
COMMENT ON COLUMN admin_audit_logs.target_id IS 'ID of the specific entity';
COMMENT ON COLUMN admin_audit_logs.old_values IS 'Previous values before the change (JSON)';
COMMENT ON COLUMN admin_audit_logs.new_values IS 'New values after the change (JSON)';
COMMENT ON COLUMN admin_audit_logs.details IS 'Additional context and metadata (JSON)';

-- Create a view for easy audit log querying
CREATE OR REPLACE VIEW admin_audit_summary AS
SELECT 
  id,
  admin_email,
  action,
  target_type,
  target_id,
  timestamp,
  status,
  CASE 
    WHEN old_values::text != '{}'::text OR new_values::text != '{}'::text 
    THEN 'Data Changed'
    ELSE 'Action Only'
  END as change_type,
  details->>'reason' as reason,
  details->>'ip_address' as ip_address
FROM admin_audit_logs
ORDER BY timestamp DESC;

-- Create a function to clean up old audit logs (optional)
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs(days_to_keep INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM admin_audit_logs 
  WHERE timestamp < NOW() - INTERVAL '1 day' * days_to_keep;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Add a comment to the cleanup function
COMMENT ON FUNCTION cleanup_old_audit_logs IS 'Removes audit logs older than specified days (default: 365 days)';

-- Example usage:
-- SELECT cleanup_old_audit_logs(180); -- Keep only last 180 days

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT ON admin_audit_logs TO authenticated;
-- GRANT SELECT ON admin_audit_summary TO authenticated;
