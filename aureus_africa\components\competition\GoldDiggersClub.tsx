import React, { useState, useEffect } from 'react';
import { competitionService, Competition, PrizeTier, LeaderboardEntry, CompetitionStats } from '../../lib/services/competitionService';

interface GoldDiggersClubProps {
  currentPhase?: any;
}

const GoldDiggersClub: React.FC<GoldDiggersClubProps> = ({ currentPhase }) => {
  const [competition, setCompetition] = useState<Competition | null>(null);
  const [prizeTiers, setPrizeTiers] = useState<PrizeTier[]>([]);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [stats, setStats] = useState<CompetitionStats>({
    totalParticipants: 0,
    qualifiedParticipants: 0,
    leadingVolume: 0,
    totalPrizePool: 0,
    minimumQualification: 2500
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [availableCompetitions, setAvailableCompetitions] = useState<Competition[]>([]);
  const [selectedCompetitionId, setSelectedCompetitionId] = useState<string | null>(null);

  useEffect(() => {
    loadAvailableCompetitions();
  }, []);

  useEffect(() => {
    if (selectedCompetitionId) {
      loadCompetitionData(selectedCompetitionId);

      // Refresh data every 30 seconds for selected competition
      const interval = setInterval(() => loadCompetitionData(selectedCompetitionId), 30000);
      return () => clearInterval(interval);
    }
  }, [selectedCompetitionId]);

  const loadAvailableCompetitions = async () => {
    try {
      console.log('🔍 Loading available competitions...');

      // Get all competitions
      const competitions = await competitionService.getAllCompetitions();
      console.log('📋 Available competitions:', competitions);

      setAvailableCompetitions(competitions);

      // Auto-select the current active competition or the most recent one
      if (competitions.length > 0) {
        const activeCompetition = competitions.find(comp => comp.status === 'active');
        const selectedComp = activeCompetition || competitions[0];
        setSelectedCompetitionId(selectedComp.id);
        console.log('🎯 Auto-selected competition:', selectedComp.name);
      }

    } catch (err) {
      console.error('❌ Error loading available competitions:', err);
      setError('Failed to load competitions');
    }
  };

  const loadCompetitionData = async (competitionId?: string) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🏆 Loading competition data for ID:', competitionId);

      // Get specific competition or current competition
      let currentCompetition;
      if (competitionId) {
        currentCompetition = await competitionService.getCompetitionById(competitionId);
      } else {
        currentCompetition = await competitionService.getCurrentCompetition();
      }

      if (!currentCompetition) {
        console.log('❌ No competition found for ID:', competitionId);

        // Check if this is a fallback competition ID
        if (competitionId && competitionId.startsWith('fallback-')) {
          const fallbackCompetitions = availableCompetitions.length > 0 ? availableCompetitions : [
            {
              id: 'fallback-phase-19',
              name: 'Gold Diggers Club - Phase 19',
              description: 'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
              status: 'active' as const,
              total_prize_pool: 150000,
              minimum_qualification_amount: 2500,
              phase_id: 19
            }
          ];

          const fallbackComp = fallbackCompetitions.find(comp => comp.id === competitionId);
          if (fallbackComp) {
            console.log('✅ Using fallback competition:', fallbackComp.name);

            const getPrizeTiersForCompetition = (comp: any) => {
              const baseAmount = comp.total_prize_pool * 0.4;
              return [
                { id: '1', tier_name: '1st Place', tier_rank_start: 1, tier_rank_end: 1, prize_amount: baseAmount, display_order: 1, emoji: '🥇' },
                { id: '2', tier_name: '2nd Place', tier_rank_start: 2, tier_rank_end: 2, prize_amount: baseAmount * 0.5, display_order: 2, emoji: '🥈' },
                { id: '3', tier_name: '3rd Place', tier_rank_start: 3, tier_rank_end: 3, prize_amount: baseAmount * 0.3, display_order: 3, emoji: '🥉' },
                { id: '4', tier_name: '4th Place', tier_rank_start: 4, tier_rank_end: 4, prize_amount: baseAmount * 0.1, display_order: 4, emoji: '🏆' },
                { id: '5', tier_name: '5th Place', tier_rank_start: 5, tier_rank_end: 5, prize_amount: baseAmount * 0.1, display_order: 5, emoji: '🏆' },
                { id: '6', tier_name: '6th Place', tier_rank_start: 6, tier_rank_end: 6, prize_amount: baseAmount * 0.1, display_order: 6, emoji: '🏆' },
                { id: '7', tier_name: '7th Place', tier_rank_start: 7, tier_rank_end: 7, prize_amount: baseAmount * 0.1, display_order: 7, emoji: '🏆' },
                { id: '8', tier_name: '8th Place', tier_rank_start: 8, tier_rank_end: 8, prize_amount: baseAmount * 0.1, display_order: 8, emoji: '🏆' },
                { id: '9', tier_name: '9th Place', tier_rank_start: 9, tier_rank_end: 9, prize_amount: baseAmount * 0.1, display_order: 9, emoji: '🏆' },
                { id: '10', tier_name: '10th Place', tier_rank_start: 10, tier_rank_end: 10, prize_amount: baseAmount * 0.1, display_order: 10, emoji: '🏆' }
              ];
            };

            setCompetition(fallbackComp);
            setPrizeTiers(getPrizeTiersForCompetition(fallbackComp));
            setLeaderboard([]);
            setStats({
              totalParticipants: 0,
              qualifiedParticipants: 0,
              leadingVolume: 0,
              totalPrizePool: fallbackComp.total_prize_pool,
              minimumQualification: fallbackComp.minimum_qualification_amount
            });

            console.log('✅ Fallback competition data loaded');
            return;
          }
        }

        console.log('❌ No active competition found - using default fallback data');

        // Use fallback competition data - create multiple phases
        const fallbackCompetitions = [
          {
            id: 'fallback-phase-19',
            name: 'Gold Diggers Club - Phase 19',
            description: 'Build your network by referring new investors. Each qualified referral counts toward your ranking.',
            status: 'active' as const,
            total_prize_pool: 150000,
            minimum_qualification_amount: 2500,
            phase_id: 19
          },
          {
            id: 'fallback-phase-18',
            name: 'Gold Diggers Club - Phase 18',
            description: 'Previous phase competition - Build your network by referring new investors.',
            status: 'ended' as const,
            total_prize_pool: 120000,
            minimum_qualification_amount: 2000,
            phase_id: 18
          },
          {
            id: 'fallback-phase-20',
            name: 'Gold Diggers Club - Phase 20',
            description: 'Upcoming phase competition - Get ready for the next challenge!',
            status: 'upcoming' as const,
            total_prize_pool: 200000,
            minimum_qualification_amount: 3000,
            phase_id: 20
          }
        ];

        setAvailableCompetitions(fallbackCompetitions);

        // Select the active competition or first one
        const activeComp = fallbackCompetitions.find(comp => comp.status === 'active') || fallbackCompetitions[0];
        setSelectedCompetitionId(activeComp.id);

        const fallbackCompetition = activeComp;

        // Generate prize tiers based on selected competition
        const getPrizeTiersForCompetition = (comp: any) => {
          const baseAmount = comp.total_prize_pool * 0.4; // 40% for 1st place
          return [
            { id: '1', tier_name: '1st Place', tier_rank_start: 1, tier_rank_end: 1, prize_amount: baseAmount, display_order: 1, emoji: '🥇' },
            { id: '2', tier_name: '2nd Place', tier_rank_start: 2, tier_rank_end: 2, prize_amount: baseAmount * 0.5, display_order: 2, emoji: '🥈' },
            { id: '3', tier_name: '3rd Place', tier_rank_start: 3, tier_rank_end: 3, prize_amount: baseAmount * 0.3, display_order: 3, emoji: '🥉' },
            { id: '4', tier_name: '4th Place', tier_rank_start: 4, tier_rank_end: 4, prize_amount: baseAmount * 0.1, display_order: 4, emoji: '🏆' },
            { id: '5', tier_name: '5th Place', tier_rank_start: 5, tier_rank_end: 5, prize_amount: baseAmount * 0.1, display_order: 5, emoji: '🏆' },
            { id: '6', tier_name: '6th Place', tier_rank_start: 6, tier_rank_end: 6, prize_amount: baseAmount * 0.1, display_order: 6, emoji: '🏆' },
            { id: '7', tier_name: '7th Place', tier_rank_start: 7, tier_rank_end: 7, prize_amount: baseAmount * 0.1, display_order: 7, emoji: '🏆' },
            { id: '8', tier_name: '8th Place', tier_rank_start: 8, tier_rank_end: 8, prize_amount: baseAmount * 0.1, display_order: 8, emoji: '🏆' },
            { id: '9', tier_name: '9th Place', tier_rank_start: 9, tier_rank_end: 9, prize_amount: baseAmount * 0.1, display_order: 9, emoji: '🏆' },
            { id: '10', tier_name: '10th Place', tier_rank_start: 10, tier_rank_end: 10, prize_amount: baseAmount * 0.1, display_order: 10, emoji: '🏆' }
          ];
        };

        const fallbackPrizeTiers = getPrizeTiersForCompetition(fallbackCompetition);

        const fallbackStats = {
          totalParticipants: 0,
          qualifiedParticipants: 0,
          leadingVolume: 0,
          totalPrizePool: 150000,
          minimumQualification: 2500
        };

        setCompetition(fallbackCompetition);
        setPrizeTiers(fallbackPrizeTiers);
        setLeaderboard([]);
        setStats(fallbackStats);

        console.log('✅ Using fallback competition data');
        return;
      }

      console.log('✅ Found competition:', currentCompetition);
      setCompetition(currentCompetition);

      // Load competition data in parallel
      const [prizeTiersData, leaderboardData, statsData] = await Promise.all([
        competitionService.getPrizeTiers(currentCompetition.id),
        competitionService.getLeaderboard(currentCompetition.id, 10),
        competitionService.getCompetitionStats(currentCompetition.id)
      ]);

      console.log('🏆 Prize tiers loaded:', prizeTiersData);
      console.log('📊 Leaderboard loaded:', leaderboardData);
      console.log('📈 Stats loaded:', statsData);

      setPrizeTiers(prizeTiersData);
      setLeaderboard(leaderboardData);
      setStats(statsData);

    } catch (err) {
      console.error('❌ Error loading competition data:', err);
      setError('Failed to load competition data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get current leader(s) for a prize tier
  const getCurrentLeader = (tierRankStart: number, tierRankEnd: number): string => {
    if (leaderboard.length === 0) {
      return 'No participants yet';
    }

    if (tierRankStart === tierRankEnd) {
      // Single position (1st, 2nd, 3rd place)
      const leader = leaderboard[tierRankStart - 1];
      return leader ? `${leader.username} (${formatCurrency(leader.referral_volume_usd)})` : 'Position open';
    } else {
      // Multiple positions (4th-10th place)
      const leaders = leaderboard.slice(tierRankStart - 1, tierRankEnd);
      if (leaders.length === 0) {
        return 'Positions open';
      } else if (leaders.length === 1) {
        return `${leaders[0].username} (${formatCurrency(leaders[0].referral_volume_usd)})`;
      } else {
        return `${leaders.length} leaders competing`;
      }
    }
  };

  const getCompetitionStatus = () => {
    if (!competition) return { title: 'Competition Loading...', description: 'Please wait...' };

    switch (competition.status) {
      case 'upcoming':
        return {
          title: 'Competition Starting Soon!',
          description: 'The Gold Diggers Club leaderboard will be populated as participants join the presale.'
        };
      case 'active':
        if (stats.totalParticipants === 0) {
          return {
            title: 'Competition Starting Soon!',
            description: 'The Gold Diggers Club leaderboard will be populated as participants join the presale.'
          };
        } else {
          return {
            title: 'Competition Active!',
            description: `${stats.qualifiedParticipants} qualified participants competing for ${formatCurrency(stats.totalPrizePool)} in prizes.`
          };
        }
      case 'ended':
        return {
          title: 'Competition Ended',
          description: 'This competition has concluded. Winners will be announced soon.'
        };
      default:
        return {
          title: 'Competition Status Unknown',
          description: 'Please check back later for updates.'
        };
    }
  };

  if (loading) {
    return (
      <section className="gold-diggers-section">
        <div className="container">
          <div className="section-header text-center mb-xl">
            <h2 className="section-title">🏆 Gold Diggers Club</h2>
            <p className="section-subtitle">Loading competition data...</p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="gold-diggers-section">
        <div className="container">
          <div className="section-header text-center mb-xl">
            <h2 className="section-title">🏆 Gold Diggers Club</h2>
            <p className="section-subtitle text-red-400">{error}</p>
            <div className="text-sm text-gray-400 mt-4">
              <p>Debug info: Check browser console for details</p>
              <p>If you see hardcoded amounts, please clear browser cache</p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  const statusInfo = getCompetitionStatus();

  return (
    <section className="gold-diggers-section">
      <div className="container">
        {/* Section Header */}
        <div className="section-header text-center mb-xl">
          <h2 className="section-title">🏆 Gold Diggers Club</h2>
          {competition && (
            <div className="current-competition-badge">
              <span className="competition-name">{competition.name}</span>
              <span className={`competition-status ${competition.status}`}>
                {competition.status === 'active' ? '🟢 Active' :
                 competition.status === 'ended' ? '🔴 Ended' :
                 competition.status === 'upcoming' ? '🟡 Upcoming' : '⚪ Unknown'}
              </span>
            </div>
          )}
          <p className="section-subtitle">
            {competition?.description || 'Build your network by referring new investors. Each qualified referral counts toward your ranking.'}
          </p>
          {competition?.id === 'fallback-competition' && (
            <div className="text-sm text-yellow-400 mt-2">
              ⚠️ Using fallback data - Run competition setup script for live data
            </div>
          )}
        </div>

        {/* Competition Tabs */}
        {availableCompetitions.length > 1 && (
          <div className="competition-tabs mb-xl">
            <div className="tabs-container">
              {availableCompetitions.map((comp) => (
                <button
                  key={comp.id}
                  onClick={() => setSelectedCompetitionId(comp.id)}
                  className={`tab-button ${selectedCompetitionId === comp.id ? 'active' : ''} ${loading && selectedCompetitionId === comp.id ? 'loading' : ''}`}
                  disabled={loading && selectedCompetitionId === comp.id}
                >
                  <span className="tab-icon">🏆</span>
                  <span className="tab-text">
                    {comp.name.replace('Gold Diggers Club - ', '')}
                  </span>
                  <span className="tab-status">
                    {comp.status === 'active' ? '🟢' : comp.status === 'ended' ? '🔴' : '🟡'}
                  </span>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Top Row - How it Works and Live Rankings */}
        <div className="grid grid-cols-2 gap-xl mb-xl">
          {/* How it Works Card */}
          <div className="gold-diggers-card">
            <div className="card-header">
              <span className="card-icon">💡</span>
              <h3 className="card-title">How it Works</h3>
            </div>
            <div className="card-content">
              <div className="steps-list">
                <div className="step-item">
                  <span className="step-number">1</span>
                  <div className="step-content">
                    <h4 className="step-title">Refer & Earn</h4>
                    <p className="step-description">
                      Build your network by referring new investors. Each qualified referral counts toward your ranking.
                    </p>
                  </div>
                </div>
                <div className="step-item">
                  <span className="step-number">2</span>
                  <div className="step-content">
                    <h4 className="step-title">Minimum Qualification</h4>
                    <p className="step-description">
                      Achieve minimum {formatCurrency(stats.minimumQualification)} in direct referral volume to qualify for bonus pool distribution.
                    </p>
                  </div>
                </div>
                <div className="step-item">
                  <span className="step-number">3</span>
                  <div className="step-content">
                    <h4 className="step-title">Climb the Rankings</h4>
                    <p className="step-description">
                      Your position is determined by total referral volume and network growth metrics.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Live Rankings Card */}
          <div className="gold-diggers-card">
            <div className="card-header">
              <span className="card-icon">🏆</span>
              <h3 className="card-title">Live Rankings</h3>
              <span className="live-indicator">
                <span className="live-dot"></span>
                LIVE
              </span>
            </div>
            <div className="card-content text-center">
              <div className="competition-status">
                <span className="status-icon">🏆</span>
                <h4 className="status-title">{statusInfo.title}</h4>
                <p className="status-description">{statusInfo.description}</p>
                <p className="status-subtitle">
                  Be the first to start building your network!
                </p>
              </div>

              <div className="qualification-badge">
                🎯 Minimum {formatCurrency(stats.minimumQualification)} in direct referrals to qualify
              </div>

              {/* Show top participants if any */}
              {leaderboard.length > 0 && (
                <div className="leaderboard-preview mt-4">
                  <h5 className="text-sm font-semibold text-gray-300 mb-2">Top Participants</h5>
                  {leaderboard.slice(0, 3).map((participant, index) => (
                    <div key={participant.user_id} className="flex justify-between items-center py-1 text-sm">
                      <span className="text-gray-300">
                        {index + 1}. {participant.full_name || participant.username}
                      </span>
                      <span className="text-yellow-400 font-semibold">
                        {formatCurrency(participant.total_referral_volume)}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Bottom Row - Prize Distribution and Stats */}
        <div className="grid grid-cols-3 gap-xl mb-xl">
          {/* Prize Distribution Card - Takes 2 columns */}
          <div className="col-span-2">
            <div className="gold-diggers-card">
              <div className="card-header">
                <span className="card-icon">🏆</span>
                <h3 className="card-title">Prize Distribution</h3>
              </div>
              <div className="card-content">
                <div className="prize-list">
                  {prizeTiers.map((tier) => (
                    <div key={tier.id} className={`prize-item ${tier.tier_rank_start === 1 ? 'prize-first' : tier.tier_rank_start === 2 ? 'prize-second' : tier.tier_rank_start === 3 ? 'prize-third' : 'prize-other'}`}>
                      <div className="prize-info">
                        <span className="prize-rank">
                          {tier.emoji} {tier.tier_name}
                        </span>
                        <span className="prize-amount">
                          {tier.tier_rank_start === tier.tier_rank_end
                            ? formatCurrency(tier.prize_amount)
                            : `${formatCurrency(tier.prize_amount)} each`
                          }
                        </span>
                      </div>
                      <div className="current-leader">
                        <span className="leader-label">Current Leader:</span>
                        <span className="leader-name">{getCurrentLeader(tier.tier_rank_start, tier.tier_rank_end)}</span>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="prize-note">
                  Remaining pool distributed proportionally among top 10 qualified participants
                </div>
              </div>
            </div>
          </div>

          {/* Live Stats - Takes 1 column */}
          <div className="space-y-lg">
            <div className="stat-card">
              <div className="stat-number">{stats.totalParticipants}</div>
              <div className="stat-label">Total Participants</div>
            </div>
            <div className="stat-card">
              <div className="stat-number">{formatCurrency(stats.leadingVolume)}</div>
              <div className="stat-label">Leading Volume</div>
            </div>

            {/* Join Competition Button */}
            <div className="text-center mt-lg">
              <a
                href="https://t.me/AureusAllianceBot"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-gold-diggers"
              >
                ⚡ Join the Competition
              </a>
              <p className="text-sm text-gray-400 mt-md">
                {competition?.end_date 
                  ? `Competition ends ${new Date(competition.end_date).toLocaleDateString()}`
                  : 'Competition ends when presale reaches $1,000,000 total volume'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GoldDiggersClub;
