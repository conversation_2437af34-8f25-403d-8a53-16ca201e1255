const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql, description) {
  console.log(`🔄 ${description}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ ${description} failed:`, error.message);
      return false;
    } else {
      console.log(`✅ ${description} completed`);
      return true;
    }
  } catch (err) {
    console.error(`❌ ${description} error:`, err.message);
    return false;
  }
}

async function setupCertificatesTableDirect() {
  console.log('🚀 Setting up certificates table (direct SQL approach)...');
  
  // Step 1: Create the table
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS certificates (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      purchase_id UUID REFERENCES aureus_share_purchases(id) ON DELETE SET NULL,
      certificate_number VARCHAR(50) UNIQUE NOT NULL,
      shares_count INTEGER NOT NULL,
      issue_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      status VARCHAR(20) DEFAULT 'issued' CHECK (status IN ('issued', 'revoked', 'transferred')),
      certificate_data JSONB DEFAULT '{}'::jsonb,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  if (!await executeSQL(createTableSQL, 'Creating certificates table')) {
    return false;
  }
  
  // Step 2: Create indexes
  const indexesSQL = `
    CREATE INDEX IF NOT EXISTS idx_certificates_user_id ON certificates(user_id);
    CREATE INDEX IF NOT EXISTS idx_certificates_purchase_id ON certificates(purchase_id);
    CREATE INDEX IF NOT EXISTS idx_certificates_certificate_number ON certificates(certificate_number);
    CREATE INDEX IF NOT EXISTS idx_certificates_status ON certificates(status);
  `;
  
  if (!await executeSQL(indexesSQL, 'Creating indexes')) {
    return false;
  }
  
  // Step 3: Create certificate number generation function
  const generateFunctionSQL = `
    CREATE OR REPLACE FUNCTION generate_certificate_number()
    RETURNS VARCHAR(50) AS $$
    DECLARE
      new_number VARCHAR(50);
      counter INTEGER := 1;
    BEGIN
      LOOP
        new_number := 'AUR-' || EXTRACT(YEAR FROM NOW()) || '-' || LPAD(counter::TEXT, 6, '0');
        
        IF NOT EXISTS (SELECT 1 FROM certificates WHERE certificate_number = new_number) THEN
          RETURN new_number;
        END IF;
        
        counter := counter + 1;
        
        IF counter > 999999 THEN
          RAISE EXCEPTION 'Unable to generate unique certificate number';
        END IF;
      END LOOP;
    END;
    $$ LANGUAGE plpgsql;
  `;
  
  if (!await executeSQL(generateFunctionSQL, 'Creating certificate number generation function')) {
    return false;
  }
  
  // Step 4: Create admin certificate creation function
  const adminFunctionSQL = `
    CREATE OR REPLACE FUNCTION admin_create_certificate(
      p_user_id INTEGER,
      p_purchase_id UUID,
      p_shares_count INTEGER,
      p_certificate_data JSONB DEFAULT '{}'::jsonb
    )
    RETURNS VARCHAR(50) AS $$
    DECLARE
      new_cert_number VARCHAR(50);
    BEGIN
      new_cert_number := generate_certificate_number();
      
      INSERT INTO certificates (
        user_id,
        purchase_id,
        certificate_number,
        shares_count,
        certificate_data
      ) VALUES (
        p_user_id,
        p_purchase_id,
        new_cert_number,
        p_shares_count,
        p_certificate_data
      );
      
      RETURN new_cert_number;
    END;
    $$ LANGUAGE plpgsql;
  `;
  
  if (!await executeSQL(adminFunctionSQL, 'Creating admin certificate function')) {
    return false;
  }
  
  // Step 5: Test the setup
  console.log('🧪 Testing certificate table setup...');
  
  try {
    // Test table access
    const { data: testData, error: testError } = await supabase
      .from('certificates')
      .select('count(*)')
      .limit(1);
    
    if (testError) {
      console.error('❌ Table access test failed:', testError.message);
      return false;
    }
    
    console.log('✅ Certificate table is accessible');
    
    // Test function
    const { data: funcTest, error: funcError } = await supabase
      .rpc('generate_certificate_number');
    
    if (funcError) {
      console.error('❌ Function test failed:', funcError.message);
      return false;
    }
    
    console.log('✅ Certificate functions working, sample number:', funcTest);
    
    return true;
    
  } catch (error) {
    console.error('❌ Testing failed:', error.message);
    return false;
  }
}

// Test certificate creation with real data
async function testWithRealData() {
  console.log('🧪 Testing with real purchase data...');
  
  try {
    // Find an approved purchase
    const { data: purchases, error: purchaseError } = await supabase
      .from('aureus_share_purchases')
      .select('*')
      .eq('status', 'active')
      .limit(1);
    
    if (purchaseError) {
      console.error('❌ Error finding purchases:', purchaseError.message);
      return;
    }
    
    if (!purchases || purchases.length === 0) {
      console.log('ℹ️ No approved purchases found for testing');
      return;
    }
    
    const purchase = purchases[0];
    console.log(`📋 Testing with purchase ${purchase.id} for user ${purchase.user_id}`);
    
    // Check if certificate already exists
    const { data: existingCert } = await supabase
      .from('certificates')
      .select('certificate_number')
      .eq('purchase_id', purchase.id)
      .single();
    
    if (existingCert) {
      console.log(`ℹ️ Certificate already exists: ${existingCert.certificate_number}`);
      return;
    }
    
    // Create test certificate
    const { data: certNumber, error: certError } = await supabase
      .rpc('admin_create_certificate', {
        p_user_id: purchase.user_id,
        p_purchase_id: purchase.id,
        p_shares_count: purchase.shares_purchased,
        p_certificate_data: {
          package_name: purchase.package_name,
          total_amount: purchase.total_amount,
          created_by: 'setup_test'
        }
      });
    
    if (certError) {
      console.error('❌ Certificate creation test failed:', certError.message);
    } else {
      console.log(`✅ Test certificate created successfully: ${certNumber}`);
    }
    
  } catch (error) {
    console.error('❌ Real data test failed:', error.message);
  }
}

// Main execution
if (require.main === module) {
  setupCertificatesTableDirect()
    .then((success) => {
      if (success) {
        console.log('✅ Certificate table setup completed successfully!');
        return testWithRealData();
      } else {
        console.error('❌ Certificate table setup failed');
        process.exit(1);
      }
    })
    .then(() => {
      console.log('🎉 All setup and tests completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Setup process failed:', error);
      process.exit(1);
    });
}

module.exports = { setupCertificatesTableDirect };
