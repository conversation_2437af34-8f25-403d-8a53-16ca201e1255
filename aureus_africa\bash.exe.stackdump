Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8BF0) msys-2.0.dll+0x1FEBA
0007FFFF9CF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x67F9
0007FFFF9CF0  000210046832 (000210285FF9, 0007FFFF9BA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9CF0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9CF0  0002100690B4 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9FD0  00021006A49D (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAFB500000 ntdll.dll
7FFAF9E30000 KERNEL32.DLL
7FFAF8D60000 KERNELBASE.dll
7FFAFB200000 USER32.dll
7FFAF8D30000 win32u.dll
7FFAF9770000 GDI32.dll
7FFAF9130000 gdi32full.dll
7FFAF88F0000 msvcp_win.dll
7FFAF8B20000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAF97A0000 advapi32.dll
7FFAFAE90000 msvcrt.dll
7FFAF9F60000 sechost.dll
7FFAFB0E0000 RPCRT4.dll
7FFAF7DF0000 CRYPTBASE.DLL
7FFAF86E0000 bcryptPrimitives.dll
7FFAF99D0000 IMM32.DLL
