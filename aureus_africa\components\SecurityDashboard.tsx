/**
 * COMPREHENSIVE SECURITY MONITORING DASHBOARD
 * 
 * Real-time security dashboard with live metrics, charts, alert management,
 * and bot activity monitoring integrated with Phase 4 security systems.
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';

interface SecurityMetrics {
  totalEvents: number;
  criticalAlerts: number;
  highAlerts: number;
  mediumAlerts: number;
  lowAlerts: number;
  resolvedAlerts: number;
  activeThreats: number;
  blockedIPs: number;
  suspiciousActivities: number;
  systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  botEvents: number;
  userEvents: number;
}

interface SecurityAlert {
  id: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  type: string;
  title: string;
  description: string;
  source: string;
  timestamp: Date;
  resolved: boolean;
  metadata: any;
}

interface ChartData {
  timestamp: string;
  alerts: number;
  threats: number;
  botEvents: number;
  userEvents: number;
}

const SecurityDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalEvents: 0,
    criticalAlerts: 0,
    highAlerts: 0,
    mediumAlerts: 0,
    lowAlerts: 0,
    resolvedAlerts: 0,
    activeThreats: 0,
    blockedIPs: 0,
    suspiciousActivities: 0,
    systemHealth: 'HEALTHY',
    botEvents: 0,
    userEvents: 0
  });

  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const [isLoading, setIsLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Real-time data fetching
  const fetchSecurityMetrics = useCallback(async () => {
    try {
      console.log('🔍 Fetching security metrics...');

      // Get security events from last 24 hours
      const timeRange = getTimeRange(selectedTimeRange);
      const { data: events, error: eventsError } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .gte('created_at', timeRange.toISOString())
        .order('created_at', { ascending: false });

      if (eventsError) {
        console.error('❌ Error fetching events:', eventsError);
        return;
      }

      // Get active alerts
      const { data: alertsData, error: alertsError } = await supabase
        .from('security_alerts')
        .select('*')
        .eq('resolved', false)
        .order('created_at', { ascending: false })
        .limit(50);

      if (alertsError) {
        console.error('❌ Error fetching alerts:', alertsError);
      }

      // Get blocked IPs
      const { data: blockedIPs, error: blockedError } = await supabase
        .from('blocked_ips')
        .select('*')
        .eq('is_active', true);

      if (blockedError) {
        console.error('❌ Error fetching blocked IPs:', blockedError);
      }

      // Process metrics
      const processedMetrics = processSecurityMetrics(events || [], alertsData || [], blockedIPs || []);
      setMetrics(processedMetrics);

      // Process alerts
      const processedAlerts = (alertsData || []).map(alert => ({
        id: alert.alert_id,
        severity: alert.severity,
        type: alert.type,
        title: alert.title,
        description: alert.description,
        source: alert.source,
        timestamp: new Date(alert.created_at),
        resolved: alert.resolved,
        metadata: alert.metadata
      }));
      setAlerts(processedAlerts);

      // Process chart data
      const processedChartData = processChartData(events || [], selectedTimeRange);
      setChartData(processedChartData);

      console.log('✅ Security metrics updated');

    } catch (error) {
      console.error('❌ Error fetching security metrics:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedTimeRange]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    fetchSecurityMetrics();

    if (autoRefresh) {
      const interval = setInterval(fetchSecurityMetrics, 30000);
      return () => clearInterval(interval);
    }
  }, [fetchSecurityMetrics, autoRefresh]);

  // Handle alert resolution
  const handleResolveAlert = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from('security_alerts')
        .update({ 
          resolved: true, 
          resolved_at: new Date().toISOString() 
        })
        .eq('alert_id', alertId);

      if (error) {
        console.error('❌ Error resolving alert:', error);
        return;
      }

      // Update local state
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? { ...alert, resolved: true } : alert
      ));

      // Log resolution
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_email: 'security_dashboard',
          action: 'ALERT_RESOLVED',
          target_type: 'security_alert',
          target_id: alertId,
          metadata: {
            resolvedBy: 'dashboard_user',
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

      console.log('✅ Alert resolved:', alertId);

    } catch (error) {
      console.error('❌ Error resolving alert:', error);
    }
  };

  // Get system health color
  const getHealthColor = (health: string) => {
    switch (health) {
      case 'HEALTHY': return 'text-green-600 bg-green-100';
      case 'WARNING': return 'text-yellow-600 bg-yellow-100';
      case 'CRITICAL': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get alert severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-500 text-white';
      case 'HIGH': return 'bg-orange-500 text-white';
      case 'MEDIUM': return 'bg-yellow-500 text-white';
      case 'LOW': return 'bg-blue-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-lg">Loading security dashboard...</span>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Security Monitoring Dashboard</h1>
            <p className="text-gray-600 mt-1">Real-time security monitoring and threat detection</p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Auto-refresh toggle */}
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">Auto-refresh</span>
            </label>

            {/* Time range selector */}
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="1h">Last Hour</option>
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
            </select>

            {/* Refresh button */}
            <button
              onClick={fetchSecurityMetrics}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* System Health Status */}
      <div className="mb-6">
        <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${getHealthColor(metrics.systemHealth)}`}>
          <div className="w-2 h-2 rounded-full bg-current mr-2"></div>
          System Health: {metrics.systemHealth}
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Events */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Events</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalEvents.toLocaleString()}</p>
            </div>
          </div>
        </div>

        {/* Active Threats */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Threats</p>
              <p className="text-2xl font-bold text-red-600">{metrics.activeThreats}</p>
            </div>
          </div>
        </div>

        {/* Blocked IPs */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Blocked IPs</p>
              <p className="text-2xl font-bold text-orange-600">{metrics.blockedIPs}</p>
            </div>
          </div>
        </div>

        {/* Bot vs User Events */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Bot Events</p>
              <p className="text-2xl font-bold text-green-600">{metrics.botEvents}</p>
              <p className="text-xs text-gray-500">User: {metrics.userEvents}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Alert Severity Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Alert Summary */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alert Summary</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Critical</span>
              <span className="px-2 py-1 bg-red-500 text-white text-xs rounded-full">{metrics.criticalAlerts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">High</span>
              <span className="px-2 py-1 bg-orange-500 text-white text-xs rounded-full">{metrics.highAlerts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Medium</span>
              <span className="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full">{metrics.mediumAlerts}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Low</span>
              <span className="px-2 py-1 bg-blue-500 text-white text-xs rounded-full">{metrics.lowAlerts}</span>
            </div>
            <div className="flex justify-between items-center pt-2 border-t">
              <span className="text-sm font-medium text-gray-900">Resolved</span>
              <span className="px-2 py-1 bg-green-500 text-white text-xs rounded-full">{metrics.resolvedAlerts}</span>
            </div>
          </div>
        </div>

        {/* Security Events Chart Placeholder */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Events Over Time</h3>
          <div className="h-48 flex items-center justify-center bg-gray-50 rounded">
            <div className="text-center">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p className="text-gray-500">Chart visualization</p>
              <p className="text-xs text-gray-400">Install chart library for full visualization</p>
            </div>
          </div>
        </div>
      </div>

      {/* Active Alerts */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Active Security Alerts</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {alerts.length === 0 ? (
            <div className="px-6 py-8 text-center">
              <svg className="w-12 h-12 text-green-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-gray-500">No active security alerts</p>
              <p className="text-sm text-gray-400">Your system is secure</p>
            </div>
          ) : (
            alerts.map((alert) => (
              <div key={alert.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                        {alert.severity}
                      </span>
                      <h4 className="ml-3 text-sm font-medium text-gray-900">{alert.title}</h4>
                    </div>
                    <p className="mt-1 text-sm text-gray-600">{alert.description}</p>
                    <div className="mt-2 flex items-center text-xs text-gray-500">
                      <span>Source: {alert.source}</span>
                      <span className="mx-2">•</span>
                      <span>{alert.timestamp.toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <button
                      onClick={() => handleResolveAlert(alert.id)}
                      className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      Resolve
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

// Helper functions
function getTimeRange(range: string): Date {
  const now = new Date();
  switch (range) {
    case '1h': return new Date(now.getTime() - 60 * 60 * 1000);
    case '24h': return new Date(now.getTime() - 24 * 60 * 60 * 1000);
    case '7d': return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    case '30d': return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    default: return new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }
}

function processSecurityMetrics(events: any[], alerts: any[], blockedIPs: any[]): SecurityMetrics {
  const botEvents = events.filter(e => 
    e.admin_email?.includes('bot') || 
    e.admin_email?.includes('system') ||
    e.metadata?.isBot === true
  ).length;

  const userEvents = events.length - botEvents;

  const criticalAlerts = alerts.filter(a => a.severity === 'CRITICAL').length;
  const highAlerts = alerts.filter(a => a.severity === 'HIGH').length;
  const mediumAlerts = alerts.filter(a => a.severity === 'MEDIUM').length;
  const lowAlerts = alerts.filter(a => a.severity === 'LOW').length;

  const activeThreats = criticalAlerts + highAlerts;
  
  let systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';
  if (criticalAlerts > 0 || activeThreats > 10) {
    systemHealth = 'CRITICAL';
  } else if (highAlerts > 5 || activeThreats > 5) {
    systemHealth = 'WARNING';
  }

  return {
    totalEvents: events.length,
    criticalAlerts,
    highAlerts,
    mediumAlerts,
    lowAlerts,
    resolvedAlerts: 0, // Would need separate query for resolved alerts
    activeThreats,
    blockedIPs: blockedIPs.length,
    suspiciousActivities: events.filter(e => e.action?.includes('SUSPICIOUS')).length,
    systemHealth,
    botEvents,
    userEvents
  };
}

function processChartData(events: any[], timeRange: string): ChartData[] {
  // Simplified chart data processing
  // In a real implementation, you'd group events by time intervals
  const intervals = timeRange === '1h' ? 12 : timeRange === '24h' ? 24 : 7;
  const data: ChartData[] = [];
  
  for (let i = 0; i < intervals; i++) {
    data.push({
      timestamp: new Date(Date.now() - (intervals - i) * 60 * 60 * 1000).toISOString(),
      alerts: Math.floor(Math.random() * 10),
      threats: Math.floor(Math.random() * 5),
      botEvents: Math.floor(Math.random() * 20),
      userEvents: Math.floor(Math.random() * 15)
    });
  }
  
  return data;
}

export default SecurityDashboard;
