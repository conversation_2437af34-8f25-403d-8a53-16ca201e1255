import React, { useState, useEffect, useRef } from 'react'
import { supabase } from '../../lib/supabase'

interface User {
  id: number
  username: string
  email: string
  full_name: string
  is_active: boolean
}

interface ReferralRelationship {
  id: string
  referrer_id: number
  referred_id: number
  referral_code: string
  commission_rate: number
  total_commission: number
  status: string
  created_at: string
  referrer_username?: string
  referrer_full_name?: string
  referred_username?: string
  referred_full_name?: string
}

interface SponsorManagerProps {
  currentUser?: any
}

export const SponsorManager: React.FC<SponsorManagerProps> = ({ currentUser }) => {
  const [users, setUsers] = useState<User[]>([])
  const [referrals, setReferrals] = useState<ReferralRelationship[]>([])
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [selectedSponsor, setSelectedSponsor] = useState<User | null>(null)
  const [changeReason, setChangeReason] = useState('')
  const [loading, setLoading] = useState(false)
  const [userSearchTerm, setUserSearchTerm] = useState('')
  const [sponsorSearchTerm, setSponsorSearchTerm] = useState('')
  const [showUserDropdown, setShowUserDropdown] = useState(false)
  const [showSponsorDropdown, setShowSponsorDropdown] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info', text: string } | null>(null)

  const userDropdownRef = useRef<HTMLDivElement>(null)
  const sponsorDropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadUsers()
    loadReferrals()
  }, [])

  // Handle clicks outside dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setShowUserDropdown(false)
      }
      if (sponsorDropdownRef.current && !sponsorDropdownRef.current.contains(event.target as Node)) {
        setShowSponsorDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, username, email, full_name, is_active')
        .eq('is_active', true)
        .order('username')

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('Error loading users:', error)
      setMessage({ type: 'error', text: 'Failed to load users' })
    }
  }

  const loadReferrals = async () => {
    try {
      const { data, error } = await supabase
        .from('referrals')
        .select(`
          id,
          referrer_id,
          referred_id,
          referral_code,
          commission_rate,
          total_commission,
          status,
          created_at,
          referrer:users!referrals_referrer_id_fkey (username, full_name),
          referred:users!referrals_referred_id_fkey (username, full_name)
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false })

      if (error) throw error
      
      const formattedReferrals = (data || []).map(ref => ({
        ...ref,
        referrer_username: ref.referrer?.username,
        referrer_full_name: ref.referrer?.full_name,
        referred_username: ref.referred?.username,
        referred_full_name: ref.referred?.full_name
      }))
      
      setReferrals(formattedReferrals)
    } catch (error) {
      console.error('Error loading referrals:', error)
      setMessage({ type: 'error', text: 'Failed to load referral relationships' })
    }
  }

  const getCurrentSponsor = (userId: number) => {
    return referrals.find(ref => ref.referred_id === userId && ref.status === 'active')
  }

  // Format user display text
  const formatUserDisplay = (user: User) => {
    return `${user.username} - ${user.full_name || 'No Name'} (ID: ${user.id}) - ${user.email}`
  }

  // Filter users for user selection dropdown
  const getFilteredUsers = () => {
    return users.filter(user =>
      user.username.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
      (user.full_name && user.full_name.toLowerCase().includes(userSearchTerm.toLowerCase()))
    )
  }

  // Filter users for sponsor selection dropdown (exclude selected user)
  const getFilteredSponsors = () => {
    return users.filter(user =>
      user.id !== selectedUser?.id && // Prevent self-sponsoring
      (user.username.toLowerCase().includes(sponsorSearchTerm.toLowerCase()) ||
       user.email.toLowerCase().includes(sponsorSearchTerm.toLowerCase()) ||
       (user.full_name && user.full_name.toLowerCase().includes(sponsorSearchTerm.toLowerCase())))
    )
  }

  // Handle user selection
  const handleUserSelect = (user: User) => {
    setSelectedUser(user)
    setUserSearchTerm(formatUserDisplay(user))
    setShowUserDropdown(false)
    // Reset sponsor selection when user changes
    setSelectedSponsor(null)
    setSponsorSearchTerm('')
  }

  // Handle sponsor selection
  const handleSponsorSelect = (sponsor: User) => {
    setSelectedSponsor(sponsor)
    setSponsorSearchTerm(formatUserDisplay(sponsor))
    setShowSponsorDropdown(false)
  }

  const handleChangeSponsor = async () => {
    if (!selectedUser || !selectedSponsor) {
      setMessage({ type: 'error', text: 'Please select both a user and a sponsor' })
      return
    }

    setLoading(true)
    setMessage(null)

    try {
      // Validate sponsor change
      if (selectedUser.id === selectedSponsor.id) {
        throw new Error('User cannot sponsor themselves')
      }

      const currentSponsor = getCurrentSponsor(selectedUser.id)
      if (currentSponsor && currentSponsor.referrer_id === selectedSponsor.id) {
        throw new Error('User already has this sponsor')
      }

      // Check for circular reference
      const isCircular = await checkCircularReference(selectedUser.id, selectedSponsor.id)
      if (isCircular) {
        throw new Error('Circular reference detected - new sponsor is in user\'s downline')
      }

      // Perform sponsor change
      await performSponsorChange(selectedUser, selectedSponsor, currentSponsor)

      setMessage({
        type: 'success',
        text: `Successfully changed sponsor for ${selectedUser.username} to ${selectedSponsor.username}`
      })

      // Reset form
      setSelectedUser(null)
      setSelectedSponsor(null)
      setUserSearchTerm('')
      setSponsorSearchTerm('')
      setChangeReason('')

      // Reload data
      await loadReferrals()

    } catch (error: any) {
      console.error('Error changing sponsor:', error)
      setMessage({ type: 'error', text: error.message || 'Failed to change sponsor' })
    } finally {
      setLoading(false)
    }
  }

  const checkCircularReference = async (userId: number, newSponsorId: number, visited = new Set<number>()): Promise<boolean> => {
    if (visited.has(newSponsorId)) {
      return true
    }

    visited.add(newSponsorId)

    const sponsorReferral = referrals.find(ref => ref.referred_id === newSponsorId && ref.status === 'active')
    
    if (sponsorReferral && sponsorReferral.referrer_id === userId) {
      return true
    }

    if (sponsorReferral && sponsorReferral.referrer_id) {
      return await checkCircularReference(userId, sponsorReferral.referrer_id, visited)
    }

    return false
  }

  const performSponsorChange = async (user: User, newSponsor: User, currentSponsor?: ReferralRelationship) => {
    // Deactivate current referral if exists
    if (currentSponsor) {
      const { error: deactivateError } = await supabase
        .from('referrals')
        .update({
          status: 'inactive',
          updated_at: new Date().toISOString()
        })
        .eq('id', currentSponsor.id)

      if (deactivateError) throw deactivateError
    }

    // Create new referral relationship
    const newReferralCode = `${newSponsor.username}_${user.id}_${Date.now()}`
    
    const { error: createError } = await supabase
      .from('referrals')
      .insert({
        referrer_id: newSponsor.id,
        referred_id: user.id,
        referral_code: newReferralCode,
        commission_rate: 15.00,
        total_commission: 0,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (createError) throw createError

    // Create audit log
    const auditDetails = {
      user_id: user.id,
      user_username: user.username,
      old_sponsor_id: currentSponsor?.referrer_id || null,
      old_sponsor_username: currentSponsor?.referrer_username || null,
      new_sponsor_id: newSponsor.id,
      new_sponsor_username: newSponsor.username,
      reason: changeReason || 'Admin sponsor change',
      admin_user: currentUser?.email || 'admin',
      timestamp: new Date().toISOString()
    }

    await supabase
      .from('admin_audit_logs')
      .insert({
        admin_telegram_id: 0,
        admin_username: currentUser?.email || 'admin',
        action: 'SPONSOR_CHANGE',
        target_type: 'user_sponsor',
        target_id: user.id.toString(),
        details: JSON.stringify(auditDetails),
        timestamp: new Date().toISOString()
      })
  }



  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Sponsor Management</h2>
        <div className="text-sm text-gray-400">
          Total Users: {users.length} | Active Referrals: {referrals.length}
        </div>
      </div>

      {message && (
        <div className={`p-4 rounded-lg border ${
          message.type === 'success' ? 'bg-green-900/20 border-green-500/30 text-green-300' :
          message.type === 'error' ? 'bg-red-900/20 border-red-500/30 text-red-300' :
          'bg-blue-900/20 border-blue-500/30 text-blue-300'
        }`}>
          {message.text}
        </div>
      )}

      {/* Sponsor Change Form */}
      <div className="glass-card p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Change User Sponsor</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* User Selection Dropdown */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Search & Select User
            </label>
            <div className="relative" ref={userDropdownRef}>
              <input
                type="text"
                placeholder="Search by username, email, or name..."
                value={userSearchTerm}
                onChange={(e) => {
                  setUserSearchTerm(e.target.value)
                  setShowUserDropdown(true)
                  if (!e.target.value) {
                    setSelectedUser(null)
                  }
                }}
                onFocus={() => setShowUserDropdown(true)}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                autoComplete="off"
              />

              {showUserDropdown && (
                <div className="absolute z-20 w-full mt-1 max-h-48 overflow-y-auto border border-gray-600 rounded-lg bg-gray-800 shadow-xl backdrop-blur-sm">
                  {getFilteredUsers().length > 0 ? (
                    <>
                      {getFilteredUsers().length > 5 && (
                        <div className="px-3 py-2 bg-gray-700 text-xs text-gray-300 border-b border-gray-600">
                          {getFilteredUsers().length} users found
                        </div>
                      )}
                      {getFilteredUsers().map(user => {
                      const currentSponsor = getCurrentSponsor(user.id)
                      const isSelected = selectedUser?.id === user.id
                      return (
                        <div
                          key={user.id}
                          onClick={() => handleUserSelect(user)}
                          className={`p-3 cursor-pointer hover:bg-gray-700 border-b border-gray-700 last:border-b-0 transition-colors ${
                            isSelected ? 'bg-yellow-500/20 border-yellow-500/30' : ''
                          }`}
                        >
                          <div className="font-medium text-white">{user.username} <span className="text-yellow-400">(ID: {user.id})</span></div>
                          <div className="text-sm text-gray-400">{user.full_name || 'No Name'}</div>
                          <div className="text-xs text-gray-500">{user.email}</div>
                          <div className="text-xs text-gray-500">
                            Current Sponsor: {currentSponsor?.referrer_username || 'None'}
                          </div>
                        </div>
                      )
                    })}
                    </>
                  ) : (
                    <div className="p-3 text-gray-400 text-sm">No users found</div>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="space-y-4">
            {/* Selected User Display */}
            {selectedUser && (
              <div className="p-4 bg-gray-800 rounded-lg border border-gray-600">
                <h4 className="font-medium text-white mb-2">Selected User</h4>
                <div className="text-sm text-gray-300">
                  <div><strong>ID:</strong> <span className="text-yellow-400">{selectedUser.id}</span></div>
                  <div><strong>Username:</strong> {selectedUser.username}</div>
                  <div><strong>Email:</strong> {selectedUser.email}</div>
                  <div><strong>Name:</strong> {selectedUser.full_name || 'N/A'}</div>
                  <div><strong>Current Sponsor:</strong> {getCurrentSponsor(selectedUser.id)?.referrer_username || 'None'}</div>
                </div>
              </div>
            )}

            {/* Sponsor Selection Dropdown */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Select New Sponsor
              </label>
              <div className="relative" ref={sponsorDropdownRef}>
                <input
                  type="text"
                  placeholder={selectedUser ? "Search for new sponsor..." : "Select a user first"}
                  value={sponsorSearchTerm}
                  onChange={(e) => {
                    setSponsorSearchTerm(e.target.value)
                    setShowSponsorDropdown(true)
                    if (!e.target.value) {
                      setSelectedSponsor(null)
                    }
                  }}
                  onFocus={() => selectedUser && setShowSponsorDropdown(true)}
                  disabled={!selectedUser}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-yellow-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                  autoComplete="off"
                />

                {showSponsorDropdown && selectedUser && (
                  <div className="absolute z-20 w-full mt-1 max-h-48 overflow-y-auto border border-gray-600 rounded-lg bg-gray-800 shadow-xl backdrop-blur-sm">
                    {getFilteredSponsors().length > 0 ? (
                      <>
                        {getFilteredSponsors().length > 5 && (
                          <div className="px-3 py-2 bg-gray-700 text-xs text-gray-300 border-b border-gray-600">
                            {getFilteredSponsors().length} eligible sponsors found
                          </div>
                        )}
                        {getFilteredSponsors().map(sponsor => {
                        const isSelected = selectedSponsor?.id === sponsor.id
                        return (
                          <div
                            key={sponsor.id}
                            onClick={() => handleSponsorSelect(sponsor)}
                            className={`p-3 cursor-pointer hover:bg-gray-700 border-b border-gray-700 last:border-b-0 transition-colors ${
                              isSelected ? 'bg-green-500/20 border-green-500/30' : ''
                            }`}
                          >
                            <div className="font-medium text-white">{sponsor.username} <span className="text-green-400">(ID: {sponsor.id})</span></div>
                            <div className="text-sm text-gray-400">{sponsor.full_name || 'No Name'}</div>
                            <div className="text-xs text-gray-500">{sponsor.email}</div>
                          </div>
                        )
                      })}
                      </>
                    ) : (
                      <div className="p-3 text-gray-400 text-sm">No eligible sponsors found</div>
                    )}
                  </div>
                )}
              </div>

              {selectedSponsor && (
                <div className="mt-2 p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
                  <div className="text-sm text-green-300">
                    <strong>Selected Sponsor:</strong> {selectedSponsor.username} (ID: {selectedSponsor.id}) - {selectedSponsor.full_name || 'No Name'}
                  </div>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Reason for Change (Optional)
              </label>
              <textarea
                placeholder="Enter reason for sponsor change..."
                value={changeReason}
                onChange={(e) => setChangeReason(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              />
            </div>

            <button
              onClick={handleChangeSponsor}
              disabled={loading || !selectedUser || !selectedSponsor}
              className="w-full py-3 px-4 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-bold rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                  Changing Sponsor...
                </div>
              ) : (
                'Change Sponsor'
              )}
            </button>

            {(!selectedUser || !selectedSponsor) && (
              <div className="text-sm text-gray-400 text-center mt-2">
                {!selectedUser ? 'Please select a user first' : 'Please select a sponsor'}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Current Referral Relationships */}
      <div className="glass-card p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Current Referral Relationships</h3>
        
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-600">
                <th className="text-left py-3 px-4 text-gray-300">Referred User</th>
                <th className="text-left py-3 px-4 text-gray-300">Sponsor</th>
                <th className="text-left py-3 px-4 text-gray-300">Commission Rate</th>
                <th className="text-left py-3 px-4 text-gray-300">Total Commission</th>
                <th className="text-left py-3 px-4 text-gray-300">Created</th>
              </tr>
            </thead>
            <tbody>
              {referrals.slice(0, 20).map(referral => (
                <tr key={referral.id} className="border-b border-gray-700 hover:bg-gray-800/50">
                  <td className="py-3 px-4 text-white">
                    <div>{referral.referred_username}</div>
                    <div className="text-xs text-gray-400">{referral.referred_full_name}</div>
                  </td>
                  <td className="py-3 px-4 text-white">
                    <div>{referral.referrer_username}</div>
                    <div className="text-xs text-gray-400">{referral.referrer_full_name}</div>
                  </td>
                  <td className="py-3 px-4 text-gray-300">{referral.commission_rate}%</td>
                  <td className="py-3 px-4 text-gray-300">${referral.total_commission.toFixed(2)}</td>
                  <td className="py-3 px-4 text-gray-400">
                    {new Date(referral.created_at).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {referrals.length > 20 && (
          <div className="mt-4 text-center text-gray-400">
            Showing 20 of {referrals.length} relationships
          </div>
        )}
      </div>
    </div>
  )
}
