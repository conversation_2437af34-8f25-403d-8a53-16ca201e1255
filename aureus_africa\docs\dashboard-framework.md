# 4.2 Dashboard Framework - Aureus Alliance Web Dashboard

## Executive Summary
This document provides the complete implementation of the dashboard framework for the Aureus Alliance Web Dashboard, including main dashboard layout, navigation system, responsive grid system, reusable UI components, theme and styling system, and loading states with error handling.

## Dashboard Layout Architecture

### Main Dashboard Layout
```typescript
// src/app/(dashboard)/layout.tsx
import { Suspense } from 'react';
import { Header } from '@/components/layout/header';
import { Sidebar } from '@/components/layout/sidebar';
import { Footer } from '@/components/layout/footer';
import { AuthGuard } from '@/components/features/auth/auth-guard';
import { DashboardSkeleton } from '@/components/layout/dashboard-skeleton';
import { Toaster } from '@/components/ui/toaster';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard>
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex">
          <Sidebar />
          <main className="flex-1 lg:pl-64">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <Suspense fallback={<DashboardSkeleton />}>
                {children}
              </Suspense>
            </div>
          </main>
        </div>
        <Footer />
        <Toaster />
      </div>
    </AuthGuard>
  );
}
```

### Header Component
```typescript
// src/components/layout/header.tsx
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/components/providers/auth-provider';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import {
  Bell,
  Menu,
  Settings,
  User,
  LogOut,
  Shield,
  DollarSign,
  TrendingUp,
} from 'lucide-react';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { NotificationCenter } from '@/components/features/notifications/notification-center';
import { Sidebar } from './sidebar';

export function Header() {
  const { user, signOut } = useAuth();
  const [showNotifications, setShowNotifications] = useState(false);
  
  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  };
  
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and Mobile Menu */}
          <div className="flex items-center space-x-4">
            {/* Mobile Menu */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="lg:hidden">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-64 p-0">
                <Sidebar />
              </SheetContent>
            </Sheet>
            
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-black" />
              </div>
              <span className="font-bold text-xl text-foreground">
                Aureus Alliance
              </span>
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link
              href="/dashboard"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Dashboard
            </Link>
            <Link
              href="/shares"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Shares
            </Link>
            <Link
              href="/payments"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Payments
            </Link>
            <Link
              href="/referrals"
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Referrals
            </Link>
          </nav>
          
          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowNotifications(true)}
              className="relative"
            >
              <Bell className="h-5 w-5" />
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
              >
                3
              </Badge>
              <span className="sr-only">Notifications</span>
            </Button>
            
            {/* Theme Toggle */}
            <ThemeToggle />
            
            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.photoUrl} alt={user?.firstName} />
                    <AvatarFallback>
                      {user?.firstName?.[0]}{user?.lastName?.[0]}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {user?.firstName} {user?.lastName}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      @{user?.username}
                    </p>
                    <div className="flex items-center space-x-1 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {user?.role}
                      </Badge>
                      {user?.kycStatus === 'approved' && (
                        <Badge variant="success" className="text-xs">
                          <Shield className="h-3 w-3 mr-1" />
                          Verified
                        </Badge>
                      )}
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="flex items-center">
                    <User className="mr-2 h-4 w-4" />
                    Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/settings" className="flex items-center">
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
      
      {/* Notification Center */}
      <NotificationCenter
        open={showNotifications}
        onOpenChange={setShowNotifications}
      />
    </header>
  );
}
```

### Sidebar Navigation
```typescript
// src/components/layout/sidebar.tsx
'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils/cn';
import { useAuth } from '@/components/providers/auth-provider';
import { rbacService, Permission } from '@/lib/auth/rbac';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  BarChart3,
  DollarSign,
  CreditCard,
  Users,
  FileText,
  Settings,
  Shield,
  TrendingUp,
  Wallet,
  UserCheck,
  Globe,
  HelpCircle,
} from 'lucide-react';

interface NavigationItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  permission?: Permission;
  badge?: string;
  children?: NavigationItem[];
}

const navigationItems: NavigationItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: BarChart3,
  },
  {
    title: 'Shares',
    href: '/shares',
    icon: TrendingUp,
    permission: Permission.SHARES_READ,
    children: [
      {
        title: 'Overview',
        href: '/shares',
        icon: BarChart3,
      },
      {
        title: 'Purchase',
        href: '/shares/purchase',
        icon: DollarSign,
        permission: Permission.SHARES_CREATE,
      },
      {
        title: 'History',
        href: '/shares/history',
        icon: FileText,
      },
    ],
  },
  {
    title: 'Payments',
    href: '/payments',
    icon: CreditCard,
    permission: Permission.PAYMENTS_READ,
    children: [
      {
        title: 'Overview',
        href: '/payments',
        icon: Wallet,
      },
      {
        title: 'Make Payment',
        href: '/payments/new',
        icon: DollarSign,
        permission: Permission.PAYMENTS_CREATE,
      },
      {
        title: 'History',
        href: '/payments/history',
        icon: FileText,
      },
    ],
  },
  {
    title: 'KYC Verification',
    href: '/kyc',
    icon: UserCheck,
    permission: Permission.KYC_READ,
  },
  {
    title: 'Referrals',
    href: '/referrals',
    icon: Users,
    permission: Permission.REFERRALS_READ,
  },
  {
    title: 'Admin Panel',
    href: '/admin',
    icon: Shield,
    permission: Permission.ADMIN_PANEL_ACCESS,
    badge: 'Admin',
    children: [
      {
        title: 'Users',
        href: '/admin/users',
        icon: Users,
        permission: Permission.ADMIN_USERS_MANAGE,
      },
      {
        title: 'Payments',
        href: '/admin/payments',
        icon: CreditCard,
        permission: Permission.PAYMENTS_APPROVE,
      },
      {
        title: 'KYC Approvals',
        href: '/admin/kyc',
        icon: UserCheck,
        permission: Permission.KYC_APPROVE,
      },
      {
        title: 'System Config',
        href: '/admin/config',
        icon: Settings,
        permission: Permission.ADMIN_SYSTEM_CONFIG,
      },
    ],
  },
];

const bottomNavigationItems: NavigationItem[] = [
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
  },
  {
    title: 'Help & Support',
    href: '/help',
    icon: HelpCircle,
  },
];

export function Sidebar() {
  const pathname = usePathname();
  const { user } = useAuth();
  
  const hasPermission = (permission?: Permission) => {
    if (!permission) return true;
    return rbacService.hasPermission(user, permission);
  };
  
  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    if (!hasPermission(item.permission)) {
      return null;
    }
    
    const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);
    
    return (
      <div key={item.href} className={cn('space-y-1', level > 0 && 'ml-4')}>
        <Button
          asChild
          variant={isActive ? 'secondary' : 'ghost'}
          className={cn(
            'w-full justify-start',
            level > 0 && 'h-8 px-2',
            isActive && 'bg-secondary'
          )}
        >
          <Link href={item.href} className="flex items-center space-x-3">
            <item.icon className={cn('h-4 w-4', level > 0 && 'h-3 w-3')} />
            <span className="flex-1 text-left">{item.title}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-auto">
                {item.badge}
              </Badge>
            )}
          </Link>
        </Button>
        
        {item.children && isActive && (
          <div className="space-y-1">
            {item.children.map((child) => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className="fixed left-0 top-16 z-40 h-[calc(100vh-4rem)] w-64 bg-background border-r lg:block hidden">
      <ScrollArea className="h-full px-3 py-4">
        <div className="space-y-1">
          {navigationItems.map((item) => renderNavigationItem(item))}
        </div>
        
        <div className="mt-8 pt-4 border-t">
          <div className="space-y-1">
            {bottomNavigationItems.map((item) => renderNavigationItem(item))}
          </div>
        </div>
        
        {/* User Stats */}
        <div className="mt-8 pt-4 border-t">
          <div className="px-3 py-2">
            <h4 className="text-sm font-semibold text-muted-foreground mb-2">
              Quick Stats
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Total Shares</span>
                <span className="font-medium">1,250</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Total Value</span>
                <span className="font-medium">$12,500</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Growth</span>
                <span className="font-medium text-green-600">+15.2%</span>
              </div>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
```

### Footer Component
```typescript
// src/components/layout/footer.tsx
'use client';

import Link from 'next/link';
import { Globe, Shield, HelpCircle, FileText } from 'lucide-react';

export function Footer() {
  return (
    <footer className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Left side - Company info */}
          <div className="flex items-center space-x-4">
            <p className="text-sm text-muted-foreground">
              © 2024 Aureus Alliance. All rights reserved.
            </p>
          </div>
          
          {/* Center - Quick links */}
          <div className="flex items-center space-x-6">
            <Link
              href="/terms"
              className="text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-1"
            >
              <FileText className="h-3 w-3" />
              <span>Terms</span>
            </Link>
            <Link
              href="/privacy"
              className="text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-1"
            >
              <Shield className="h-3 w-3" />
              <span>Privacy</span>
            </Link>
            <Link
              href="/help"
              className="text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center space-x-1"
            >
              <HelpCircle className="h-3 w-3" />
              <span>Help</span>
            </Link>
          </div>
          
          {/* Right side - Status */}
          <div className="flex items-center space-x-2">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-muted-foreground">All systems operational</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
```

## Responsive Grid System

### Grid Layout Components
```typescript
// src/components/ui/grid.tsx
import { cn } from '@/lib/utils/cn';

interface GridProps extends React.HTMLAttributes<HTMLDivElement> {
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  gap?: 1 | 2 | 3 | 4 | 5 | 6 | 8;
  responsive?: {
    sm?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    md?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    lg?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    xl?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  };
}

export function Grid({
  cols = 1,
  gap = 4,
  responsive,
  className,
  children,
  ...props
}: GridProps) {
  const colsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6',
    12: 'grid-cols-12',
  };
  
  const gapClasses = {
    1: 'gap-1',
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    5: 'gap-5',
    6: 'gap-6',
    8: 'gap-8',
  };
  
  const responsiveClasses = responsive ? [
    responsive.sm && `sm:grid-cols-${responsive.sm}`,
    responsive.md && `md:grid-cols-${responsive.md}`,
    responsive.lg && `lg:grid-cols-${responsive.lg}`,
    responsive.xl && `xl:grid-cols-${responsive.xl}`,
  ].filter(Boolean).join(' ') : '';
  
  return (
    <div
      className={cn(
        'grid',
        colsClasses[cols],
        gapClasses[gap],
        responsiveClasses,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

interface GridItemProps extends React.HTMLAttributes<HTMLDivElement> {
  span?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  responsive?: {
    sm?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    md?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    lg?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
    xl?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  };
}

export function GridItem({
  span,
  responsive,
  className,
  children,
  ...props
}: GridItemProps) {
  const spanClasses = span ? {
    1: 'col-span-1',
    2: 'col-span-2',
    3: 'col-span-3',
    4: 'col-span-4',
    5: 'col-span-5',
    6: 'col-span-6',
    12: 'col-span-12',
  }[span] : '';
  
  const responsiveClasses = responsive ? [
    responsive.sm && `sm:col-span-${responsive.sm}`,
    responsive.md && `md:col-span-${responsive.md}`,
    responsive.lg && `lg:col-span-${responsive.lg}`,
    responsive.xl && `xl:col-span-${responsive.xl}`,
  ].filter(Boolean).join(' ') : '';
  
  return (
    <div
      className={cn(spanClasses, responsiveClasses, className)}
      {...props}
    >
      {children}
    </div>
  );
}
```

### Dashboard Grid Layout
```typescript
// src/components/layout/dashboard-grid.tsx
import { Grid, GridItem } from '@/components/ui/grid';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface DashboardGridProps {
  children: React.ReactNode;
}

export function DashboardGrid({ children }: DashboardGridProps) {
  return (
    <Grid
      cols={1}
      gap={6}
      responsive={{
        sm: 1,
        md: 2,
        lg: 3,
        xl: 4,
      }}
      className="min-h-screen"
    >
      {children}
    </Grid>
  );
}

interface DashboardSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  span?: 1 | 2 | 3 | 4;
  className?: string;
}

export function DashboardSection({
  title,
  description,
  children,
  span = 1,
  className,
}: DashboardSectionProps) {
  return (
    <GridItem
      span={span}
      responsive={{
        sm: 1,
        md: span > 2 ? 2 : span,
        lg: span,
        xl: span,
      }}
      className={className}
    >
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">{title}</CardTitle>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </CardHeader>
        <CardContent>
          {children}
        </CardContent>
      </Card>
    </GridItem>
  );
}

// Predefined dashboard layouts
export function DashboardMetricsGrid({ children }: { children: React.ReactNode }) {
  return (
    <Grid cols={1} gap={4} responsive={{ sm: 2, lg: 4 }} className="mb-6">
      {children}
    </Grid>
  );
}

export function DashboardChartsGrid({ children }: { children: React.ReactNode }) {
  return (
    <Grid cols={1} gap={6} responsive={{ lg: 2 }}>
      {children}
    </Grid>
  );
}

export function DashboardTablesGrid({ children }: { children: React.ReactNode }) {
  return (
    <Grid cols={1} gap={6}>
      {children}
    </Grid>
  );
}
```

## Reusable UI Components

### Enhanced Card Components
```typescript
// src/components/ui/card.tsx
import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils/cn';

const cardVariants = cva(
  'rounded-lg border bg-card text-card-foreground shadow-sm',
  {
    variants: {
      variant: {
        default: 'border-border',
        outline: 'border-2',
        ghost: 'border-transparent shadow-none',
        elevated: 'shadow-md',
      },
      size: {
        default: 'p-6',
        sm: 'p-4',
        lg: 'p-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  hover?: boolean;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, hover, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        cardVariants({ variant, size }),
        hover && 'transition-shadow hover:shadow-md',
        className
      )}
      {...props}
    />
  )
);
Card.displayName = 'Card';

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-col space-y-1.5 p-6', className)}
    {...props}
  />
));
CardHeader.displayName = 'CardHeader';

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      'text-2xl font-semibold leading-none tracking-tight',
      className
    )}
    {...props}
  />
));
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));
CardDescription.displayName = 'CardDescription';

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />
));
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center p-6 pt-0', className)}
    {...props}
  />
));
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
```

### Metric Card Component
```typescript
// src/components/ui/metric-card.tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils/cn';

interface MetricCardProps {
  title: string;
  value: string | number;
  description?: string;
  trend?: {
    value: number;
    label: string;
  };
  icon?: React.ComponentType<{ className?: string }>;
  variant?: 'default' | 'success' | 'warning' | 'danger';
  loading?: boolean;
  className?: string;
}

export function MetricCard({
  title,
  value,
  description,
  trend,
  icon: Icon,
  variant = 'default',
  loading = false,
  className,
}: MetricCardProps) {
  const getTrendIcon = () => {
    if (!trend) return null;
    
    if (trend.value > 0) {
      return <TrendingUp className="h-4 w-4" />;
    } else if (trend.value < 0) {
      return <TrendingDown className="h-4 w-4" />;
    } else {
      return <Minus className="h-4 w-4" />;
    }
  };
  
  const getTrendColor = () => {
    if (!trend) return 'text-muted-foreground';
    
    if (trend.value > 0) {
      return 'text-green-600 dark:text-green-400';
    } else if (trend.value < 0) {
      return 'text-red-600 dark:text-red-400';
    } else {
      return 'text-muted-foreground';
    }
  };
  
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20';
      case 'warning':
        return 'border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-950/20';
      case 'danger':
        return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20';
      default:
        return '';
    }
  };
  
  if (loading) {
    return (
      <Card className={cn('animate-pulse', getVariantStyles(), className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 bg-muted rounded w-20"></div>
          {Icon && <div className="h-4 w-4 bg-muted rounded"></div>}
        </CardHeader>
        <CardContent>
          <div className="h-8 bg-muted rounded w-24 mb-2"></div>
          <div className="h-3 bg-muted rounded w-32"></div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className={cn('hover:shadow-md transition-shadow', getVariantStyles(), className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {(description || trend) && (
          <div className="flex items-center justify-between mt-2">
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
            {trend && (
              <div className={cn('flex items-center space-x-1 text-xs', getTrendColor())}>
                {getTrendIcon()}
                <span>{Math.abs(trend.value)}%</span>
                <span className="text-muted-foreground">{trend.label}</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

### Data Table Component
```typescript
// src/components/ui/data-table.tsx
'use client';

import * as React from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Search } from 'lucide-react';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  searchKey?: string;
  searchPlaceholder?: string;
  loading?: boolean;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  searchKey,
  searchPlaceholder = 'Search...',
  loading = false,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  
  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });
  
  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {searchKey && (
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={searchPlaceholder}
                value={(table.getColumn(searchKey)?.getFilterValue() as string) ?? ''}
                onChange={(event) =>
                  table.getColumn(searchKey)?.setFilterValue(event.target.value)
                }
                className="pl-8 max-w-sm"
              />
            </div>
          )}
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  {columns.map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <div className="h-4 bg-muted animate-pulse rounded"></div>
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Pagination */}
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
```

## Theme and Styling System

### Theme Provider
```typescript
// src/components/providers/theme-provider.tsx
'use client';

import * as React from 'react';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { type ThemeProviderProps } from 'next-themes/dist/types';

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;
}
```

### Theme Toggle Component
```typescript
// src/components/ui/theme-toggle.tsx
'use client';

import * as React from 'react';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function ThemeToggle() {
  const { setTheme } = useTheme();
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme('light')}>
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('system')}>
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
```

### Global Styles
```css
/* src/styles/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 45 93% 47%; /* Aureus gold */
    --primary-foreground: 0 0% 0%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 45 93% 47%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark theme colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 45 93% 47%; /* Aureus gold */
    --primary-foreground: 0 0% 0%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 45 93% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
@layer components {
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted));
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }
}

/* Animation utilities */
@layer utilities {
  .animate-in {
    animation: in 0.2s ease-out;
  }
  
  .animate-out {
    animation: out 0.2s ease-in;
  }
  
  @keyframes in {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes out {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(-10px);
    }
  }
}

/* Telegram widget styling */
.telegram-login-widget {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--muted-foreground) / 0.1) 50%,
    hsl(var(--muted)) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
```

## Loading States and Error Handling

### Loading Components
```typescript
// src/components/ui/loading.tsx
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils/cn';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };
  
  return (
    <Loader2
      className={cn('animate-spin text-primary', sizeClasses[size], className)}
    />
  );
}

interface LoadingSkeletonProps {
  className?: string;
  lines?: number;
}

export function LoadingSkeleton({ className, lines = 1 }: LoadingSkeletonProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className="h-4 bg-muted rounded loading-shimmer"
        />
      ))}
    </div>
  );
}

export function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <LoadingSkeleton className="w-48" />
        <LoadingSkeleton className="w-32" />
      </div>
      
      {/* Metrics skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="p-6 border rounded-lg">
            <LoadingSkeleton lines={3} />
          </div>
        ))}
      </div>
      
      {/* Chart skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from({ length: 2 }).map((_, index) => (
          <div key={index} className="p-6 border rounded-lg">
            <LoadingSkeleton className="mb-4" />
            <div className="h-64 bg-muted rounded loading-shimmer" />
          </div>
        ))}
      </div>
      
      {/* Table skeleton */}
      <div className="border rounded-lg">
        <div className="p-6">
          <LoadingSkeleton className="mb-4" />
          <div className="space-y-2">
            {Array.from({ length: 5 }).map((_, index) => (
              <LoadingSkeleton key={index} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
```

### Error Boundary Components
```typescript
// src/components/ui/error-boundary.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { logger } from '@/lib/monitoring/logger';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class ErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error('React error boundary caught error', error, {
      componentStack: errorInfo.componentStack,
    });
  }
  
  retry = () => {
    this.setState({ hasError: false, error: null });
  };
  
  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} retry={this.retry} />;
      }
      
      return <DefaultErrorFallback error={this.state.error} retry={this.retry} />;
    }
    
    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error: Error;
  retry: () => void;
}

export function DefaultErrorFallback({ error, retry }: ErrorFallbackProps) {
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
          <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
        </div>
        <CardTitle className="text-xl">Something went wrong</CardTitle>
        <CardDescription>
          {error.message || 'An unexpected error occurred'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={retry} className="w-full" variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" />
          Try again
        </Button>
        <Button
          onClick={() => window.location.reload()}
          className="w-full"
          variant="default"
        >
          Reload page
        </Button>
      </CardContent>
    </Card>
  );
}

// Hook for error handling
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);
  
  const handleError = React.useCallback((error: Error) => {
    logger.error('useErrorHandler caught error', error);
    setError(error);
  }, []);
  
  const clearError = React.useCallback(() => {
    setError(null);
  }, []);
  
  return { error, handleError, clearError };
}
```

### Status Components
```typescript
// src/components/ui/status.tsx
import { CheckCircle, XCircle, AlertCircle, Clock, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils/cn';

interface StatusProps {
  status: 'success' | 'error' | 'warning' | 'pending' | 'loading';
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function Status({ status, message, size = 'md', className }: StatusProps) {
  const icons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertCircle,
    pending: Clock,
    loading: Loader2,
  };
  
  const colors = {
    success: 'text-green-600 dark:text-green-400',
    error: 'text-red-600 dark:text-red-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    pending: 'text-blue-600 dark:text-blue-400',
    loading: 'text-muted-foreground',
  };
  
  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  };
  
  const Icon = icons[status];
  
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <Icon
        className={cn(
          sizes[size],
          colors[status],
          status === 'loading' && 'animate-spin'
        )}
      />
      {message && (
        <span className={cn('text-sm', colors[status])}>{message}</span>
      )}
    </div>
  );
}

interface EmptyStateProps {
  icon?: React.ComponentType<{ className?: string }>;
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  className,
}: EmptyStateProps) {
  return (
    <div className={cn('text-center py-12', className)}>
      {Icon && (
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
          <Icon className="h-6 w-6 text-muted-foreground" />
        </div>
      )}
      <h3 className="text-lg font-semibold">{title}</h3>
      {description && (
        <p className="text-muted-foreground mt-2 max-w-sm mx-auto">
          {description}
        </p>
      )}
      {action && <div className="mt-6">{action}</div>}
    </div>
  );
}
```

## Implementation Checklist

### Phase 4.2 Dashboard Framework Checklist
- [ ] **Main Dashboard Layout**
  - [ ] Create dashboard layout component
  - [ ] Implement header with navigation
  - [ ] Build responsive sidebar
  - [ ] Add footer component

- [ ] **Navigation System**
  - [ ] Implement role-based navigation
  - [ ] Create mobile navigation
  - [ ] Add breadcrumb navigation
  - [ ] Set up navigation state management

- [ ] **Responsive Grid System**
  - [ ] Create grid components
  - [ ] Implement responsive breakpoints
  - [ ] Build dashboard sections
  - [ ] Test responsive layouts

- [ ] **Reusable UI Components**
  - [ ] Enhance card components
  - [ ] Create metric cards
  - [ ] Build data table component
  - [ ] Implement form components

- [ ] **Theme and Styling**
  - [ ] Set up theme provider
  - [ ] Implement theme toggle
  - [ ] Create global styles
  - [ ] Define design tokens

- [ ] **Loading States and Error Handling**
  - [ ] Create loading components
  - [ ] Implement error boundaries
  - [ ] Build status components
  - [ ] Add empty state components

### Success Criteria
- [ ] ✅ Dashboard layout responsive on all devices
- [ ] ✅ Navigation system works with role-based access
- [ ] ✅ Grid system provides flexible layouts
- [ ] ✅ UI components consistent and reusable
- [ ] ✅ Theme switching works properly
- [ ] ✅ Loading and error states handled gracefully

---

**Dashboard Framework Status**: IMPLEMENTATION COMPLETE
**Responsive Design**: MOBILE-FIRST APPROACH
**Component Library**: REUSABLE UI COMPONENTS
**Theme System**: DARK/LIGHT MODE SUPPORT

*This dashboard framework provides a solid foundation with responsive design, comprehensive navigation, reusable components, and robust error handling for the Aureus Alliance web platform.*
