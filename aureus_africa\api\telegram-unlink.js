// API endpoint to unlink Telegram account from web user
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { user_id } = req.body;

    if (!user_id) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Find the linked Telegram account
    const { data: telegramUser, error: findError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('user_id', parseInt(user_id))
      .single();

    if (findError && findError.code !== 'PGRST116') {
      console.error('Error finding linked Telegram user:', findError);
      return res.status(500).json({ error: 'Database error' });
    }

    if (!telegramUser) {
      return res.status(404).json({ error: 'No linked Telegram account found' });
    }

    // Unlink the account by setting user_id to null
    // Keep the telegram_users record but remove the link
    const { error: updateError } = await supabase
      .from('telegram_users')
      .update({
        user_id: null,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', parseInt(user_id));

    if (updateError) {
      console.error('Error unlinking accounts:', updateError);
      return res.status(500).json({ error: 'Failed to unlink accounts' });
    }

    console.log(`✅ Successfully unlinked web user ${user_id} from Telegram ${telegramUser.telegram_id}`);

    res.status(200).json({
      success: true,
      message: 'Telegram account unlinked successfully'
    });

  } catch (error) {
    console.error('Telegram unlinking error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
