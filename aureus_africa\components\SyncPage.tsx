import React, { useState, useEffect } from 'react';
import { TelegramSyncHandler } from '../lib/telegramSyncHandler';
import { AccountSyncService } from '../lib/accountSyncService';

interface SyncPageProps {
  syncToken: string;
  user: any;
  onSyncComplete: () => void;
  onBack: () => void;
}

export const SyncPage: React.FC<SyncPageProps> = ({ syncToken, user, onSyncComplete, onBack }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [syncData, setSyncData] = useState<any>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  useEffect(() => {
    loadSyncData();
  }, [syncToken]);

  const loadSyncData = async () => {
    if (!syncToken) return;

    setIsLoading(true);
    try {
      // Get sync request details
      const response = await fetch(`/api/auth/sync-token?token=${syncToken}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Invalid sync token');
      }

      setSyncData(data);
      setShowConfirmation(true);
    } catch (err: any) {
      setError(err.message || 'Failed to load sync data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmSync = async () => {
    if (!user?.id || !syncToken) return;

    setIsLoading(true);
    setError(null);

    try {
      const success = await TelegramSyncHandler.processSyncToken(syncToken, user.id);
      
      if (success) {
        setSuccess('Accounts successfully linked! Your Telegram data is now available on the web dashboard.');
        setTimeout(() => {
          onSyncComplete();
        }, 2000);
      } else {
        throw new Error('Failed to link accounts');
      }
    } catch (err: any) {
      setError(err.message || 'Account linking failed');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && !showConfirmation) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading sync request...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 p-6">
        <div className="container mx-auto">
          <button
            onClick={onBack}
            className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors duration-200 group"
          >
            <svg className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span className="font-medium">Back to Dashboard</span>
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 px-4 py-8 flex items-center justify-center min-h-[80vh]">
        <div className="w-full max-w-2xl mx-auto">
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-600/30">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-white mb-2">Link Telegram Account</h1>
              <p className="text-gray-400">
                Connect your Telegram bot account with your web dashboard for seamless access to all your data.
              </p>
            </div>

            {/* Error/Success Messages */}
            {error && (
              <div className="mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                <p className="text-red-400 text-center">{error}</p>
              </div>
            )}
            
            {success && (
              <div className="mb-6 p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                <p className="text-green-400 text-center">{success}</p>
              </div>
            )}

            {/* Sync Confirmation */}
            {showConfirmation && syncData && (
              <div className="space-y-6">
                {/* Account Details */}
                <div className="bg-gray-700/50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-white mb-4">Account Linking Details</h3>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Web Account */}
                    <div className="space-y-3">
                      <h4 className="font-medium text-blue-400">Web Account</h4>
                      <div className="text-sm text-gray-300 space-y-1">
                        <p><strong>Email:</strong> {user?.email}</p>
                        <p><strong>Name:</strong> {user?.user_metadata?.full_name || 'Not set'}</p>
                        <p><strong>Status:</strong> <span className="text-green-400">Active</span></p>
                      </div>
                    </div>

                    {/* Telegram Account */}
                    <div className="space-y-3">
                      <h4 className="font-medium text-purple-400">Telegram Account</h4>
                      <div className="text-sm text-gray-300 space-y-1">
                        <p><strong>Telegram ID:</strong> {syncData.telegram_id}</p>
                        <p><strong>Username:</strong> @{syncData.telegram_username || 'Not set'}</p>
                        <p><strong>Status:</strong> <span className="text-green-400">Verified</span></p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* What will be synced */}
                <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-6">
                  <h3 className="text-green-400 font-semibold mb-3">🔄 What will be synced:</h3>
                  <ul className="text-sm text-gray-300 space-y-2">
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      All your Telegram bot purchase history
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Referral relationships and commission balances
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Share certificates and ownership records
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Access to web-exclusive features and tools
                    </li>
                  </ul>
                </div>

                {/* Warning */}
                <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                  <h4 className="font-semibold text-yellow-400 mb-2">⚠️ Important</h4>
                  <p className="text-sm text-gray-300">
                    This will permanently link your Telegram and web accounts. All data will be merged and accessible from both platforms. 
                    This action cannot be undone.
                  </p>
                </div>

                {/* Actions */}
                <div className="flex gap-4">
                  <button
                    onClick={onBack}
                    className="flex-1 px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleConfirmSync}
                    disabled={isLoading}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg font-medium transition-all duration-200 disabled:opacity-50"
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                        Linking Accounts...
                      </span>
                    ) : (
                      'Confirm Account Link'
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};
