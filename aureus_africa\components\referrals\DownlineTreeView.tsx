import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

interface DownlineTreeViewProps {
  userId: number;
  className?: string;
}

interface TreeNode {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  telegram_username?: string;
  total_referrals: number;
  total_earnings: number;
  created_at: string;
  is_active: boolean;
  level: number;
  children: TreeNode[];
  parent_id?: number;
}

interface TreeStats {
  totalNodes: number;
  activeNodes: number;
  totalLevels: number;
  totalEarnings: number;
}

export const DownlineTreeView: React.FC<DownlineTreeViewProps> = ({ userId, className = '' }) => {
  const [treeData, setTreeData] = useState<TreeNode | null>(null);
  const [treeStats, setTreeStats] = useState<TreeStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set([userId]));
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);
  const [viewMode, setViewMode] = useState<'tree' | 'list'>('tree');
  const [maxDepth, setMaxDepth] = useState(3);
  const [showInactive, setShowInactive] = useState(true);

  useEffect(() => {
    loadDownlineData();
  }, [userId, maxDepth, showInactive]);

  const loadDownlineData = async () => {
    setLoading(true);
    try {
      // Get all users in the downline
      const { data: allUsers, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          first_name,
          last_name,
          email,
          telegram_username,
          total_referrals,
          total_earnings,
          created_at,
          is_active
        `);

      if (error) throw error;

      // Get referral relationships
      const { data: referrals, error: referralError } = await supabase
        .from('referrals')
        .select('referrer_id, referred_id')
        .eq('status', 'active');

      if (referralError) throw referralError;

      // Build the tree structure
      const tree = buildTree(allUsers || [], referrals || [], userId, 0);
      setTreeData(tree);

      // Calculate statistics
      const stats = calculateTreeStats(tree);
      setTreeStats(stats);

    } catch (error) {
      console.error('Error loading downline data:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildTree = (users: any[], referrals: any[], rootId: number, level: number): TreeNode | null => {
    const user = users.find(u => u.id === rootId);
    if (!user || level > maxDepth) return null;

    // Find direct referrals
    const directReferrals = referrals
      .filter(r => r.referrer_id === rootId)
      .map(r => r.referred_id);

    // Build children recursively
    const children = directReferrals
      .map(childId => buildTree(users, referrals, childId, level + 1))
      .filter(child => child !== null && (showInactive || child.is_active)) as TreeNode[];

    return {
      id: user.id,
      username: user.username,
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      email: user.email,
      telegram_username: user.telegram_username,
      total_referrals: user.total_referrals || 0,
      total_earnings: user.total_earnings || 0,
      created_at: user.created_at,
      is_active: user.is_active,
      level,
      children,
      parent_id: level > 0 ? rootId : undefined
    };
  };

  const calculateTreeStats = (node: TreeNode | null): TreeStats => {
    if (!node) return { totalNodes: 0, activeNodes: 0, totalLevels: 0, totalEarnings: 0 };

    let totalNodes = 1;
    let activeNodes = node.is_active ? 1 : 0;
    let totalLevels = node.level + 1;
    let totalEarnings = node.total_earnings;

    const processChildren = (children: TreeNode[]) => {
      children.forEach(child => {
        totalNodes++;
        if (child.is_active) activeNodes++;
        totalLevels = Math.max(totalLevels, child.level + 1);
        totalEarnings += child.total_earnings;
        processChildren(child.children);
      });
    };

    processChildren(node.children);

    return { totalNodes, activeNodes, totalLevels, totalEarnings };
  };

  const toggleNode = (nodeId: number) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const contactUser = (node: TreeNode) => {
    if (node.telegram_username) {
      window.open(`https://t.me/${node.telegram_username}`);
    } else if (node.email) {
      window.open(`mailto:${node.email}`);
    } else {
      alert('No contact information available for this user.');
    }
  };

  const renderTreeNode = (node: TreeNode, isLast: boolean = false, prefix: string = '') => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children.length > 0;
    const isSelected = selectedNode?.id === node.id;

    return (
      <div key={node.id} className="select-none">
        {/* Node Row */}
        <div 
          className={`flex items-center p-2 rounded-lg cursor-pointer transition-colors ${
            isSelected ? 'bg-blue-600/20 border border-blue-500/30' : 'hover:bg-gray-700/50'
          }`}
          onClick={() => setSelectedNode(node)}
        >
          {/* Tree Lines */}
          <div className="flex items-center mr-3 text-gray-500">
            <span className="font-mono text-sm">{prefix}</span>
            <span className="font-mono text-sm">{isLast ? '└─' : '├─'}</span>
          </div>

          {/* Expand/Collapse Button */}
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleNode(node.id);
              }}
              className="mr-2 w-4 h-4 flex items-center justify-center text-gray-400 hover:text-white"
            >
              {isExpanded ? '▼' : '▶'}
            </button>
          )}

          {/* User Avatar */}
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${
            node.is_active ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
          }`}>
            {node.first_name?.[0] || node.username[0].toUpperCase()}
          </div>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <span className="text-white font-medium truncate">
                {node.first_name} {node.last_name} (@{node.username})
              </span>
              {!node.is_active && (
                <span className="text-xs bg-red-600 text-white px-2 py-1 rounded">Inactive</span>
              )}
            </div>
            <div className="text-sm text-gray-400">
              Level {node.level} • {node.total_referrals} referrals • ${node.total_earnings.toFixed(2)} earned
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            {(node.telegram_username || node.email) && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  contactUser(node);
                }}
                className="text-blue-400 hover:text-blue-300 text-sm"
                title="Contact user"
              >
                💬
              </button>
            )}
            <span className="text-xs text-gray-500">
              {new Date(node.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="ml-6">
            {node.children.map((child, index) => {
              const isLastChild = index === node.children.length - 1;
              const childPrefix = prefix + (isLast ? '  ' : '│ ');
              return renderTreeNode(child, isLastChild, childPrefix);
            })}
          </div>
        )}
      </div>
    );
  };

  const renderListView = (node: TreeNode, allNodes: TreeNode[] = []) => {
    allNodes.push(node);
    node.children.forEach(child => renderListView(child, allNodes));
    return allNodes;
  };

  if (loading) {
    return (
      <div className={`${className} text-center p-8`}>
        <div className="text-2xl mb-4">🌳</div>
        <div className="text-gray-400">Loading your downline...</div>
      </div>
    );
  }

  if (!treeData) {
    return (
      <div className={`${className} text-center p-8`}>
        <div className="text-4xl mb-4">🌱</div>
        <h3 className="text-white text-xl mb-4">No Downline Yet</h3>
        <p className="text-gray-400 mb-6">
          Start building your network by sharing your referral link!
        </p>
      </div>
    );
  }

  const allNodes = viewMode === 'list' ? renderListView(treeData) : [];

  return (
    <div className={className}>
      {/* Header Controls */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-white text-2xl font-bold">🌳 Your Downline</h2>
          <p className="text-gray-400">Manage and view your referral network</p>
        </div>
        <div className="flex items-center space-x-4">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('tree')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewMode === 'tree' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
              }`}
            >
              🌳 Tree
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewMode === 'list' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
              }`}
            >
              📋 List
            </button>
          </div>

          {/* Filters */}
          <select
            value={maxDepth}
            onChange={(e) => setMaxDepth(Number(e.target.value))}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-1 text-white text-sm"
          >
            <option value={2}>2 Levels</option>
            <option value={3}>3 Levels</option>
            <option value={5}>5 Levels</option>
            <option value={10}>All Levels</option>
          </select>

          <label className="flex items-center space-x-2 text-sm text-gray-300">
            <input
              type="checkbox"
              checked={showInactive}
              onChange={(e) => setShowInactive(e.target.checked)}
              className="rounded"
            />
            <span>Show Inactive</span>
          </label>
        </div>
      </div>

      {/* Statistics */}
      {treeStats && (
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-800 rounded-lg p-4 text-center border border-gray-700">
            <div className="text-2xl font-bold text-blue-400">{treeStats.totalNodes}</div>
            <div className="text-sm text-gray-400">Total Members</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 text-center border border-gray-700">
            <div className="text-2xl font-bold text-green-400">{treeStats.activeNodes}</div>
            <div className="text-sm text-gray-400">Active Members</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 text-center border border-gray-700">
            <div className="text-2xl font-bold text-purple-400">{treeStats.totalLevels}</div>
            <div className="text-sm text-gray-400">Max Depth</div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4 text-center border border-gray-700">
            <div className="text-2xl font-bold text-yellow-400">${treeStats.totalEarnings.toFixed(2)}</div>
            <div className="text-sm text-gray-400">Total Earnings</div>
          </div>
        </div>
      )}

      {/* Tree/List View */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        {viewMode === 'tree' ? (
          <div className="space-y-1">
            {renderTreeNode(treeData, true)}
          </div>
        ) : (
          <div className="space-y-2">
            {allNodes.map((node, index) => (
              <div key={node.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    node.is_active ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300'
                  }`}>
                    {node.first_name?.[0] || node.username[0].toUpperCase()}
                  </div>
                  <div>
                    <div className="text-white font-medium">
                      {node.first_name} {node.last_name} (@{node.username})
                    </div>
                    <div className="text-sm text-gray-400">
                      Level {node.level} • {node.total_referrals} referrals • ${node.total_earnings.toFixed(2)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {(node.telegram_username || node.email) && (
                    <button
                      onClick={() => contactUser(node)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                    >
                      Contact
                    </button>
                  )}
                  <span className="text-xs text-gray-500">
                    {new Date(node.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Selected Node Details */}
      {selectedNode && (
        <div className="mt-6 bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h3 className="text-white text-lg font-semibold mb-4">👤 Member Details</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm text-gray-400">Name</label>
              <div className="text-white">{selectedNode.first_name} {selectedNode.last_name}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Username</label>
              <div className="text-white">@{selectedNode.username}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Level</label>
              <div className="text-white">{selectedNode.level}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Status</label>
              <div className={selectedNode.is_active ? 'text-green-400' : 'text-red-400'}>
                {selectedNode.is_active ? 'Active' : 'Inactive'}
              </div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Total Referrals</label>
              <div className="text-white">{selectedNode.total_referrals}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Total Earnings</label>
              <div className="text-white">${selectedNode.total_earnings.toFixed(2)}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Joined</label>
              <div className="text-white">{new Date(selectedNode.created_at).toLocaleDateString()}</div>
            </div>
            <div>
              <label className="text-sm text-gray-400">Contact</label>
              <div className="flex space-x-2">
                {selectedNode.telegram_username && (
                  <button
                    onClick={() => window.open(`https://t.me/${selectedNode.telegram_username}`)}
                    className="text-blue-400 hover:text-blue-300"
                  >
                    ✈️ Telegram
                  </button>
                )}
                {selectedNode.email && (
                  <button
                    onClick={() => window.open(`mailto:${selectedNode.email}`)}
                    className="text-green-400 hover:text-green-300"
                  >
                    📧 Email
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
