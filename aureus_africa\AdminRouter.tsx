import React, { useState, useEffect } from 'react';
import { AdminLogin } from './components/AdminLogin';
import { AdminDashboard } from './components/AdminDashboard';
import { getCurrentUser } from './lib/supabase';
import { checkAdminStatus } from './lib/adminAuth';

interface AdminRouterProps {
  onBackToMain: () => void;
}

export const AdminRouter: React.FC<AdminRouterProps> = ({ onBackToMain }) => {
  const [adminUser, setAdminUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in and is an admin
    const checkAuth = async () => {
      try {
        console.log('🔍 AdminRouter: Checking existing session...');
        const currentUser = await getCurrentUser();

        if (currentUser?.email) {
          console.log('🔍 AdminRouter: Found user session, checking admin status:', currentUser.email);
          const adminUser = await checkAdminStatus(currentUser.email);

          if (adminUser) {
            console.log('✅ AdminRouter: Admin session restored:', adminUser.email, adminUser.role);
            setAdminUser({ ...currentUser, adminUser });
          } else {
            console.log('❌ AdminRouter: User is not an admin, clearing session');
            setAdminUser(null);
          }
        } else {
          console.log('🔍 AdminRouter: No user session found');
        }

        setLoading(false);
      } catch (error) {
        console.error('AdminRouter: Auth check failed:', error);
        setAdminUser(null);
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Handle admin login success
  const handleAdminLogin = (user: any) => {
    setAdminUser(user);
  };

  // Handle admin logout
  const handleAdminLogout = () => {
    setAdminUser(null);
    onBackToMain();
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading admin portal...</p>
        </div>
      </div>
    );
  }

  // Render admin dashboard if logged in
  if (adminUser) {
    return <AdminDashboard user={adminUser} onLogout={handleAdminLogout} />;
  }

  // Render admin login
  return (
    <>
      <AdminLogin onLoginSuccess={handleAdminLogin} />
      <div className="fixed top-4 left-4 z-50">
        <button
          onClick={onBackToMain}
          className="px-4 py-2 bg-gray-800/80 hover:bg-gray-700/80 text-gray-300 hover:text-white rounded-lg transition-colors text-sm backdrop-blur-sm border border-gray-600"
        >
          ← Back to Main Site
        </button>
      </div>
    </>
  );
};
