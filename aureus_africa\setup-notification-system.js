const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeSQL(sql, description) {
  console.log(`🔄 ${description}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ ${description} failed:`, error.message);
      return false;
    } else {
      console.log(`✅ ${description} completed`);
      return true;
    }
  } catch (err) {
    console.error(`❌ ${description} error:`, err.message);
    return false;
  }
}

async function setupNotificationSystem() {
  console.log('🚀 Setting up notification system...');
  
  // Step 1: Create user_notifications table
  const createNotificationsTableSQL = `
    CREATE TABLE IF NOT EXISTS user_notifications (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      notification_type VARCHAR(50) NOT NULL,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      
      -- Metadata for different notification types
      metadata JSONB DEFAULT '{}',
      
      -- Status tracking
      is_read BOOLEAN DEFAULT FALSE,
      is_archived BOOLEAN DEFAULT FALSE,
      priority VARCHAR(20) DEFAULT 'normal',
      
      -- Related entities (optional foreign keys)
      payment_id UUID,
      commission_id UUID,
      referral_id UUID,
      
      -- Timestamps
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      read_at TIMESTAMP WITH TIME ZONE,
      archived_at TIMESTAMP WITH TIME ZONE,
      
      -- Constraints
      CONSTRAINT valid_notification_type CHECK (
        notification_type IN (
          'payment_approved', 'payment_rejected', 'commission_earned', 
          'system', 'referral', 'withdrawal', 'share_purchase', 
          'phase_change', 'account_update', 'security_alert'
        )
      ),
      CONSTRAINT valid_priority CHECK (priority IN ('low', 'normal', 'high', 'urgent'))
    );
  `;
  
  if (!await executeSQL(createNotificationsTableSQL, 'Creating user_notifications table')) {
    return false;
  }
  
  // Step 2: Create indexes
  const indexesSQL = `
    CREATE INDEX IF NOT EXISTS idx_user_notifications_user_id ON user_notifications(user_id);
    CREATE INDEX IF NOT EXISTS idx_user_notifications_type ON user_notifications(notification_type);
    CREATE INDEX IF NOT EXISTS idx_user_notifications_read ON user_notifications(is_read);
    CREATE INDEX IF NOT EXISTS idx_user_notifications_created_at ON user_notifications(created_at);
  `;
  
  if (!await executeSQL(indexesSQL, 'Creating notification indexes')) {
    return false;
  }
  
  // Step 3: Create notification templates table
  const createTemplatesTableSQL = `
    CREATE TABLE IF NOT EXISTS notification_templates (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      template_key VARCHAR(100) UNIQUE NOT NULL,
      notification_type VARCHAR(50) NOT NULL,
      title_template VARCHAR(255) NOT NULL,
      message_template TEXT NOT NULL,
      
      -- Template variables (for documentation)
      variables JSONB DEFAULT '[]',
      
      -- Status
      is_active BOOLEAN DEFAULT TRUE,
      
      -- Timestamps
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `;
  
  if (!await executeSQL(createTemplatesTableSQL, 'Creating notification_templates table')) {
    return false;
  }
  
  // Step 4: Insert default notification templates
  const insertTemplatesSQL = `
    INSERT INTO notification_templates (template_key, notification_type, title_template, message_template, variables) VALUES
    ('payment_approved', 'payment_approved', 
     '✅ Payment Approved - {{amount}} USDT', 
     'Great news! Your payment of {{amount}} USDT has been approved and processed.\\n\\n📊 Share Allocation:\\n• Shares Purchased: {{shares}} shares\\n• Price per Share: ${{price_per_share}}\\n• Total Investment: {{amount}} USDT\\n\\n💰 Your shares are now active and earning dividends!\\n\\nTransaction ID: {{payment_id}}\\nProcessed: {{processed_date}}',
     '["amount", "shares", "price_per_share", "payment_id", "processed_date"]'),
    
    ('payment_rejected', 'payment_rejected',
     '❌ Payment Rejected - {{amount}} USDT',
     'Unfortunately, your payment of {{amount}} USDT has been rejected.\\n\\n📋 Rejection Details:\\n• Reason: {{rejection_reason}}\\n• Transaction ID: {{payment_id}}\\n• Submitted: {{submitted_date}}\\n\\n💡 Next Steps:\\n1. Review the rejection reason above\\n2. Correct any issues with your payment proof\\n3. Resubmit your payment\\n\\nNeed help? Contact our support team.',
     '["amount", "rejection_reason", "payment_id", "submitted_date"]'),
    
    ('commission_earned', 'commission_earned',
     '💰 Commission Earned - {{commission_amount}} USDT',
     'Congratulations! You\\'ve earned a new commission from your referral network.\\n\\n💵 Commission Details:\\n• Amount: {{commission_amount}} USDT\\n• From: {{referred_user}}\\n• Purchase Amount: {{purchase_amount}} USDT\\n• Commission Rate: {{commission_rate}}%\\n\\n📈 Your Total Earnings:\\n• USDT Balance: {{total_usdt_balance}} USDT\\n• Share Balance: {{total_share_balance}} shares\\n\\nKeep growing your network to earn more!',
     '["commission_amount", "referred_user", "purchase_amount", "commission_rate", "total_usdt_balance", "total_share_balance"]'),
    
    ('system_alert', 'system',
     '📢 System Alert: {{alert_title}}',
     '{{alert_message}}',
     '["alert_title", "alert_message"]'),
    
    ('welcome_message', 'system',
     '🎉 Welcome to Aureus Africa!',
     'Welcome to the Aureus Africa community! We\\'re excited to have you join our gold mining venture.\\n\\n🚀 Getting Started:\\n1. Complete your KYC verification\\n2. Explore the current share offerings\\n3. Set up your referral links to earn commissions\\n4. Join our Telegram community for updates\\n\\n💡 Need help? Check out our FAQ or contact support.\\n\\nHappy investing!',
     '[]')
    ON CONFLICT (template_key) DO NOTHING;
  `;
  
  if (!await executeSQL(insertTemplatesSQL, 'Inserting default notification templates')) {
    return false;
  }
  
  // Step 5: Create notification summary view
  const createSummaryViewSQL = `
    CREATE OR REPLACE VIEW user_notification_summary AS
    SELECT 
      u.id as user_id,
      u.username,
      COUNT(n.id) as total_notifications,
      COUNT(CASE WHEN n.is_read = FALSE THEN 1 END) as unread_count,
      COUNT(CASE WHEN n.notification_type = 'payment_approved' THEN 1 END) as payment_approved_count,
      COUNT(CASE WHEN n.notification_type = 'payment_rejected' THEN 1 END) as payment_rejected_count,
      COUNT(CASE WHEN n.notification_type = 'commission_earned' THEN 1 END) as commission_count,
      MAX(n.created_at) as last_notification_date
    FROM users u
    LEFT JOIN user_notifications n ON u.id = n.user_id AND n.is_archived = FALSE
    GROUP BY u.id, u.username;
  `;
  
  if (!await executeSQL(createSummaryViewSQL, 'Creating notification summary view')) {
    return false;
  }
  
  // Step 6: Set permissions
  const permissionsSQL = `
    GRANT SELECT, INSERT, UPDATE ON user_notifications TO authenticated;
    GRANT SELECT ON notification_templates TO authenticated;
    GRANT SELECT ON user_notification_summary TO authenticated;
  `;
  
  if (!await executeSQL(permissionsSQL, 'Setting notification permissions')) {
    return false;
  }
  
  // Step 7: Create sample notifications for existing users
  console.log('📝 Creating sample notifications for existing users...');
  
  try {
    // Get existing users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, username, email')
      .limit(10);
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError.message);
    } else if (users && users.length > 0) {
      console.log(`📋 Found ${users.length} users, creating welcome notifications...`);
      
      for (const user of users) {
        // Check if user already has notifications
        const { data: existingNotifications } = await supabase
          .from('user_notifications')
          .select('id')
          .eq('user_id', user.id)
          .limit(1);
        
        if (!existingNotifications || existingNotifications.length === 0) {
          // Create welcome notification
          const { error: notificationError } = await supabase
            .from('user_notifications')
            .insert({
              user_id: user.id,
              notification_type: 'system',
              title: '🎉 Welcome to Aureus Africa!',
              message: `Welcome ${user.username || user.email}! We're excited to have you join our gold mining community.\n\n🚀 Getting Started:\n1. Complete your KYC verification\n2. Explore the current share offerings\n3. Set up your referral links to earn commissions\n4. Join our Telegram community for updates\n\n💡 Need help? Check out our FAQ or contact support.\n\nHappy investing!`,
              metadata: { 
                welcome: true, 
                created_by: 'system_setup',
                user_email: user.email 
              },
              priority: 'normal'
            });
          
          if (notificationError) {
            console.error(`❌ Error creating notification for user ${user.id}:`, notificationError.message);
          } else {
            console.log(`✅ Created welcome notification for user ${user.id} (${user.username || user.email})`);
          }
        } else {
          console.log(`ℹ️ User ${user.id} already has notifications, skipping`);
        }
      }
    } else {
      console.log('ℹ️ No users found for notification creation');
    }
  } catch (error) {
    console.error('❌ Error creating sample notifications:', error.message);
  }
  
  // Step 8: Test the system
  console.log('🧪 Testing notification system...');
  
  try {
    // Test table access
    const { data: testData, error: testError } = await supabase
      .from('user_notifications')
      .select('count(*)')
      .limit(1);
    
    if (testError) {
      console.error('❌ Notification table access test failed:', testError.message);
      return false;
    }
    
    console.log('✅ Notification table is accessible');
    
    // Test summary view
    const { data: summaryTest, error: summaryError } = await supabase
      .from('user_notification_summary')
      .select('*')
      .limit(1);
    
    if (summaryError) {
      console.error('❌ Summary view test failed:', summaryError.message);
      return false;
    }
    
    console.log('✅ Notification summary view working');
    
    return true;
    
  } catch (error) {
    console.error('❌ Notification system testing failed:', error.message);
    return false;
  }
}

// Main execution
if (require.main === module) {
  setupNotificationSystem()
    .then((success) => {
      if (success) {
        console.log('✅ Notification system setup completed successfully!');
        console.log('');
        console.log('📋 Next steps:');
        console.log('1. Users should now see notifications in their dashboard');
        console.log('2. New notifications will be created automatically for payments and commissions');
        console.log('3. Users can mark notifications as read/unread');
        console.log('4. Telegram bot integration will sync notifications');
        process.exit(0);
      } else {
        console.error('❌ Notification system setup failed');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('❌ Setup process failed:', error);
      process.exit(1);
    });
}

module.exports = { setupNotificationSystem };
