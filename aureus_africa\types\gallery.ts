// Gallery Management Types

export interface GalleryCategory {
  id: string;
  name: string;
  description?: string;
  slug: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  updated_by: string;
}

export interface GalleryImage {
  id: string;
  title: string;
  description?: string;
  image_url: string;
  thumbnail_url?: string;
  category_id?: string;
  alt_text?: string;
  file_size?: number;
  width?: number;
  height?: number;
  display_order: number;
  is_featured: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  updated_by: string;
  // Joined data
  category?: GalleryCategory;
}

export interface GalleryImageWithCategory extends GalleryImage {
  category: GalleryCategory;
}

export interface CreateGalleryImageRequest {
  title: string;
  description?: string;
  image_url: string;
  thumbnail_url?: string;
  category_id?: string;
  alt_text?: string;
  file_size?: number;
  width?: number;
  height?: number;
  display_order?: number;
  is_featured?: boolean;
  is_active?: boolean;
}

export interface UpdateGalleryImageRequest extends Partial<CreateGalleryImageRequest> {
  id: string;
}

export interface CreateGalleryCategoryRequest {
  name: string;
  description?: string;
  slug: string;
  display_order?: number;
  is_active?: boolean;
}

export interface UpdateGalleryCategoryRequest extends Partial<CreateGalleryCategoryRequest> {
  id: string;
}

export interface GalleryFilters {
  category_id?: string;
  is_featured?: boolean;
  is_active?: boolean;
  search?: string;
}

export interface GalleryUploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'complete' | 'error';
  error?: string;
  result?: GalleryImage;
}

export interface ImageUploadResult {
  url: string;
  thumbnail_url?: string;
  width: number;
  height: number;
  file_size: number;
}

// Gallery management hooks return types
export interface UseGalleryImagesResult {
  images: GalleryImage[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createImage: (data: CreateGalleryImageRequest) => Promise<GalleryImage>;
  updateImage: (data: UpdateGalleryImageRequest) => Promise<GalleryImage>;
  deleteImage: (id: string) => Promise<void>;
  uploadImage: (file: File, metadata: Partial<CreateGalleryImageRequest>) => Promise<GalleryImage>;
}

export interface UseGalleryCategoriesResult {
  categories: GalleryCategory[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createCategory: (data: CreateGalleryCategoryRequest) => Promise<GalleryCategory>;
  updateCategory: (data: UpdateGalleryCategoryRequest) => Promise<GalleryCategory>;
  deleteCategory: (id: string) => Promise<void>;
}

// Admin gallery management component props
export interface GalleryManagerProps {
  onImageSelect?: (image: GalleryImage) => void;
  allowMultiSelect?: boolean;
  categoryFilter?: string;
  showUpload?: boolean;
  showCategories?: boolean;
}

export interface ImageUploaderProps {
  onUploadComplete: (images: GalleryImage[]) => void;
  onUploadProgress?: (progress: GalleryUploadProgress[]) => void;
  allowMultiple?: boolean;
  maxFileSize?: number;
  acceptedTypes?: string[];
  defaultCategory?: string;
}

export interface CategoryManagerProps {
  onCategorySelect?: (category: GalleryCategory) => void;
  showCreateForm?: boolean;
  allowEdit?: boolean;
}

// Frontend gallery component props
export interface GalleryDisplayProps {
  categoryFilter?: string;
  showCategories?: boolean;
  showSearch?: boolean;
  itemsPerPage?: number;
  layout?: 'grid' | 'masonry' | 'carousel';
  showFeaturedFirst?: boolean;
}

export interface GalleryGridProps {
  images: GalleryImage[];
  onImageClick?: (image: GalleryImage) => void;
  columns?: number;
  showOverlay?: boolean;
  showCategories?: boolean;
}

export interface GalleryLightboxProps {
  images: GalleryImage[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onNext: () => void;
  onPrevious: () => void;
}

// Utility types
export type GallerySortField = 'created_at' | 'updated_at' | 'display_order' | 'title';
export type GallerySortDirection = 'asc' | 'desc';

export interface GallerySortOptions {
  field: GallerySortField;
  direction: GallerySortDirection;
}

export interface GalleryPaginationOptions {
  page: number;
  limit: number;
  total?: number;
}

export interface GalleryQueryOptions {
  filters?: GalleryFilters;
  sort?: GallerySortOptions;
  pagination?: GalleryPaginationOptions;
}

export interface GalleryQueryResult {
  images: GalleryImage[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
