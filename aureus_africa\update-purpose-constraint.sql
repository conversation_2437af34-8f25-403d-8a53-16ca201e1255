-- Update the purpose constraint in email_verification_codes table
-- to include 'telegram_connection' purpose

-- First, drop the existing constraint
ALTER TABLE email_verification_codes 
DROP CONSTRAINT IF EXISTS email_verification_codes_purpose_check;

-- Add the new constraint with telegram_connection included
ALTER TABLE email_verification_codes 
ADD CONSTRAINT email_verification_codes_purpose_check 
CHECK (purpose IN ('registration', 'account_update', 'withdrawal', 'password_reset', 'telegram_connection'));

-- Verify the constraint was updated
SELECT conname, consrc 
FROM pg_constraint 
WHERE conrelid = 'email_verification_codes'::regclass 
AND conname LIKE '%purpose%';
