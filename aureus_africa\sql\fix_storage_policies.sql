-- Fix Storage Policies for Web App Integration
-- Execute this SQL in your Supabase SQL editor

-- =====================================================
-- 1. ALLOW WEB APP USERS TO UPLOAD TO PROOF BUCKET
-- =====================================================

-- Drop existing restrictive policies if they exist
DROP POLICY IF EXISTS "Authenticated users can upload payment proofs" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload to proof bucket" ON storage.objects;

-- Create policy to allow authenticated users to upload to proof bucket
CREATE POLICY "Allow authenticated users to upload to proof bucket" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'proof' AND 
        auth.role() = 'authenticated'
    );

-- Ensure anyone can view files in proof bucket (for admin review)
CREATE POLICY "Anyone can view proof files" ON storage.objects
    FOR SELECT USING (bucket_id = 'proof');

-- Allow users to update their own uploaded files
CREATE POLICY "Users can update their proof files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'proof' AND 
        auth.role() = 'authenticated'
    );

-- =====================================================
-- 2. ENSURE MARKETING MATERIALS BUCKET POLICIES
-- =====================================================

-- Check if marketing-materials bucket exists, if not create it
INSERT INTO storage.buckets (id, name, owner, public, file_size_limit, allowed_mime_types)
VALUES (
    'marketing-materials',
    'marketing-materials',
    NULL,
    true,
    52428800, -- 50MB limit
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf', 'video/mp4', 'video/webm']
)
ON CONFLICT (id) DO NOTHING;

-- Policy for marketing-materials bucket (admins can upload, everyone can view)
CREATE POLICY "Admins can upload marketing materials" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'marketing-materials' AND 
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE auth_user_id = auth.uid() AND is_admin = true
        )
    );

CREATE POLICY "Anyone can view marketing materials" ON storage.objects
    FOR SELECT USING (bucket_id = 'marketing-materials');

-- Policy for admins to manage marketing materials
CREATE POLICY "Admins can manage marketing materials" ON storage.objects
    FOR ALL USING (
        bucket_id = 'marketing-materials' AND 
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE auth_user_id = auth.uid() AND is_admin = true
        )
    );

-- =====================================================
-- 3. SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Storage policies fixed successfully!';
    RAISE NOTICE '📁 Proof bucket: Authenticated users can now upload';
    RAISE NOTICE '📁 Marketing materials bucket: Created with admin policies';
    RAISE NOTICE '🔒 All storage policies configured correctly';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 File upload functionality should now work!';
END $$;
