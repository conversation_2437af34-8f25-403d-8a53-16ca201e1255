#!/usr/bin/env node

/**
 * FIX CORRECT USER PASSWORD
 * 
 * This script fixes the password for the correct user record that is linked
 * to the Telegram user having login issues.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

const fixCorrectUserPassword = async () => {
  try {
    console.log('🔧 Fixing password for the correct user record...\n');

    const telegramId = '1393852532';
    const newPassword = 'Gunst0n5o0!@#';

    // Step 1: Find the Telegram user and their linked user
    console.log('📋 Step 1: Finding Telegram user and linked user...');
    
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single();

    if (telegramError) {
      console.error('❌ Error finding telegram user:', telegramError);
      return;
    }

    if (!telegramUser.user_id) {
      console.error('❌ Telegram user is not linked to any user in users table');
      return;
    }

    console.log(`✅ Found Telegram user linked to user ID: ${telegramUser.user_id}`);

    // Get the linked user details
    const { data: linkedUser, error: linkedError } = await supabase
      .from('users')
      .select('*')
      .eq('id', telegramUser.user_id)
      .single();

    if (linkedError) {
      console.error('❌ Error finding linked user:', linkedError);
      return;
    }

    console.log('📋 Current linked user details:');
    console.log(`   ID: ${linkedUser.id}`);
    console.log(`   Username: ${linkedUser.username}`);
    console.log(`   Email: ${linkedUser.email}`);
    console.log(`   Current password hash: ${linkedUser.password_hash.substring(0, 20)}...`);

    // Step 2: Hash the new password
    console.log(`\n📋 Step 2: Hashing new password: "${newPassword}"`);
    const hashedPassword = await hashPassword(newPassword);
    console.log('✅ Password hashed successfully');

    // Step 3: Update the correct user record
    console.log('\n📋 Step 3: Updating the correct user record...');
    
    const { data: updateData, error: updateError } = await supabase
      .from('users')
      .update({
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      })
      .eq('id', linkedUser.id)
      .select();

    if (updateError) {
      console.error('❌ Error updating user password:', updateError);
      return;
    }

    console.log('✅ User password updated successfully');

    // Step 4: Verify the fix
    console.log('\n📋 Step 4: Verifying the fix...');
    
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('id, username, email, password_hash')
      .eq('id', linkedUser.id)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying fix:', verifyError);
      return;
    }

    console.log('📋 Updated user details:');
    console.log(`   ID: ${verifyUser.id}`);
    console.log(`   Username: ${verifyUser.username}`);
    console.log(`   Email: ${verifyUser.email}`);
    console.log(`   New password hash: ${verifyUser.password_hash.substring(0, 20)}...`);

    // Test password verification
    const isValid = await bcrypt.compare(newPassword, verifyUser.password_hash);
    console.log(`   Password verification: ${isValid ? '✅ SUCCESS' : '❌ FAILED'}`);

    if (isValid) {
      console.log('\n🎉 FIX COMPLETED SUCCESSFULLY!');
      console.log('The user should now be able to login with:');
      console.log(`   Telegram ID: ${telegramId}`);
      console.log(`   Password: ${newPassword}`);
      console.log('\n📋 What was fixed:');
      console.log('   ✅ Updated the correct user record (ID: ' + linkedUser.id + ')');
      console.log('   ✅ Set the password hash to match the admin-specified password');
      console.log('   ✅ Verified password verification works');
      console.log('\n🔍 The issue was that the admin password change updated the wrong user record.');
      console.log('   Now the correct linked user has the right password.');
    } else {
      console.log('\n❌ Password verification still failed. Something went wrong.');
    }

  } catch (error) {
    console.error('❌ Fix failed:', error);
  }
};

fixCorrectUserPassword();
